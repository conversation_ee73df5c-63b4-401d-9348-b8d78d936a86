<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class Application extends Model
{
    use HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'fullname',
        'gender',
        'marriage',
        'date_of_birth',
        'nationality',
        'id_number',
        'place_of_birth',
        'email',
        'phone',
        'residential_address',
        'postal_address',
        'education_level',
        'institution_name',
        'course_studied',
        'graduation_year',
        'additional_qualifications',
        'certificate_path',
        'current_employer',
        'job_title',
        'employment_start_date',
        'employment_end_date',
        'job_responsibilities',
        'previous_experience',
        'emergency_contact_name',
        'emergency_contact_relationship',
        'emergency_contact_phone',
        'emergency_contact_address',
        'next_of_kin_name',
        'next_of_kin_phone',
        'declaration',
        'declaration_date',
        'applicant_signature',
        'signature',
        'status',
        'stage',
        'user_id',
        'submission_date',
        'ip_address',
        'user_agent',
        'last_activity',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id_number', // Hide sensitive ID numbers in API responses
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'employment_start_date' => 'date',
        'employment_end_date' => 'date',
        'declaration_date' => 'date',
        'submission_date' => 'datetime',
        'last_activity' => 'datetime',
        'declaration' => 'boolean',
        'graduation_year' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be guarded against mass assignment.
     */
    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // Removed duplicate casts - already defined above

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function educationalBackgrounds()
    {
        return $this->hasMany(EducationalBackground::class);
    }

    public function workExperiences()
    {
        return $this->hasMany(WorkExperience::class);
    }

    public function emergencyContacts()
    {
        return $this->hasMany(EmergencyContact::class);
    }

    /**
     * Scope to get applications for the current authenticated user only
     */
    public function scopeForCurrentUser($query)
    {
        return $query->where('user_id', auth()->id());
    }

    /**
     * Check if the application belongs to the current user
     */
    public function belongsToCurrentUser(): bool
    {
        return $this->user_id === auth()->id();
    }

    /**
     * Get masked ID number for display (security)
     */
    public function getMaskedIdNumberAttribute(): string
    {
        if (!$this->id_number) return '';

        $length = strlen($this->id_number);
        if ($length <= 4) return str_repeat('*', $length);

        return substr($this->id_number, 0, 2) . str_repeat('*', $length - 4) . substr($this->id_number, -2);
    }

    /**
     * Boot method to add model events for security logging
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically set user_id when creating
        static::creating(function ($application) {
            if (!$application->user_id && auth()->check()) {
                $application->user_id = auth()->id();
            }
        });

        // Log application updates for audit trail
        static::updated(function ($application) {
            \Log::info('Application updated', [
                'application_id' => $application->id,
                'user_id' => $application->user_id,
                'stage' => $application->stage,
                'status' => $application->status,
                'updated_by' => auth()->id(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);
        });
    }
}
