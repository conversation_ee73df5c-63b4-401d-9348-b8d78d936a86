<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Application extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'fullname',
        'gender',
        'marriage',
        'date_of_birth',
        'nationality',
        'id_number',
        'place_of_birth',
        'email',
        'phone',
        'residential_address',
        'postal_address',
        'education_level',
        'institution_name',
        'course_studied',
        'graduation_year',
        'additional_qualifications',
        'current_employer',
        'job_title',
        'employment_start_date',
        'employment_end_date',
        'job_responsibilities',
        'previous_experience',
        'emergency_contact_name',
        'emergency_contact_relationship',
        'emergency_contact_phone',
        'emergency_contact_address',
        'next_of_kin_name',
        'next_of_kin_phone',
        'declaration',
        'declaration_date',
        'applicant_signature',
        'signature',
        'status',
        'stage',
        'user_id',
        'submission_date',
    ];

    protected $casts = [
        'submission_date' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function educationalBackgrounds()
    {
        return $this->hasMany(EducationalBackground::class);
    }

    public function workExperiences()
    {
        return $this->hasMany(WorkExperience::class);
    }

    public function emergencyContacts()
    {
        return $this->hasMany(EmergencyContact::class);
    }
}
