<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class PositionApply extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'application_id',
        'job_title',
        'reference_number',
        'posting_region',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }
}
