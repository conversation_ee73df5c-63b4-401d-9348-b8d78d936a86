<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EducationalBackground extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'application_id',
        'institution_name',
        'level',
        'field_of_study',
        'completed_year',
        'certificate_url',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

}
