<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WorkExperience extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'application_id',
        'organization_name',
        'position',
        'start_date',
        'end_date',
        'responsibilities',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }
}
