<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmergencyContact extends Model
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'application_id',
        'lead_id',
        'next_of_kin_name',
        'next_of_kin_phone',
        'next_of_kin_relationship',
        'next_of_kin_address',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function lead()
    {
        return $this->belongsTo(Lead::class);
    }

}
