<?php

namespace App\Livewire\Applicant;

use App\Models\Application;
use Livewire\Component;

class Application extends Component
{
    public $userId;
    public $application;

    public function mount()
    {
        $this->userId = auth()->user()->id;
        $this->application = Application::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();
    }

    public function render()
    {
        return view('livewire.applicant.application')->with('application', $this->application);
    }

}
