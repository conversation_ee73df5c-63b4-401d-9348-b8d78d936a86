<?php

namespace App\Livewire\Applicant;

use App\Models\Application as ApplicationModel;
use Livewire\Component;

class Application extends Component
{
    public $userId;
    public $application;

    public function mount($application = null)
    {
        // If application is passed from the controller, use it
        if ($application) {
            $this->application = $application;
        } else {
            // Otherwise, try to get the current user's application
            $this->userId = auth()->user()->id;
            $this->application = ApplicationModel::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();
        }

        // If still no application, create a default object to prevent errors
        if (!$this->application) {
            $this->application = (object) [
                'id' => null,
                'stage' => 'section1',
                'status' => 'draft'
            ];
        }
    }

    public function render()
    {
        return view('livewire.applicant.application')->with('application', $this->application);
    }

}
