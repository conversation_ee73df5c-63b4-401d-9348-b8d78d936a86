<?php

namespace App\Livewire\Applicant;

use App\Models\Application as ApplicationModel;
use Livewire\Component;

class Application extends Component
{
    public $userId;
    public $application;
    public $currentStage;

    // Form fields for different sections
    public $fullname = '';
    public $gender = '';
    public $marriage = '';
    public $date_of_birth = '';
    public $nationality = '';
    public $id_number = '';
    public $place_of_birth = '';
    public $email = '';
    public $phone = '';
    public $residential_address = '';
    public $postal_address = '';

    public function mount($application = null)
    {
        $this->userId = auth()->user()->id;

        // If application is passed from the controller, use it
        if ($application) {
            $this->application = $application;
            $this->currentStage = $application->stage ?? 'section1';
            $this->loadApplicationData();
        } else {
            // Otherwise, try to get the current user's application
            $this->application = ApplicationModel::where('user_id', $this->userId)->first();

            if ($this->application) {
                $this->currentStage = $this->application->stage;
                $this->loadApplicationData();
            } else {
                // Create new application
                $this->application = ApplicationModel::create([
                    'user_id' => $this->userId,
                    'stage' => 'section1',
                    'status' => 'draft'
                ]);
                $this->currentStage = 'section1';
            }
        }
    }

    public function loadApplicationData()
    {
        if ($this->application) {
            $this->fullname = $this->application->fullname ?? '';
            $this->gender = $this->application->gender ?? '';
            $this->marriage = $this->application->marriage ?? '';
            $this->date_of_birth = $this->application->date_of_birth ?? '';
            $this->nationality = $this->application->nationality ?? '';
            $this->id_number = $this->application->id_number ?? '';
            $this->place_of_birth = $this->application->place_of_birth ?? '';
            $this->email = $this->application->email ?? '';
            $this->phone = $this->application->phone ?? '';
            $this->residential_address = $this->application->residential_address ?? '';
            $this->postal_address = $this->application->postal_address ?? '';
        }
    }

    public function saveSection()
    {
        $this->validate($this->getRulesForCurrentStage());

        $this->application->update([
            'fullname' => $this->fullname,
            'gender' => $this->gender,
            'marriage' => $this->marriage,
            'date_of_birth' => $this->date_of_birth,
            'nationality' => $this->nationality,
            'id_number' => $this->id_number,
            'place_of_birth' => $this->place_of_birth,
            'email' => $this->email,
            'phone' => $this->phone,
            'residential_address' => $this->residential_address,
            'postal_address' => $this->postal_address,
            'stage' => $this->currentStage,
            'status' => 'draft'
        ]);

        session()->flash('message', 'Section saved successfully!');
    }

    public function nextSection()
    {
        $this->saveSection();

        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex < count($stages) - 1) {
            $this->currentStage = $stages[$currentIndex + 1];
            $this->application->update(['stage' => $this->currentStage]);
        }
    }

    public function previousSection()
    {
        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex > 0) {
            $this->currentStage = $stages[$currentIndex - 1];
            $this->application->update(['stage' => $this->currentStage]);
        }
    }

    public function getRulesForCurrentStage()
    {
        switch ($this->currentStage) {
            case 'section1':
                return [
                    'fullname' => 'required|string|max:255',
                    'gender' => 'required|in:male,female,other',
                    'marriage' => 'required|in:single,married,divorced,widowed',
                    'date_of_birth' => 'required|date',
                    'nationality' => 'required|string|max:255',
                    'id_number' => 'required|string|max:255',
                    'place_of_birth' => 'required|string|max:255',
                ];
            case 'section2':
                return [
                    'email' => 'required|email|max:255',
                    'phone' => 'required|string|max:20',
                    'residential_address' => 'required|string',
                    'postal_address' => 'nullable|string',
                ];
            default:
                return [];
        }
    }

    public function render()
    {
        return view('livewire.applicant.application');
    }
}
