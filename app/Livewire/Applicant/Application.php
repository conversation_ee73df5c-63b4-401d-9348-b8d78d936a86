<?php

namespace App\Livewire\Applicant;

use App\Models\Application as ApplicationModel;
use Livewire\Component;

class Application extends Component
{
    public $userId;
    public $application;
    public $currentStage;

    // Form fields for different sections
    public $fullname = '';
    public $gender = '';
    public $marriage = '';
    public $date_of_birth = '';
    public $nationality = '';
    public $id_number = '';
    public $place_of_birth = '';
    public $email = '';
    public $phone = '';
    public $residential_address = '';
    public $postal_address = '';

    // Section 3 - Education fields
    public $education_level = '';
    public $institution_name = '';
    public $course_studied = '';
    public $graduation_year = '';
    public $additional_qualifications = '';

    // Section 4 - Work Experience fields
    public $current_employer = '';
    public $job_title = '';
    public $employment_start_date = '';
    public $employment_end_date = '';
    public $job_responsibilities = '';
    public $previous_experience = '';

    // Section 5 - Emergency Contact fields
    public $emergency_contact_name = '';
    public $emergency_contact_relationship = '';
    public $emergency_contact_phone = '';
    public $emergency_contact_address = '';
    public $next_of_kin_name = '';
    public $next_of_kin_phone = '';

    // Section 6 - Declaration fields
    public $declaration = '';
    public $declaration_date = '';
    public $applicant_signature = '';

    public function mount($application = null)
    {
        $this->userId = auth()->user()->id;

        // If application is passed from the controller, use it
        if ($application) {
            $this->application = $application;
            $this->currentStage = $application->stage ?? 'section1';
            $this->loadApplicationData();
        } else {
            // Otherwise, try to get the current user's application
            $this->application = ApplicationModel::where('user_id', $this->userId)->first();

            if ($this->application) {
                $this->currentStage = $this->application->stage;
                $this->loadApplicationData();
            } else {
                // Don't create application yet - wait for user to fill required fields
                $this->application = null;
                $this->currentStage = 'section1';
            }
        }
    }

    public function loadApplicationData()
    {
        if ($this->application) {
            // Section 1 & 2 fields
            $this->fullname = $this->application->fullname ?? '';
            $this->gender = $this->application->gender ?? '';
            $this->marriage = $this->application->marriage ?? '';
            $this->date_of_birth = $this->application->date_of_birth ?? '';
            $this->nationality = $this->application->nationality ?? '';
            $this->id_number = $this->application->id_number ?? '';
            $this->place_of_birth = $this->application->place_of_birth ?? '';
            $this->email = $this->application->email ?? '';
            $this->phone = $this->application->phone ?? '';
            $this->residential_address = $this->application->residential_address ?? '';
            $this->postal_address = $this->application->postal_address ?? '';

            // Section 3 - Education fields
            $this->education_level = $this->application->education_level ?? '';
            $this->institution_name = $this->application->institution_name ?? '';
            $this->course_studied = $this->application->course_studied ?? '';
            $this->graduation_year = $this->application->graduation_year ?? '';
            $this->additional_qualifications = $this->application->additional_qualifications ?? '';

            // Section 4 - Work Experience fields
            $this->current_employer = $this->application->current_employer ?? '';
            $this->job_title = $this->application->job_title ?? '';
            $this->employment_start_date = $this->application->employment_start_date ?? '';
            $this->employment_end_date = $this->application->employment_end_date ?? '';
            $this->job_responsibilities = $this->application->job_responsibilities ?? '';
            $this->previous_experience = $this->application->previous_experience ?? '';

            // Section 5 - Emergency Contact fields
            $this->emergency_contact_name = $this->application->emergency_contact_name ?? '';
            $this->emergency_contact_relationship = $this->application->emergency_contact_relationship ?? '';
            $this->emergency_contact_phone = $this->application->emergency_contact_phone ?? '';
            $this->emergency_contact_address = $this->application->emergency_contact_address ?? '';
            $this->next_of_kin_name = $this->application->next_of_kin_name ?? '';
            $this->next_of_kin_phone = $this->application->next_of_kin_phone ?? '';

            // Section 6 - Declaration fields
            $this->declaration = $this->application->declaration ?? '';
            $this->declaration_date = $this->application->declaration_date ?? '';
            $this->applicant_signature = $this->application->applicant_signature ?? '';
        }
    }

    public function saveSection()
    {
        $this->validate($this->getRulesForCurrentStage());

        $updateData = [
            'fullname' => $this->fullname,
            'gender' => $this->gender,
            'marriage' => $this->marriage,
            'date_of_birth' => $this->date_of_birth,
            'nationality' => $this->nationality,
            'id_number' => $this->id_number,
            'place_of_birth' => $this->place_of_birth,
            'email' => $this->email,
            'phone' => $this->phone,
            'residential_address' => $this->residential_address,
            'postal_address' => $this->postal_address,
            'education_level' => $this->education_level,
            'institution_name' => $this->institution_name,
            'course_studied' => $this->course_studied,
            'graduation_year' => $this->graduation_year,
            'additional_qualifications' => $this->additional_qualifications,
            'current_employer' => $this->current_employer,
            'job_title' => $this->job_title,
            'employment_start_date' => $this->employment_start_date,
            'employment_end_date' => $this->employment_end_date,
            'job_responsibilities' => $this->job_responsibilities,
            'previous_experience' => $this->previous_experience,
            'emergency_contact_name' => $this->emergency_contact_name,
            'emergency_contact_relationship' => $this->emergency_contact_relationship,
            'emergency_contact_phone' => $this->emergency_contact_phone,
            'emergency_contact_address' => $this->emergency_contact_address,
            'next_of_kin_name' => $this->next_of_kin_name,
            'next_of_kin_phone' => $this->next_of_kin_phone,
            'declaration' => $this->declaration,
            'declaration_date' => $this->declaration_date,
            'applicant_signature' => $this->applicant_signature,
            'stage' => $this->currentStage,
            'status' => 'draft'
        ];

        // If no application exists, create one
        if (!$this->application) {
            $updateData['user_id'] = $this->userId;
            $this->application = ApplicationModel::create($updateData);
        } else {
            // Update existing application
            $this->application->update($updateData);
        }

        session()->flash('message', 'Section saved successfully!');
    }

    public function nextSection()
    {
        $this->saveSection();

        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex < count($stages) - 1) {
            $this->currentStage = $stages[$currentIndex + 1];
            $this->application->update(['stage' => $this->currentStage]);
        } elseif ($this->currentStage == 'section6') {
            // If on the last section, offer to submit
            $this->submitApplication();
        }
    }

    public function submitApplication()
    {
        // Validate all sections before submission
        $this->validate($this->getRulesForCurrentStage());

        $this->application->update([
            'status' => 'submitted',
            'submission_date' => now(),
            'declaration_date' => $this->declaration_date ?: now()->format('Y-m-d')
        ]);

        session()->flash('message', 'Application submitted successfully! You will be contacted regarding the next steps.');

        // Redirect to dashboard or confirmation page
        return redirect()->route('applicant.dashboard');
    }

    public function previousSection()
    {
        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex > 0) {
            $this->currentStage = $stages[$currentIndex - 1];
            $this->application->update(['stage' => $this->currentStage]);
        }
    }

    public function getRulesForCurrentStage()
    {
        switch ($this->currentStage) {
            case 'section1':
                return [
                    'fullname' => 'required|string|max:255',
                    'gender' => 'required|in:male,female,other',
                    'marriage' => 'required|in:single,married,divorced,widowed',
                    'date_of_birth' => 'required|date',
                    'nationality' => 'required|string|max:255',
                    'id_number' => 'required|string|max:255',
                    'place_of_birth' => 'required|string|max:255',
                ];
            case 'section2':
                return [
                    'email' => 'required|email|max:255',
                    'phone' => 'required|string|max:20',
                    'residential_address' => 'required|string',
                    'postal_address' => 'nullable|string',
                ];
            case 'section3':
                return [
                    'education_level' => 'required|string|max:255',
                    'institution_name' => 'required|string|max:255',
                    'course_studied' => 'required|string|max:255',
                    'graduation_year' => 'required|integer|min:1950|max:' . date('Y'),
                    'additional_qualifications' => 'nullable|string',
                ];
            case 'section4':
                return [
                    'current_employer' => 'nullable|string|max:255',
                    'job_title' => 'nullable|string|max:255',
                    'employment_start_date' => 'nullable|date',
                    'employment_end_date' => 'nullable|date|after_or_equal:employment_start_date',
                    'job_responsibilities' => 'nullable|string',
                    'previous_experience' => 'nullable|string',
                ];
            case 'section5':
                return [
                    'emergency_contact_name' => 'required|string|max:255',
                    'emergency_contact_relationship' => 'required|string|max:255',
                    'emergency_contact_phone' => 'required|string|max:20',
                    'emergency_contact_address' => 'required|string',
                    'next_of_kin_name' => 'required|string|max:255',
                    'next_of_kin_phone' => 'required|string|max:20',
                ];
            case 'section6':
                return [
                    'declaration' => 'required|accepted',
                    'declaration_date' => 'required|date',
                    'applicant_signature' => 'required|string|max:255',
                ];
            default:
                return [];
        }
    }

    public function render()
    {
        return view('livewire.applicant.application');
    }
}
