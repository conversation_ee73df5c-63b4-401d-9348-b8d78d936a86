<?php

namespace App\Livewire\Applicant;

use App\Models\Application as ApplicationModel;
use Livewire\Component;
use Livewire\WithFileUploads;

class Application extends Component
{
    use WithFileUploads;
    public $userId;
    public $application;
    public $currentStage;

    // Form fields for different sections
    public $fullname = '';
    public $gender = '';
    public $marriage = '';
    public $date_of_birth = '';
    public $nationality = '';
    public $id_number = '';
    public $place_of_birth = '';
    public $email = '';
    public $phone = '';
    public $residential_address = '';
    public $postal_address = '';

    // Section 3 - Education fields (multiple entries)
    public $educationalBackgrounds = [];
    public $additional_qualifications = '';

    // Section 4 - Work Experience fields (multiple entries)
    public $workExperiences = [];
    public $previous_experience = '';

    // Section 5 - Emergency Contact fields
    public $emergency_contact_name = '';
    public $emergency_contact_relationship = '';
    public $emergency_contact_phone = '';
    public $emergency_contact_address = '';
    public $next_of_kin_name = '';
    public $next_of_kin_phone = '';

    // Section 6 - Declaration fields
    public $declaration = '';
    public $declaration_date = '';
    public $applicant_signature = '';

    public function mount($application = null)
    {
        // Security check: Ensure user is authenticated
        if (!auth()->check()) {
            abort(401, 'Unauthorized access');
        }

        $this->userId = auth()->user()->id;

        // Initialize arrays for multiple entries
        $this->initializeArrays();

        // If application is passed from the controller, use it
        if ($application) {
            // Security check: Ensure application belongs to current user
            if ($application->user_id !== $this->userId) {
                abort(403, 'Access denied');
            }

            $this->application = $application;
            $this->currentStage = $application->stage ?? 'section1';
            $this->loadApplicationData();
        } else {
            // Otherwise, try to get the current user's application
            $this->application = ApplicationModel::where('user_id', $this->userId)->first();

            if ($this->application) {
                $this->currentStage = $this->application->stage;
                $this->loadApplicationData();
            } else {
                // Don't create application yet - wait for user to fill required fields
                $this->application = null;
                $this->currentStage = 'section1';
            }
        }

        // Track user activity for security
        $this->trackUserActivity();
    }

    /**
     * Initialize arrays for multiple entries
     */
    private function initializeArrays()
    {
        // Initialize with one empty educational background
        if (empty($this->educationalBackgrounds)) {
            $this->educationalBackgrounds = [
                [
                    'education_level' => '',
                    'institution_name' => '',
                    'course_studied' => '',
                    'graduation_year' => '',
                    'certificate' => null,
                    'certificate_path' => '',
                ]
            ];
        }

        // Initialize with one empty work experience
        if (empty($this->workExperiences)) {
            $this->workExperiences = [
                [
                    'employer' => '',
                    'job_title' => '',
                    'start_date' => '',
                    'end_date' => '',
                    'responsibilities' => '',
                    'is_current' => false,
                ]
            ];
        }
    }

    /**
     * Track user activity for security monitoring
     */
    private function trackUserActivity()
    {
        if ($this->application) {
            $this->application->update([
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'last_activity' => now(),
            ]);
        }
    }

    /**
     * Add a new educational background entry
     */
    public function addEducationalBackground()
    {
        $this->educationalBackgrounds[] = [
            'education_level' => '',
            'institution_name' => '',
            'course_studied' => '',
            'graduation_year' => '',
            'certificate' => null,
            'certificate_path' => '',
        ];
    }

    /**
     * Remove an educational background entry
     */
    public function removeEducationalBackground($index)
    {
        if (count($this->educationalBackgrounds) > 1) {
            unset($this->educationalBackgrounds[$index]);
            $this->educationalBackgrounds = array_values($this->educationalBackgrounds);
        }
    }

    /**
     * Add a new work experience entry
     */
    public function addWorkExperience()
    {
        $this->workExperiences[] = [
            'employer' => '',
            'job_title' => '',
            'start_date' => '',
            'end_date' => '',
            'responsibilities' => '',
            'is_current' => false,
        ];
    }

    /**
     * Remove a work experience entry
     */
    public function removeWorkExperience($index)
    {
        if (count($this->workExperiences) > 1) {
            unset($this->workExperiences[$index]);
            $this->workExperiences = array_values($this->workExperiences);
        }
    }

    /**
     * Auto-set declaration date when declaration is checked
     */
    public function updatedDeclaration($value)
    {
        if ($value && empty($this->declaration_date)) {
            $this->declaration_date = now()->format('Y-m-d');
        }
    }

    /**
     * Handle work experience current job checkbox
     */
    public function updatedWorkExperiences($value, $key)
    {
        // If the is_current checkbox is checked, clear the end_date
        if (str_contains($key, '.is_current')) {
            $index = explode('.', $key)[0];
            if ($this->workExperiences[$index]['is_current']) {
                $this->workExperiences[$index]['end_date'] = '';
            }
        }
    }

    /**
     * Handle certificate upload for educational backgrounds
     */
    public function updatedEducationalBackgrounds($value, $key)
    {
        // Handle certificate uploads
        if (str_contains($key, '.certificate')) {
            $index = explode('.', $key)[0];
            $certificate = $this->educationalBackgrounds[$index]['certificate'];

            if ($certificate) {
                // Validate the uploaded file
                $this->validate([
                    "educationalBackgrounds.{$index}.certificate" => 'file|mimes:pdf,jpg,jpeg,png|max:2048'
                ]);

                // Store the file temporarily (will be moved to permanent location on save)
                $this->educationalBackgrounds[$index]['certificate_path'] = $certificate->getClientOriginalName();
            }
        }
    }

    /**
     * Remove uploaded certificate for educational background
     */
    public function removeCertificate($index)
    {
        if (isset($this->educationalBackgrounds[$index])) {
            // Delete the file if it exists
            if (!empty($this->educationalBackgrounds[$index]['certificate_path'])) {
                $filePath = 'public/certificates/education/' . $this->educationalBackgrounds[$index]['certificate_path'];
                if (\Storage::exists($filePath)) {
                    \Storage::delete($filePath);
                }
            }

            // Clear the certificate fields
            $this->educationalBackgrounds[$index]['certificate'] = null;
            $this->educationalBackgrounds[$index]['certificate_path'] = '';
        }
    }

    /**
     * Load educational backgrounds from database
     */
    private function loadEducationalBackgrounds()
    {
        if ($this->application && $this->application->educationalBackgrounds) {
            $this->educationalBackgrounds = $this->application->educationalBackgrounds->map(function ($education) {
                return [
                    'id' => $education->id,
                    'education_level' => $education->education_level,
                    'institution_name' => $education->institution_name,
                    'course_studied' => $education->course_studied,
                    'graduation_year' => $education->graduation_year,
                    'certificate' => null,
                    'certificate_path' => $education->certificate_path ?? '',
                ];
            })->toArray();
        }

        // Ensure at least one entry exists
        if (empty($this->educationalBackgrounds)) {
            $this->initializeArrays();
        }
    }

    /**
     * Load work experiences from database
     */
    private function loadWorkExperiences()
    {
        if ($this->application && $this->application->workExperiences) {
            $this->workExperiences = $this->application->workExperiences->map(function ($work) {
                return [
                    'id' => $work->id,
                    'employer' => $work->employer,
                    'job_title' => $work->job_title,
                    'start_date' => $work->start_date ? (string) $work->start_date : '',
                    'end_date' => $work->end_date ? (string) $work->end_date : '',
                    'responsibilities' => $work->responsibilities,
                    'is_current' => $work->is_current,
                ];
            })->toArray();
        }

        // Ensure at least one entry exists
        if (empty($this->workExperiences)) {
            $this->initializeArrays();
        }
    }

    /**
     * Save educational backgrounds to database
     */
    private function saveEducationalBackgrounds()
    {
        if (!$this->application) return;

        // Handle certificate uploads and save paths
        foreach ($this->educationalBackgrounds as $index => $education) {
            if (isset($education['certificate']) && $education['certificate']) {
                // Store the certificate file
                $certificatePath = $education['certificate']->store('certificates/education', 'public');
                $this->educationalBackgrounds[$index]['certificate_path'] = $certificatePath;
            }
        }

        // For now, save the first educational background to the main application table
        // Later we can create a separate table for multiple entries
        if (!empty($this->educationalBackgrounds[0])) {
            $firstEducation = $this->educationalBackgrounds[0];
            $this->application->update([
                'education_level' => $firstEducation['education_level'] ?? '',
                'institution_name' => $this->sanitizeString($firstEducation['institution_name'] ?? ''),
                'course_studied' => $this->sanitizeString($firstEducation['course_studied'] ?? ''),
                'graduation_year' => $firstEducation['graduation_year'] ? (int) $firstEducation['graduation_year'] : null,
                'certificate_path' => $firstEducation['certificate_path'] ?? null,
            ]);
        }

        // TODO: Save additional educational backgrounds to a separate table
        // This would require creating an EducationalBackground model and migration
    }

    /**
     * Save work experiences to database
     */
    private function saveWorkExperiences()
    {
        if (!$this->application) return;

        // For now, save the first work experience to the main application table
        // Later we can create a separate table for multiple entries
        if (!empty($this->workExperiences[0])) {
            $firstWork = $this->workExperiences[0];
            $this->application->update([
                'current_employer' => $this->sanitizeString($firstWork['employer'] ?? ''),
                'job_title' => $this->sanitizeString($firstWork['job_title'] ?? ''),
                'employment_start_date' => !empty($firstWork['start_date']) ? $firstWork['start_date'] : null,
                'employment_end_date' => $firstWork['is_current'] ? null : (!empty($firstWork['end_date']) ? $firstWork['end_date'] : null),
                'job_responsibilities' => $this->sanitizeText($firstWork['responsibilities'] ?? ''),
            ]);
        }

        // TODO: Save additional work experiences to a separate table
        // This would require creating a WorkExperience model and migration
    }

    public function loadApplicationData()
    {
        if ($this->application) {
            // Section 1 & 2 fields
            $this->fullname = $this->application->fullname ?? '';
            $this->gender = $this->application->gender ?? '';
            $this->marriage = $this->application->marriage ?? '';
            $this->date_of_birth = $this->application->date_of_birth
                ? (string) $this->application->date_of_birth
                : '';
            $this->nationality = $this->application->nationality ?? '';
            $this->id_number = $this->application->id_number ?? '';
            $this->place_of_birth = $this->application->place_of_birth ?? '';
            $this->email = $this->application->email ?? '';
            $this->phone = $this->application->phone ?? '';
            $this->residential_address = $this->application->residential_address ?? '';
            $this->postal_address = $this->application->postal_address ?? '';

            // Section 3 - Load educational backgrounds from related models
            $this->loadEducationalBackgrounds();
            $this->additional_qualifications = $this->application->additional_qualifications ?? '';

            // Section 4 - Load work experiences from related models
            $this->loadWorkExperiences();
            $this->previous_experience = $this->application->previous_experience ?? '';

            // Section 5 - Emergency Contact fields
            $this->emergency_contact_name = $this->application->emergency_contact_name ?? '';
            $this->emergency_contact_relationship = $this->application->emergency_contact_relationship ?? '';
            $this->emergency_contact_phone = $this->application->emergency_contact_phone ?? '';
            $this->emergency_contact_address = $this->application->emergency_contact_address ?? '';
            $this->next_of_kin_name = $this->application->next_of_kin_name ?? '';
            $this->next_of_kin_phone = $this->application->next_of_kin_phone ?? '';

            // Section 6 - Declaration fields
            $this->declaration = $this->application->declaration ?? '';
            $this->declaration_date = $this->application->declaration_date
                ? (string) $this->application->declaration_date
                : '';
            $this->applicant_signature = $this->application->applicant_signature ?? '';
        }
    }

    public function saveSection()
    {
        // Sanitize all input data before validation
        $this->sanitizeInputs();

        $this->validate($this->getRulesForCurrentStage());

        $updateData = [
            'fullname' => $this->sanitizeString($this->fullname),
            'gender' => $this->gender,
            'marriage' => $this->marriage,
            'date_of_birth' => $this->date_of_birth ?: null,
            'nationality' => $this->sanitizeString($this->nationality),
            'id_number' => $this->sanitizeAlphanumeric($this->id_number),
            'place_of_birth' => $this->sanitizeString($this->place_of_birth),
            'email' => $this->sanitizeEmail($this->email),
            'phone' => $this->sanitizePhone($this->phone),
            'residential_address' => $this->sanitizeText($this->residential_address),
            'postal_address' => $this->sanitizeText($this->postal_address),
            'additional_qualifications' => $this->sanitizeText($this->additional_qualifications),
            'previous_experience' => $this->sanitizeText($this->previous_experience),
            'emergency_contact_name' => $this->sanitizeString($this->emergency_contact_name),
            'emergency_contact_relationship' => $this->emergency_contact_relationship,
            'emergency_contact_phone' => $this->sanitizePhone($this->emergency_contact_phone),
            'emergency_contact_address' => $this->sanitizeText($this->emergency_contact_address),
            'next_of_kin_name' => $this->sanitizeString($this->next_of_kin_name),
            'next_of_kin_phone' => $this->sanitizePhone($this->next_of_kin_phone),
            'declaration' => $this->declaration ? 1 : 0,
            'declaration_date' => $this->declaration_date ?: null,
            'applicant_signature' => $this->sanitizeString($this->applicant_signature),
            'stage' => $this->currentStage,
            'status' => 'draft'
        ];

        // If no application exists, create one
        if (!$this->application) {
            $updateData['user_id'] = $this->userId;
            $this->application = ApplicationModel::create($updateData);
        } else {
            // Update existing application
            $this->application->update($updateData);
        }

        // Save educational backgrounds and work experiences
        $this->saveEducationalBackgrounds();
        $this->saveWorkExperiences();

        session()->flash('message', 'Section saved successfully!');
    }

    public function nextSection()
    {
        $this->saveSection();

        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex < count($stages) - 1) {
            $this->currentStage = $stages[$currentIndex + 1];
            $this->application->update(['stage' => $this->currentStage]);
        } elseif ($this->currentStage == 'section6') {
            // If on the last section, offer to submit
            $this->submitApplication();
        }
    }

    public function submitApplication()
    {
        // Validate all sections before submission
        $this->validate($this->getRulesForCurrentStage());

        $this->application->update([
            'status' => 'submitted',
            'submission_date' => now(),
            'declaration_date' => $this->declaration_date ?: now()->format('Y-m-d')
        ]);

        session()->flash('message', 'Application submitted successfully! You will be contacted regarding the next steps.');

        // Redirect to dashboard or confirmation page
        return redirect()->route('applicant.dashboard');
    }

    public function previousSection()
    {
        $stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
        $currentIndex = array_search($this->currentStage, $stages);

        if ($currentIndex !== false && $currentIndex > 0) {
            $this->currentStage = $stages[$currentIndex - 1];
            $this->application->update(['stage' => $this->currentStage]);
        }
    }

    public function getRulesForCurrentStage()
    {
        switch ($this->currentStage) {
            case 'section1':
                return [
                    'fullname' => 'required|string|min:2|max:100|regex:/^[a-zA-Z\s\-\.\']+$/u',
                    'gender' => 'required|in:male,female,other',
                    'marriage' => 'required|in:single,married,divorced,widowed',
                    'date_of_birth' => 'required|date|before_or_equal:' . date('Y-m-d', strtotime('-18 years')) . '|after_or_equal:1900-01-01',
                    'nationality' => 'required|string|min:2|max:50|regex:/^[a-zA-Z\s]+$/u',
                    'id_number' => 'required|string|min:5|max:30|regex:/^[a-zA-Z0-9\-]+$/',
                    'place_of_birth' => 'required|string|min:2|max:100|regex:/^[a-zA-Z\s\,\-\.]+$/u',
                ];
            case 'section2':
                return [
                    'email' => 'required|email:rfc,dns|max:255|unique:applications,email,' . ($this->application->id ?? 'NULL'),
                    'phone' => 'required|string|min:10|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
                    'residential_address' => 'required|string|min:10|max:500',
                    'postal_address' => 'nullable|string|max:500',
                ];
            case 'section3':
                return [
                    'educationalBackgrounds.*.education_level' => 'required|in:primary,jhs,shs,diploma,degree,masters,phd,other',
                    'educationalBackgrounds.*.institution_name' => 'required|string|min:2|max:200|regex:/^[a-zA-Z0-9\s\-\.\,\'&]+$/u',
                    'educationalBackgrounds.*.course_studied' => 'required|string|min:2|max:200|regex:/^[a-zA-Z0-9\s\-\.\,\'&\(\)]+$/u',
                    'educationalBackgrounds.*.graduation_year' => 'required|integer|min:1950|max:' . (date('Y') + 1),
                    'educationalBackgrounds.*.certificate' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
                    'additional_qualifications' => 'nullable|string|max:1000',
                ];
            case 'section4':
                return [
                    'workExperiences.*.employer' => 'nullable|string|max:200|regex:/^[a-zA-Z0-9\s\-\.\,\'&\(\)]+$/u',
                    'workExperiences.*.job_title' => 'nullable|string|max:100|regex:/^[a-zA-Z0-9\s\-\.\,\'&\(\)\/]+$/u',
                    'workExperiences.*.start_date' => 'nullable|date|before_or_equal:today|after_or_equal:1950-01-01',
                    'workExperiences.*.end_date' => 'nullable|date|before_or_equal:today',
                    'workExperiences.*.responsibilities' => 'nullable|string|max:2000',
                    'previous_experience' => 'nullable|string|max:2000',
                ];
            case 'section5':
                return [
                    'emergency_contact_name' => 'required|string|min:2|max:100|regex:/^[a-zA-Z\s\-\.\']+$/u',
                    'emergency_contact_relationship' => 'required|in:parent,spouse,sibling,child,friend,relative,other',
                    'emergency_contact_phone' => 'required|string|min:10|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
                    'emergency_contact_address' => 'required|string|min:10|max:500',
                    'next_of_kin_name' => 'required|string|min:2|max:100|regex:/^[a-zA-Z\s\-\.\']+$/u',
                    'next_of_kin_phone' => 'required|string|min:10|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
                ];
            case 'section6':
                return [
                    'declaration' => 'required|accepted',
                    'declaration_date' => 'required|date|before_or_equal:today|after_or_equal:' . date('Y-m-d', strtotime('-30 days')),
                    'applicant_signature' => 'required|string|min:2|max:100|regex:/^[a-zA-Z\s\-\.\']+$/u',
                ];
            default:
                return [];
        }
    }

    /**
     * Sanitize all inputs to prevent XSS and other security issues
     */
    private function sanitizeInputs()
    {
        // Sanitize all string properties
        $stringProperties = [
            'fullname', 'nationality', 'id_number', 'place_of_birth', 'email', 'phone',
            'residential_address', 'postal_address', 'institution_name', 'course_studied',
            'current_employer', 'job_title', 'emergency_contact_name', 'emergency_contact_phone',
            'emergency_contact_address', 'next_of_kin_name', 'next_of_kin_phone', 'applicant_signature'
        ];

        foreach ($stringProperties as $property) {
            if (property_exists($this, $property) && $this->$property) {
                $this->$property = trim($this->$property);
            }
        }

        // Sanitize text areas
        $textProperties = ['additional_qualifications', 'job_responsibilities', 'previous_experience'];
        foreach ($textProperties as $property) {
            if (property_exists($this, $property) && $this->$property) {
                $this->$property = trim($this->$property);
            }
        }
    }

    /**
     * Sanitize string input (names, places, etc.)
     */
    private function sanitizeString($input)
    {
        if (!$input) return $input;

        // Remove HTML tags and encode special characters
        $sanitized = strip_tags($input);
        $sanitized = htmlspecialchars($sanitized, ENT_QUOTES, 'UTF-8');

        // Remove any potential SQL injection patterns
        $sanitized = preg_replace('/[<>"\']/', '', $sanitized);

        return trim($sanitized);
    }

    /**
     * Sanitize alphanumeric input (ID numbers, etc.)
     */
    private function sanitizeAlphanumeric($input)
    {
        if (!$input) return $input;

        // Keep only alphanumeric characters and hyphens
        return preg_replace('/[^a-zA-Z0-9\-]/', '', trim($input));
    }

    /**
     * Sanitize email input
     */
    private function sanitizeEmail($input)
    {
        if (!$input) return $input;

        return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
    }

    /**
     * Sanitize phone input
     */
    private function sanitizePhone($input)
    {
        if (!$input) return $input;

        // Keep only numbers, spaces, hyphens, parentheses, and plus sign
        return preg_replace('/[^0-9\s\-\(\)\+]/', '', trim($input));
    }

    /**
     * Sanitize text input (addresses, descriptions, etc.)
     */
    private function sanitizeText($input)
    {
        if (!$input) return $input;

        // Remove HTML tags and encode special characters
        $sanitized = strip_tags($input);
        $sanitized = htmlspecialchars($sanitized, ENT_QUOTES, 'UTF-8');

        // Remove script tags and potential XSS patterns
        $sanitized = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $sanitized);
        $sanitized = preg_replace('/javascript:/i', '', $sanitized);
        $sanitized = preg_replace('/on\w+\s*=/i', '', $sanitized);

        return trim($sanitized);
    }

    public function render()
    {
        return view('livewire.applicant.application');
    }
}
