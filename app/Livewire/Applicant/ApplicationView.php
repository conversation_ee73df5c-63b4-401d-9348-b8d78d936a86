<?php

namespace App\Livewire\Applicant;

use Livewire\Component;
use App\Models\Application as ApplicationModel;

class ApplicationView extends Component
{
    public $application;

    public function mount($application = null)
    {
        // Security check: Ensure user is authenticated
        if (!auth()->check()) {
            abort(401, 'Unauthorized access');
        }

        $userId = auth()->id();

        if ($application) {
            // Security check: Ensure application belongs to current user
            if ($application->user_id !== $userId) {
                abort(403, 'Access denied');
            }
            $this->application = $application;
        } else {
            // Get the current user's application
            $this->application = ApplicationModel::where('user_id', $userId)->first();

            if (!$this->application) {
                abort(404, 'No application found');
            }
        }
    }

    /**
     * Get the user-friendly section title
     */
    public function getSectionTitle($stage)
    {
        $titles = [
            'section1' => 'Personal Details',
            'section2' => 'Contact & Address Information',
            'section3' => 'Educational Background',
            'section4' => 'Work Experience',
            'section5' => 'Emergency Contact & Lead Information',
            'section6' => 'Position Application',
            'section7' => 'Declaration & Submission'
        ];

        return $titles[$stage] ?? 'Unknown Section';
    }

    public function render()
    {
        return view('livewire.applicant.application-view');
    }
}
