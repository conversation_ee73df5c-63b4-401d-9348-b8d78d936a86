<?php

namespace App\Http\Controllers\Applicant;

use App\Http\Controllers\Controller;
use App\Models\Application;

class ApplicationController extends Controller
{
    public $userId;

    public function __construct()
    {
        // Apply authentication middleware
        $this->middleware('auth');

        // Apply rate limiting for form submissions
        $this->middleware('throttle:60,1')->only(['personal']);
    }

    // /**
    //  * Display a listing of the resource. (dashboard)
    //  */
    public function index()
    {
        // Security check: Ensure user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access your application.');
        }

        $this->userId = auth()->id();

        try {
            $application = Application::select('id', 'stage', 'status', 'submission_date')->where('user_id', $this->userId)->first();
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Error fetching application in index: ' . $e->getMessage());

            // Return to application form if there's a database error
            return redirect()->route('applicant.application-form')->with('error', 'Unable to load your application status. Starting fresh.');
        }

        if (empty($application)) {
            // No application exists - show dashboard with option to start application
            return view('applicant.dashboard')->with([
                'application' => null,
                'message' => 'Welcome! You can start your application when ready.'
            ]);
        } elseif (
            in_array($application->stage, ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'])
            && $application->status == 'draft'
        ) {
            // Application in progress - show dashboard with continue option
            return view('applicant.dashboard')->with([
                'application' => $application,
                'message' => 'Your application is in progress. You can continue from where you left off.'
            ]);
        } else {
            // Application completed or submitted - show dashboard
            return view('applicant.dashboard')->with([
                'application' => $application,
                'message' => 'Your application has been submitted successfully.'
            ]);
        }
    }

    // /**
    //  * Show the form for creating a new resource. (application form)
    //  */
    public function personal()
    {
        // Security check: Ensure user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access your application.');
        }

        $this->userId = auth()->id();

        try {
            $application = Application::where('user_id', $this->userId)->first();
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Error fetching application: ' . $e->getMessage());

            // Return with error message
            return redirect()->route('applicant.dashboard')->with('error', 'Unable to load application. Please try again.');
        }

        // Always show the application form - the Livewire component will handle creating new applications
        return view('applicant.applicationForm')->with('application', $application);
    }

    /**
     * Show the application in view-only mode
     */
    public function view()
    {
        // Security check: Ensure user is authenticated
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please log in to access your application.');
        }

        $this->userId = auth()->id();

        try {
            $application = Application::where('user_id', $this->userId)->first();
        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('Error fetching application for view: ' . $e->getMessage());

            // Return with error message
            return redirect()->route('applicant.dashboard')->with('error', 'Unable to load application. Please try again.');
        }

        if (!$application) {
            return redirect()->route('applicant.dashboard')->with('error', 'No application found. Please start your application first.');
        }

        // Show the application in view-only mode
        return view('applicant.applicationView')->with('application', $application);
    }

}
