<?php

namespace App\Http\Controllers\Applicant;

use App\Http\Controllers\Controller;
use App\Models\Application;

class ApplicationController extends Controller
{
    public $userId;

    // /**
    //  * Display a listing of the resource. (dashboard)
    //  */
    public function index()
    {
        $this->userId = auth()->user()->id;
        $application = Application::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();

        if (empty($application)) {
            return redirect()->route('applicant.application-form');
        } elseif (
            ($application->stage == 'section6' || $application->stage == 'section5' || $application->stage == 'section4' || $application->stage == 'section3' || $application->stage == 'section2' || $application->stage == 'section1')
            && $application->status == 'draft'
        ) {
            return redirect()->route('applicant.application-form')->with('application', $application);
        } else {
            return view('applicant.dashboard')->with('application', $application);
        }
    }

    // /**
    //  * Show the form for creating a new resource. (application form)
    //  */
    public function personal()
    {
        $this->userId = auth()->user()->id;
        $application = Application::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();

        // If no application exists, create a default one or redirect to create one
        if (!$application) {
            // You might want to create a default application here or redirect
            return redirect()->route('applicant.dashboard')->with('error', 'No application found. Please start a new application.');
        }

        if (
            ($application->stage == 'section6' || $application->stage == 'section5' || $application->stage == 'section4' || $application->stage == 'section3' || $application->stage == 'section2' || $application->stage == 'section1')
            && $application->status == 'draft'
        ) {
            return view('applicant.applicationForm')->with('application', $application);
        } else {
            return redirect()->route('applicant.dashboard');
        }
    }

}
