<?php

namespace App\Http\Controllers\Applicant;

use App\Http\Controllers\Controller;
use App\Models\Application;

class ApplicationController extends Controller
{
    public $userId;

    // /**
    //  * Display a listing of the resource. (dashboard)
    //  */
    public function index()
    {
        $this->userId = auth()->user()->id;
        $application = Application::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();

        if (empty($application)) {
            return redirect()->route('applicant.application-form');
        } elseif (
            $application->stage == 'section6' || $application->stage == 'section5' || $application->stage == 'section4' || $application->stage == 'section3' || $application->stage == 'section2' || $application->stage == 'section1'
            && $application->status == 'draft'
        ) {
            return redirect()->route('applicant.application-form');
        } else {
            return view('applicant.dashboard');
        }
    }

    // /**
    //  * Show the form for creating a new resource. (application form)
    //  */
    public function personal()
    {
        $this->userId = auth()->user()->id;
        $application = Application::select('id', 'stage', 'status')->where('user_id', $this->userId)->first();

        if (
            $application->stage == 'section6' || $application->stage == 'section5' || $application->stage == 'section4' || $application->stage == 'section3' || $application->stage == 'section2' || $application->stage == 'section1'
            && $application->status == 'draft'
        ) {
            return view('applicant.applicationForm');
        } else {
            return redirect()->route('applicant.dashboard');
        }
    }

}
