<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class Login<PERSON>ontroller extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated($request, $user)
    {
         // Redirect based on user role
        if ($request->user()->role == "admin") {
            return redirect()->intended(route('admin.dashboard')); // Admin dashboard
        } else if($request->user()->role == "lead"){
            return redirect()->intended(route('lead.dashboard')); // Lead dashboard
        } else if($request->user()->role == "user"){
            return redirect()->intended(route('applicant.dashboard')); // Applicant dashboard
        }
        return redirect()->intended(route('dashboard'));
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }
}
