<?php

use App\Http\Controllers\Applicant\ApplicationController;
use App\Http\Controllers\ApplicantController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\WelcomeController;
use Illuminate\Support\Facades\Route;


Route::get('/', [WelcomeController::class, 'index']);


// Public applicant routes (no authentication required)
Route::prefix('applicant')->name('applicant.')->group(function () {
    Route::get('returning-applicant', [ApplicantController::class, 'index'])->name('returning-applicant');
    Route::get('recruitment-payment/{id}', [PaymentController::class, 'index'])->name('recruitment-payment');
    Route::post('payment', [PaymentController::class, 'initialize'])->name('payment.initialize');
    Route::get('payment/callback', [PaymentController::class, 'callback'])->name('payment.callback');
});



// Admin Dashboard Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('dashboard');
});

// Lead Dashboard Routes
Route::middleware(['auth', 'role:lead'])->prefix('lead')->name('lead.')->group(function () {
    Route::get('/dashboard', function () {
        return view('lead.dashboard');
    })->name('dashboard');
});

// Applicant Dashboard Routes
Route::middleware(['auth', 'role:user'])->prefix('applicant')->name('applicant.')->group(function () {
    Route::get('dashboard', [ApplicationController::class, 'index'])->name('dashboard');
    Route::get('application-form', [ApplicationController::class, 'personal'])->name('application-form');


});

// // General Dashboard (fallback)
// Route::get('/dashboard', function () {
//     return view('dashboard');
// })->middleware(['auth'])->name('dashboard');


require __DIR__.'/auth.php';

 Auth::routes([

  'register' => false, // Register Routes...

//   'reset' => false, // Reset Password Routes...

  'verify' => false, // Email Verification Routes...

]);

