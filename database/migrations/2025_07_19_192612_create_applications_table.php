<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('fullname');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->enum('marriage', ['single', 'married', 'divorced', 'widowed']);
            $table->date('date_of_birth');
            $table->string('nationality');
            $table->string('id_number');
            $table->string('place_of_birth');
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('residential_address')->nullable();
            $table->string('postal_address')->nullable();
            $table->string('declaration')->nullable();
            $table->date('declaration_date')->nullable();
            $table->string('signature')->nullable();
            $table->enum('status', ['draft', 'submitted', 'under_review', 'accepted', 'rejected'])->default('draft');
            $table->enum('stage')->default('section1','section2','section3','section4','section5','section6')->default('section1');
            $table->foreignId('user_id')->references('id')->constrained()->on('users')->onDelete('cascade');
            $table->timestamp('submission_date')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applications');
    }
};
