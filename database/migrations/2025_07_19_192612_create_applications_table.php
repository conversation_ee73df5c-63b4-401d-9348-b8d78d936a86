<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('fullname')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->enum('marriage', ['single', 'married', 'divorced', 'widowed'])->nullable();
            $table->date('date_of_birth')->nullable();
            $table->string('nationality')->nullable();
            $table->string('id_number')->nullable();
            $table->string('place_of_birth')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('residential_address')->nullable();
            $table->string('postal_address')->nullable();
            // Section 3 - Education fields
            $table->string('education_level')->nullable();
            $table->string('institution_name')->nullable();
            $table->string('course_studied')->nullable();
            $table->integer('graduation_year')->nullable();
            $table->text('additional_qualifications')->nullable();

            // Section 4 - Work Experience fields
            $table->string('current_employer')->nullable();
            $table->string('job_title')->nullable();
            $table->date('employment_start_date')->nullable();
            $table->date('employment_end_date')->nullable();
            $table->text('job_responsibilities')->nullable();
            $table->text('previous_experience')->nullable();

            // Section 5 - Emergency Contact fields
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_relationship')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->text('emergency_contact_address')->nullable();
            $table->string('next_of_kin_name')->nullable();
            $table->string('next_of_kin_phone')->nullable();

            // Section 6 - Declaration fields
            $table->string('declaration')->nullable();
            $table->date('declaration_date')->nullable();
            $table->string('applicant_signature')->nullable();
            $table->string('signature')->nullable();
            $table->enum('status', ['draft', 'submitted', 'under_review', 'accepted', 'rejected'])->default('draft');
            $table->enum('stage', ['section1','section2','section3','section4','section5','section6','section7'])->default('section1');
            $table->foreignId('user_id')->references('id')->constrained()->on('users')->onDelete('cascade');
            $table->timestamp('submission_date')->nullable();

            // Security and audit fields
            $table->string('ip_address', 45)->nullable(); // Support IPv6
            $table->text('user_agent')->nullable();
            $table->timestamp('last_activity')->nullable();
            $table->softDeletes(); // Add soft deletes for data retention

            $table->timestamps();

            // Add indexes for security and performance
            $table->index(['user_id', 'status']);
            $table->index(['email']);
            $table->index(['created_at']);
            $table->index(['deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applications');
    }
};
