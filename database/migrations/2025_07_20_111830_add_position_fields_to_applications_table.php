<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->string('position_applied')->nullable()->after('next_of_kin_phone');
            $table->string('preferred_location')->nullable()->after('position_applied');
            $table->string('expected_salary')->nullable()->after('preferred_location');
            $table->date('availability_date')->nullable()->after('expected_salary');
            $table->enum('employment_type', ['full-time', 'part-time', 'contract', 'internship', 'temporary'])->nullable()->after('availability_date');
            $table->text('cover_letter')->nullable()->after('employment_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->dropColumn([
                'position_applied',
                'preferred_location',
                'expected_salary',
                'availability_date',
                'employment_type',
                'cover_letter'
            ]);
        });
    }
};
