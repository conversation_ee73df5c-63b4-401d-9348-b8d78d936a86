<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Remove old position fields that are no longer used
            $table->dropColumn([
                'position_applied',
                'preferred_location',
                'expected_salary'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Restore the old position fields
            $table->string('position_applied')->nullable()->after('next_of_kin_phone');
            $table->string('preferred_location')->nullable()->after('position_applied');
            $table->string('expected_salary')->nullable()->after('preferred_location');
        });
    }
};
