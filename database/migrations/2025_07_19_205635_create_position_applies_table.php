<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('position_applies', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->foreignId('application_id')->constrained()->onDelete('cascade');
            $table->string('job_title');
            $table->string('reference_number');
            $table->string('posting_region');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('position_applies');
    }
};
