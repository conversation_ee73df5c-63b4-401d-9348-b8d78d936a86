<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->dropColumn(['employment_type', 'cover_letter']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            $table->enum('employment_type', ['full-time', 'part-time', 'contract', 'internship', 'temporary'])->nullable()->after('availability_date');
            $table->text('cover_letter')->nullable()->after('employment_type');
        });
    }
};
