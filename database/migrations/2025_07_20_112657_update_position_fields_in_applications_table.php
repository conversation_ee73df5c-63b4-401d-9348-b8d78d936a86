<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Add the new position fields
            $table->string('position_job_title')->nullable()->after('next_of_kin_phone');
            $table->string('reference_number')->nullable()->after('position_job_title');
            $table->string('posting_region')->nullable()->after('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Drop the new position fields
            $table->dropColumn([
                'position_job_title',
                'reference_number',
                'posting_region'
            ]);
        });
    }
};
