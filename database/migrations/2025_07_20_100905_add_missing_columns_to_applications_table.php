<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Add soft deletes column
            $table->softDeletes();

            // Add security and audit fields
            $table->string('ip_address', 45)->nullable(); // Support IPv6
            $table->text('user_agent')->nullable();
            $table->timestamp('last_activity')->nullable();

            // Add missing education and work experience fields
            $table->string('education_level')->nullable();
            $table->string('institution_name')->nullable();
            $table->string('course_studied')->nullable();
            $table->integer('graduation_year')->nullable();
            $table->text('additional_qualifications')->nullable();

            $table->string('current_employer')->nullable();
            $table->string('job_title')->nullable();
            $table->date('employment_start_date')->nullable();
            $table->date('employment_end_date')->nullable();
            $table->text('job_responsibilities')->nullable();
            $table->text('previous_experience')->nullable();

            // Add emergency contact fields
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_relationship')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->text('emergency_contact_address')->nullable();
            $table->string('next_of_kin_name')->nullable();
            $table->string('next_of_kin_phone')->nullable();

            // Add declaration fields
            $table->string('applicant_signature')->nullable();

            // Add indexes for security and performance
            $table->index(['user_id', 'status']);
            $table->index(['email']);
            $table->index(['created_at']);
            $table->index(['deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('applications', function (Blueprint $table) {
            // Drop the added columns
            $table->dropSoftDeletes();
            $table->dropColumn([
                'ip_address',
                'user_agent',
                'last_activity',
                'education_level',
                'institution_name',
                'course_studied',
                'graduation_year',
                'additional_qualifications',
                'current_employer',
                'job_title',
                'employment_start_date',
                'employment_end_date',
                'job_responsibilities',
                'previous_experience',
                'emergency_contact_name',
                'emergency_contact_relationship',
                'emergency_contact_phone',
                'emergency_contact_address',
                'next_of_kin_name',
                'next_of_kin_phone',
                'applicant_signature'
            ]);

            // Drop indexes
            $table->dropIndex(['user_id', 'status']);
            $table->dropIndex(['email']);
            $table->dropIndex(['created_at']);
        });
    }
};
