<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('include_section7', function (Blueprint $table) {
            // Update the stage enum to include section7
            DB::statement("ALTER TABLE applications MODIFY COLUMN stage ENUM('section1','section2','section3','section4','section5','section6','section7') DEFAULT 'section1'");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('include_section7', function (Blueprint $table) {
            //
        });
    }
};
