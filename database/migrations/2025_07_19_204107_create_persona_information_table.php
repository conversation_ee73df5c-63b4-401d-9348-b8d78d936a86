<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('persona_information', function (Blueprint $table) {
            $table->id();
            $table->foreignId('application_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->date('date_of_birth');
            $table->enum('gender', ['male', 'female','other']);
            $table->enum('marriage', ['single', 'married','divorced','widowed']);
            $table->string('nationality')->nullable();
            $table->string('id_number');
            $table->string('phone_number');
            $table->enum('stage', ['section1','section2','section3'])->default('section1');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('persona_information');
    }
};
