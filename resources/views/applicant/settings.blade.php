@extends('applicant.layout.sidebar')

@section('title')
Settings - GSPPS Job Portal
@endsection

@section('content')
<div id="content" class="app-content">
    <h1 class="page-header mb-3">
        Account Settings <small>Manage your account preferences</small>
    </h1>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fa fa-check-circle"></i> {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fa fa-exclamation-triangle"></i> {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- BEGIN row -->
    <div class="row">
        <!-- <PERSON><PERSON><PERSON> Account Information Card -->
        <div class="col-xl-4 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-user"></i> Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label"><strong>Name:</strong></label>
                        <p class="form-control-plaintext">{{ Auth::user()->name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><strong>Email:</strong></label>
                        <p class="form-control-plaintext">{{ Auth::user()->email }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><strong>Account Created:</strong></label>
                        <p class="form-control-plaintext">{{ Auth::user()->created_at->format('M d, Y') }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><strong>Last Updated:</strong></label>
                        <p class="form-control-plaintext">{{ Auth::user()->updated_at->format('M d, Y h:i A') }}</p>
                    </div>
                    <div class="mb-0">
                        <label class="form-label"><strong>Account Status:</strong></label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-success">
                                <i class="fa fa-check-circle"></i> Active
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Account Information Card -->

        <!-- BEGIN Change Password Card -->
        <div class="col-xl-8 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-lock"></i> Change Password
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('applicant.settings.password') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="current_password" class="form-label">Current Password *</label>
                                <input type="password"
                                       class="form-control @error('current_password') is-invalid @enderror"
                                       id="current_password"
                                       name="current_password"
                                       required
                                       placeholder="Enter your current password">
                                @error('current_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="new_password" class="form-label">New Password *</label>
                                <input type="password"
                                       class="form-control @error('new_password') is-invalid @enderror"
                                       id="new_password"
                                       name="new_password"
                                       required
                                       minlength="8"
                                       placeholder="Enter new password">
                                @error('new_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="new_password_confirmation" class="form-label">Confirm New Password *</label>
                                <input type="password"
                                       class="form-control"
                                       id="new_password_confirmation"
                                       name="new_password_confirmation"
                                       required
                                       minlength="8"
                                       placeholder="Confirm new password">
                                <small class="form-text text-muted">Re-enter your new password to confirm.</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fa fa-info-circle"></i>
                                    <strong>Password Requirements:</strong>
                                    <ul class="mb-0 mt-2">
                                        <li>Must be at least 8 characters long</li>
                                        <li>Should contain a mix of letters, numbers, and special characters</li>
                                        <li>Avoid using personal information or common words</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fa fa-save"></i> Update Password
                                </button>
                                <a href="{{ route('applicant.dashboard') }}" class="btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- END Change Password Card -->
    </div>
    <!-- END row -->

    <!-- BEGIN Notification Preferences -->
    <div class="row">
        <div class="col-xl-12 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-bell"></i> Notification Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" checked disabled>
                                    <label class="form-check-label" for="email_notifications">
                                        <strong>Email Notifications</strong>
                                        <small class="d-block text-muted">Receive updates about your application status</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="application_updates" checked disabled>
                                    <label class="form-check-label" for="application_updates">
                                        <strong>Application Updates</strong>
                                        <small class="d-block text-muted">Get notified when your application status changes</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="security_alerts" checked disabled>
                                    <label class="form-check-label" for="security_alerts">
                                        <strong>Security Alerts</strong>
                                        <small class="d-block text-muted">Important security notifications</small>
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="system_updates" disabled>
                                    <label class="form-check-label" for="system_updates">
                                        <strong>System Updates</strong>
                                        <small class="d-block text-muted">General system announcements</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i>
                            <strong>Note:</strong> Notification preferences will be available in a future update.
                            Essential notifications (security and application updates) are currently enabled by default.
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- END Notification Preferences -->

    <!-- BEGIN Additional Settings -->
    <div class="row">
        <!-- BEGIN Help & Support Card -->
        <div class="col-xl-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-question-circle"></i> Help & Support
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-info" disabled>
                            <i class="fa fa-book"></i> User Guide
                            <small class="d-block">Coming soon</small>
                        </button>
                        <button class="btn btn-outline-info" disabled>
                            <i class="fa fa-question"></i> FAQ
                            <small class="d-block">Coming soon</small>
                        </button>
                        <button class="btn btn-outline-info" disabled>
                            <i class="fa fa-envelope"></i> Contact Support
                            <small class="d-block">Coming soon</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Help & Support Card -->

        <!-- BEGIN Account Actions Card -->
        <div class="col-xl-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-tools"></i> Account Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('applicant.application-form') }}" class="btn btn-outline-primary">
                            <i class="fa fa-edit"></i> Edit Application
                        </a>
                        <a href="{{ route('applicant.application-view') }}" class="btn btn-outline-secondary">
                            <i class="fa fa-eye"></i> View Application
                        </a>
                        <a href="{{ route('applicant.dashboard') }}" class="btn btn-outline-success">
                            <i class="fa fa-tachometer-alt"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- END Account Actions Card -->
    </div>
    <!-- END Additional Settings -->

    <!-- BEGIN Security Notice -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fa fa-shield-alt"></i> Security Notice
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        <strong>Important Security Information:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Never share your password with anyone</li>
                            <li>Use a unique password that you don't use on other websites</li>
                            <li>Log out from shared or public computers</li>
                            <li>Contact support immediately if you suspect unauthorized access</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END Security Notice -->
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('new_password_confirmation');
    const submitButton = document.querySelector('button[type="submit"]');

    function validatePasswords() {
        const newPass = newPassword.value;
        const confirmPass = confirmPassword.value;

        // Check if passwords match
        if (newPass && confirmPass) {
            if (newPass === confirmPass) {
                confirmPassword.classList.remove('is-invalid');
                confirmPassword.classList.add('is-valid');
                submitButton.disabled = false;
            } else {
                confirmPassword.classList.remove('is-valid');
                confirmPassword.classList.add('is-invalid');
                submitButton.disabled = true;
            }
        } else {
            confirmPassword.classList.remove('is-valid', 'is-invalid');
            submitButton.disabled = false;
        }

        // Check password strength
        if (newPass) {
            if (newPass.length >= 8) {
                newPassword.classList.remove('is-invalid');
                newPassword.classList.add('is-valid');
            } else {
                newPassword.classList.remove('is-valid');
                newPassword.classList.add('is-invalid');
            }
        } else {
            newPassword.classList.remove('is-valid', 'is-invalid');
        }
    }

    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
});
</script>
@endsection
