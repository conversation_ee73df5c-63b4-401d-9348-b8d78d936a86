@extends('applicant.layout.sidebar')

@section('title')
 Applicant Dashboard - GSPPS Job Portal
@endsection

@section('content')

<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<h1 class="page-header mb-3">
				Hi, {{ ucwords(Auth::user()->name) }}. <small>here's what's happening with your recruitment.</small>
			</h1>

			@if(session('message'))
				<div class="alert alert-info alert-dismissible fade show" role="alert">
					{{ session('message') }}
					<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
				</div>
			@endif

			<!-- BEGIN row -->
			<div class="row">
				<!-- BEGIN Application Status Card -->
				<div class="col-xl-8 mb-3">
					<div class="card">
						<div class="card-header">
							<h5 class="card-title mb-0">Application Status</h5>
						</div>
						<div class="card-body">
							@if($application)
								<div class="row">
									<div class="col-md-4">
										<h6>Current Stage:</h6>
										<span class="badge bg-primary fs-12px">
											@php
												$sectionTitles = [
													'section1' => 'Personal Details',
													'section2' => 'Contact & Address Information',
													'section3' => 'Educational Background',
													'section4' => 'Work Experience',
													'section5' => 'Emergency Contact & Lead Information',
													'section6' => 'Position Application',
													'section7' => 'Declaration & Submission'
												];
												$currentStageTitle = $sectionTitles[$application->stage] ?? 'Unknown Stage';
												$stageNumber = substr($application->stage, -1);
											@endphp
											{{ $currentStageTitle }} (Step {{ $stageNumber }} of 7)
										</span>
									</div>
									<div class="col-md-4">
										<h6>Status:</h6>
										<span class="badge
											@if($application->status == 'draft') bg-warning
											@elseif($application->status == 'submitted') bg-info
											@elseif($application->status == 'under_review') bg-secondary
											@elseif($application->status == 'accepted') bg-success
											@elseif($application->status == 'rejected') bg-danger
											@endif fs-12px">
											{{ ucfirst(str_replace('_', ' ', $application->status)) }}
										</span>
									</div>
									<div class="col-md-4">
										<h6>Submitted Date:</h6>
										@if($application->submission_date)
											<span class="text-success fs-12px">
												{{ $application->submission_date->format('M d, Y') }}
												<br><small class="text-muted">{{ $application->submission_date->format('h:i A') }}</small>
											</span>
										@else
											<span class="text-muted fs-12px">Not submitted yet</span>
										@endif
									</div>
								</div>

								@if($application->status == 'draft')
									<div class="mt-3">
										<p class="text-muted">
											Your application is in progress at <strong>{{ $currentStageTitle }}</strong>.
											You can continue from where you left off.
										</p>
										<a href="{{ route('applicant.application-form') }}" class="btn btn-primary">
											<i class="fa fa-edit"></i> Continue Application
										</a>
									</div>
								@elseif($application->status == 'submitted')
									<div class="mt-3">
										<p class="text-success">Your application has been submitted successfully and is under review.</p>
										<p class="text-muted mb-0">Thank you for completing your application. We will contact you regarding the next steps.</p>
									</div>
								@endif
							@else
								<div class="text-center py-4">
									<i class="fa fa-file-alt fa-3x text-muted mb-3"></i>
									<h5>No Application Found</h5>
									<p class="text-muted">You haven't started your application yet. Click the button below to begin.</p>
									<a href="{{ route('applicant.application-form') }}" class="btn btn-success">
										<i class="fa fa-plus"></i> Start New Application
									</a>
								</div>
							@endif
						</div>
					</div>
				</div>
				<!-- END Application Status Card -->

				<!-- BEGIN Quick Actions Card -->
				<div class="col-xl-4 mb-3">
					<div class="card">
						<div class="card-header">
							<h5 class="card-title mb-0">Quick Actions</h5>
						</div>
						<div class="card-body">
							<div class="d-grid gap-2">
								@if($application && $application->status == 'draft')
									<a href="{{ route('applicant.application-form') }}" class="btn btn-outline-primary">
										<i class="fa fa-edit"></i> Continue Application
									</a>
								@elseif(!$application)
									<a href="{{ route('applicant.application-form') }}" class="btn btn-outline-success">
										<i class="fa fa-plus"></i> Start Application
									</a>
								@endif

								<button class="btn btn-outline-secondary" disabled>
									<i class="fa fa-file-download"></i> Download Certificate
									<small class="d-block">Available after approval</small>
								</button>

								<button class="btn btn-outline-info" disabled>
									<i class="fa fa-envelope"></i> Contact Support
									<small class="d-block">Coming soon</small>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END Quick Actions Card -->
			</div>
			<!-- END row -->


		</div>
		<!-- END #content -->
@endsection
