<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>@yield('title', 'GSPPS Job Portal')</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- ================== BEGIN core-css ================== -->
    <link href="{{ asset('css/vendor.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/app.min.css') }}" rel="stylesheet">
    <!-- ================== END core-css ================== -->

    @livewireStyles
</head>
<body>
<div id="app" class="app">
    <!-- BEGIN #header -->
    <div id="header" class="app-header">
        <!-- BEGIN mobile-toggler -->
        <div class="mobile-toggler">
            <button type="button" class="menu-toggler" data-toggle="sidebar-mobile">
                <span class="bar"></span>
                <span class="bar"></span>
            </button>
        </div>
        <!-- END mobile-toggler -->

        <!-- BEGIN brand -->
        <div class="brand">
            <div class="desktop-toggler">
                <button type="button" class="menu-toggler" data-toggle="sidebar-minify">
                    <span class="bar"></span>
                    <span class="bar"></span>
                </button>
            </div>

            <a href="{{ route('applicant.dashboard') }}" class="brand-logo">
                {{-- <img src="{{ asset('img/logo.png') }}" class="invert-dark" alt="" height="20"> --}}
                GSPPS PORTAL
            </a>
        </div>
        <!-- END brand -->

        <!-- BEGIN menu -->
        <div class="menu">
            <form class="menu-search" method="POST" name="header_search_form">
                <div class="menu-search-icon"><i class="fa fa-search"></i></div>
                <div class="menu-search-input">
                    <input type="text" class="form-control" placeholder="Search menu...">
                </div>
            </form>

            <div class="menu-item dropdown">
                <a href="#" data-bs-toggle="dropdown" data-display="static" class="menu-link">
                    <i class="fa fa-caret-down"></i> &nbsp;
                    <div class="menu-img online">
                        <img src="{{ asset('img/user/user.jpg') }}" alt="" class="ms-100 mh-100 rounded-circle">
                    </div>
                    <div class="menu-text">{{ Auth::user()->name }}</div>
                </a>
                <div class="dropdown-menu dropdown-menu-end me-lg-3">
                    <a class="dropdown-item d-flex align-items-center" href="#">Setting <i
                            class="fa fa-wrench fa-fw ms-auto text-body text-opacity-50"></i></a>
                    <div class="dropdown-divider"></div>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <a class="dropdown-item d-flex align-items-center" href="{{ route('logout') }}"
                            onclick="event.preventDefault();
                                            this.closest('form').submit();">
                            {{ __('Log Out') }}
                            <i class="fa fa-toggle-off fa-fw ms-auto text-body text-opacity-50"></i>
                        </a>
                    </form>
                </div>
            </div>
        </div>
        <!-- END menu -->
    </div>
    <!-- END #header -->
    <!-- BEGIN #sidebar -->
    <div id="sidebar" class="app-sidebar">
        <!-- BEGIN scrollbar -->
        <div class="app-sidebar-content" data-scrollbar="true" data-height="100%">
            <!-- BEGIN menu -->
            <div class="menu">
                <div class="menu-header">Navigation</div>
                <div class="menu-item active">
                    <a href="{{ route('applicant.dashboard') }}" class="menu-link">
                        <span class="menu-icon"><i class="fa fa-laptop"></i></span>
                        <span class="menu-text">Dashboard</span>
                    </a>
                </div>
                <div class="menu-divider"></div>
                <div class="menu-header">Pages</div>
                <div class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="menu-icon"><i class="fa fa-qrcode"></i></span>
                        <span class="menu-text">Application Form</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="menu-icon"><i class="fa fa-qrcode"></i></span>
                        <span class="menu-text">Status</span>
                    </a>
                </div>
                <div class="menu-item">
                    <a href="#" class="menu-link">
                        <span class="menu-icon"><i class="fa fa-cog"></i></span>
                        <span class="menu-text">Settings</span>
                    </a>
                </div>
            </div>
            <!-- END menu -->
        </div>
        <!-- END scrollbar -->

        <!-- BEGIN mobile-sidebar-backdrop -->
        <button class="app-sidebar-mobile-backdrop" data-dismiss="sidebar-mobile"></button>
        <!-- END mobile-sidebar-backdrop -->
    </div>
    <!-- END #sidebar -->

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        @yield('content')
    </div>
    <!-- END #content -->
</div>

<!-- ================== BEGIN core-js ================== -->
<script src="{{ asset('js/vendor.min.js') }}"></script>
<script src="{{ asset('js/app.min.js') }}"></script>
<!-- ================== END core-js ================== -->

@livewireScripts
</body>
</html>
