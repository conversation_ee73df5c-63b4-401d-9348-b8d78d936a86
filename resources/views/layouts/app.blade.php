<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">

    <!-- Styles / Scripts -->
    <!-- ================== BEGIN core-css ================== -->
    <link href="{{ asset('css/vendor.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/app.min.css') }}" rel="stylesheet">
    <!-- ================== END core-css ================== -->

    <!-- ================== BEGIN page-css ================== -->
    <link href="{{ asset('plugins/summernote/dist/summernote-lite.css') }}" rel="stylesheet">
    <link href="{{ asset('plugins/blueimp-file-upload/css/jquery.fileupload.css') }}" rel="stylesheet">
    <link href="{{ asset('plugins/tag-it/css/jquery.tagit.css') }}" rel="stylesheet">
    <!-- ================== END page-css ================== -->

    <!-- Scripts -->
    {{-- @vite(['resources/sass/app.scss', 'resources/js/app.js']) --}}
</head>

<body>
    {{-- <div id="app">
        <nav class="navbar navbar-expand-md navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    {{ config('app.name', 'Laravel') }}
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">

                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                                </li>
                            @endif

                            @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    {{ Auth::user()->name }}
                                </a>

                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @yield('content')
        </main>
    </div> --}}
    @yield('content')

    <!-- BEGIN btn-scroll-top -->
    <a href="#" data-click="scroll-top" class="btn-scroll-top fade"><i class="fa fa-arrow-up"></i></a>
    <!-- END btn-scroll-top -->
    </div>
    <!-- END #app -->


    <!-- ================== BEGIN core-js ================== -->
    <script src="{{ asset('js/vendor.min.js') }}"></script>
    <script src="{{ asset('js/app.min.js') }}"></script>
    <!-- ================== END core-js ================== -->


    <!-- ================== BEGIN page-js ================== -->
    <script src="{{ asset('plugins/summernote/dist/summernote-lite.min.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/vendor/jquery.ui.widget.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-tmpl/js/tmpl.min.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-load-image/js/load-image.all.min.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-canvas-to-blob/js/canvas-to-blob.min.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-gallery/js/jquery.blueimp-gallery.min.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.iframe-transport.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-process.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-image.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-audio.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-video.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-validate.js') }}"></script>
    <script src="{{ asset('plugins/blueimp-file-upload/js/jquery.fileupload-ui.js') }}"></script>
    <script src="{{ asset('plugins/jquery-migrate/dist/jquery-migrate.min.js') }}"></script>
    <script src="{{ asset('plugins/tag-it/js/tag-it.min.js') }}"></script>
    <script src="{{ asset('js/demo/page-product-details.demo.js') }}"></script>
    <!-- ================== END page-js ================== -->

</body>

</html>
