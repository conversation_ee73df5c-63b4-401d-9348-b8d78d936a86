<div>
    <!-- Application Status Header -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="card-title mb-0">
                <i class="fa fa-file-alt"></i> Application Summary
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h6>Current Stage:</h6>
                    <span class="badge bg-primary">
                        {{ $this->getSectionTitle($application->stage) }}
                    </span>
                </div>
                <div class="col-md-3">
                    <h6>Status:</h6>
                    <span class="badge
                        @if($application->status == 'draft') bg-warning
                        @elseif($application->status == 'submitted') bg-success
                        @elseif($application->status == 'under_review') bg-info
                        @elseif($application->status == 'accepted') bg-success
                        @elseif($application->status == 'rejected') bg-danger
                        @endif">
                        {{ ucfirst(str_replace('_', ' ', $application->status)) }}
                    </span>
                </div>
                <div class="col-md-3">
                    <h6>Submitted Date:</h6>
                    @if($application->submission_date)
                        <span class="text-success">
                            {{ $application->submission_date->format('M d, Y h:i A') }}
                        </span>
                    @else
                        <span class="text-muted">Not submitted yet</span>
                    @endif
                </div>
                <div class="col-md-3">
                    <h6>Last Updated:</h6>
                    <span class="text-muted">
                        {{ $application->updated_at->format('M d, Y h:i A') }}
                    </span>
                </div>
            </div>

            @if($application->status == 'draft')
                <div class="mt-3">
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle"></i>
                        <strong>Application In Progress:</strong> This application is still in draft mode.
                        <a href="{{ route('applicant.application-form') }}" class="alert-link">Continue editing</a> to complete and submit it.
                    </div>
                </div>
            @elseif($application->status == 'submitted')
                <div class="mt-3">
                    <div class="alert alert-success">
                        <i class="fa fa-check-circle"></i>
                        <strong>Application Submitted:</strong> Your application has been successfully submitted and is under review.
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Section 1: Personal Details -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-user"></i> Section 1: Personal Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Full Name:</strong></label>
                    <p class="form-control-plaintext">{{ $application->fullname ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Gender:</strong></label>
                    <p class="form-control-plaintext">{{ ucfirst($application->gender) ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Marital Status:</strong></label>
                    <p class="form-control-plaintext">{{ ucfirst($application->marriage) ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Date of Birth:</strong></label>
                    <p class="form-control-plaintext">{{ $application->date_of_birth ? $application->date_of_birth->format('M d, Y') : 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Nationality:</strong></label>
                    <p class="form-control-plaintext">{{ $application->nationality ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>ID Number:</strong></label>
                    <p class="form-control-plaintext">{{ $application->id_number ? '****' . substr($application->id_number, -4) : 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Place of Birth:</strong></label>
                    <p class="form-control-plaintext">{{ $application->place_of_birth ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 2: Contact Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-address-book"></i> Section 2: Contact & Address Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Email:</strong></label>
                    <p class="form-control-plaintext">{{ $application->email ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Phone:</strong></label>
                    <p class="form-control-plaintext">{{ $application->phone ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Residential Address:</strong></label>
                    <p class="form-control-plaintext">{{ $application->residential_address ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Postal Address:</strong></label>
                    <p class="form-control-plaintext">{{ $application->postal_address ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 3: Educational Background -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-graduation-cap"></i> Section 3: Educational Background
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Education Level:</strong></label>
                    <p class="form-control-plaintext">{{ ucfirst($application->education_level) ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Institution Name:</strong></label>
                    <p class="form-control-plaintext">{{ $application->institution_name ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Course Studied:</strong></label>
                    <p class="form-control-plaintext">{{ $application->course_studied ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Graduation Year:</strong></label>
                    <p class="form-control-plaintext">{{ $application->graduation_year ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Additional Qualifications:</strong></label>
                    <p class="form-control-plaintext">{{ $application->additional_qualifications ?: 'Not provided' }}</p>
                </div>
                @if($application->certificate_path)
                    <div class="col-md-12 mb-3">
                        <label class="form-label"><strong>Certificate:</strong></label>
                        <p class="form-control-plaintext">
                            <a href="{{ Storage::url($application->certificate_path) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fa fa-download"></i> View Certificate
                            </a>
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Section 4: Work Experience -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-briefcase"></i> Section 4: Work Experience
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Current Employer:</strong></label>
                    <p class="form-control-plaintext">{{ $application->current_employer ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Job Title:</strong></label>
                    <p class="form-control-plaintext">{{ $application->job_title ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Employment Start Date:</strong></label>
                    <p class="form-control-plaintext">{{ $application->employment_start_date ? $application->employment_start_date->format('M d, Y') : 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Employment End Date:</strong></label>
                    <p class="form-control-plaintext">{{ $application->employment_end_date ? $application->employment_end_date->format('M d, Y') : 'Current' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Job Responsibilities:</strong></label>
                    <p class="form-control-plaintext">{{ $application->job_responsibilities ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Previous Experience:</strong></label>
                    <p class="form-control-plaintext">{{ $application->previous_experience ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 5: Emergency Contact & Lead Information -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-phone"></i> Section 5: Emergency Contact & Lead Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Emergency Contact Name:</strong></label>
                    <p class="form-control-plaintext">{{ $application->emergency_contact_name ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Relationship:</strong></label>
                    <p class="form-control-plaintext">{{ ucfirst($application->emergency_contact_relationship) ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Emergency Contact Phone:</strong></label>
                    <p class="form-control-plaintext">{{ $application->emergency_contact_phone ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Next of Kin Name:</strong></label>
                    <p class="form-control-plaintext">{{ $application->next_of_kin_name ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Next of Kin Phone:</strong></label>
                    <p class="form-control-plaintext">{{ $application->next_of_kin_phone ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Emergency Contact Address:</strong></label>
                    <p class="form-control-plaintext">{{ $application->emergency_contact_address ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 6: Position Application -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-briefcase"></i> Section 6: Position Application
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Job Title:</strong></label>
                    <p class="form-control-plaintext">{{ $application->position_job_title ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Reference Number:</strong></label>
                    <p class="form-control-plaintext">{{ $application->reference_number ?: 'Not provided' }}</p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Preferred Posting Region:</strong></label>
                    <p class="form-control-plaintext">{{ $application->posting_region ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 7: Declaration -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fa fa-check-circle"></i> Section 7: Declaration & Submission
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Declaration:</strong></label>
                    <p class="form-control-plaintext">
                        @if($application->declaration)
                            <span class="badge bg-success"><i class="fa fa-check"></i> Agreed</span>
                        @else
                            <span class="badge bg-warning">Not agreed</span>
                        @endif
                    </p>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label"><strong>Declaration Date:</strong></label>
                    <p class="form-control-plaintext">{{ $application->declaration_date ? $application->declaration_date->format('M d, Y') : 'Not provided' }}</p>
                </div>
                <div class="col-md-12 mb-3">
                    <label class="form-label"><strong>Applicant Signature:</strong></label>
                    <p class="form-control-plaintext">{{ $application->applicant_signature ?: 'Not provided' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card">
        <div class="card-body text-center">
            <a href="{{ route('applicant.dashboard') }}" class="btn btn-secondary me-2">
                <i class="fa fa-arrow-left"></i> Back to Dashboard
            </a>
            @if($application->status == 'draft')
                <a href="{{ route('applicant.application-form') }}" class="btn btn-primary">
                    <i class="fa fa-edit"></i> Continue Editing
                </a>
            @endif
        </div>
    </div>
</div>
