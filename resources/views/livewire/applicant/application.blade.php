<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN container -->
			<div class="container">
				<!-- BEGIN row -->
				<div class="row justify-content-center">
					<!-- BEGIN col-10 -->
					<div class="col-xl-10">
						<!-- BEGIN row -->
						<div class="row">
							<!-- BEGIN col-9 -->
							<div class="col-xl-9">
								<ul class="breadcrumb">
									<li class="breadcrumb-item"><a href="#">Application</a></li>
									<li class="breadcrumb-item active">Application Form</li>
								</ul>

								<h1 class="page-header">
                                    @if($application && $application->stage)
                                        Application - {{ ucfirst($application->stage) }} <small>Complete your application form</small>
                                    @else
                                        Application Form <small>Complete your application form</small>
                                    @endif
								</h1>

								<hr class="mb-4">

								<!-- BEGIN #wizardLayout1 -->
								<div id="wizardLayout1" class="mb-5">
									<h4>Wizard layout 1</h4>
									<p>Wizard layout include the number of step and text. Please do note that all the wizard is for uxui ONLY but do not include any javascript or backend logic.</p>
									<div class="card">
										<div class="card-body">
											<div class="nav-wizards-container">
												<nav class="nav nav-wizards-1 mb-2">
													<div class="nav-item col">
														<a class="nav-link active" href="#">
															<div class="nav-no">1</div>
															<div class="nav-text">Personal Details</div>
														</a>
													</div>
													<div class="nav-item col">
														<a class="nav-link completed" href="#">
															<div class="nav-no">2</div>
															<div class="nav-text">Second step</div>
														</a>
													</div>
													<div class="nav-item col">
														<a class="nav-link disabled" href="#">
															<div class="nav-no">3</div>
															<div class="nav-text">Active step</div>
														</a>
													</div>
													<div class="nav-item col">
														<a class="nav-link disabled" href="#">
															<div class="nav-no">4</div>
															<div class="nav-text">Disabled step</div>
														</a>
													</div>
													<div class="nav-item col">
														<a class="nav-link disabled" href="#">
															<div class="nav-no">5</div>
															<div class="nav-text">Last step</div>
														</a>
													</div>
												</nav>
											</div>
											<div class="card">
												<div class="card-body">
													wizard content here
												</div>
											</div>
										</div>
										<div class="hljs-container rounded-bottom">
											<pre><code class="xml" data-url="assets/data/form-wizards/code-1.json"></code></pre>
										</div>
									</div>
								</div>
								<!-- END #wizardLayout1 -->
							</div>
							<!-- END col-9-->
							<!-- BEGIN col-3 -->
							<div class="col-xl-3">
								<!-- BEGIN #sidebar-bootstrap -->
								<nav id="sidebar-bootstrap" class="navbar navbar-sticky d-none d-xl-block">
									<nav class="nav">
										<a class="nav-link" href="#wizardLayout1" data-toggle="scroll-to">Personal Details</a>
										<a class="nav-link" href="#wizardLayout2" data-toggle="scroll-to">Wizard layout 2</a>
										<a class="nav-link" href="#wizardLayout3" data-toggle="scroll-to">Wizard layout 3</a>
									</nav>
								</nav>
								<!-- END #sidebar-bootstrap -->
							</div>
							<!-- END col-3 -->
						</div>
						<!-- END row -->
					</div>
					<!-- END col-10 -->
				</div>
				<!-- END row -->
			</div>
			<!-- END container -->
		</div>
		<!-- END #content -->
