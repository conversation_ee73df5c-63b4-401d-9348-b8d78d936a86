<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN container -->
			<div class="container">
				<!-- BEGIN row -->
				<div class="row justify-content-center">
					<!-- BEGIN col-10 -->
					<div class="col-xl-10">
						<!-- BEGIN row -->
						<div class="row">
							<!-- BEGIN col-9 -->
							<div class="col-xl-9">
								<ul class="breadcrumb">
									<li class="breadcrumb-item"><a href="#">Application</a></li>
									<li class="breadcrumb-item active">Application Form</li>
								</ul>

								<h1 class="page-header">
                                    Application Form - {{ ucfirst($currentStage ?? 'Section 1') }} <small>Complete your application form</small>
								</h1>

								<hr class="mb-4">

								@if (session()->has('message'))
									<div class="alert alert-success">
										{{ session('message') }}
									</div>
								@endif

								<!-- BEGIN Application Form -->
								<div class="mb-5">
									<div class="card">
										<div class="card-body">
											<!-- Progress Navigation -->
											<div class="nav-wizards-container">
												<nav class="nav nav-wizards-1 mb-4">
													@php
														$stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
														$stageNames = [
															'section1' => 'Personal Details',
															'section2' => 'Contact Information',
															'section3' => 'Education',
															'section4' => 'Work Experience',
															'section5' => 'Emergency Contacts',
															'section6' => 'Position Applied For',
															'section7' => 'Declaration'
														];
														$currentIndex = array_search($currentStage, $stages);
													@endphp

													@foreach($stages as $index => $stage)
														<div class="nav-item col">
															<a class="nav-link {{ $index == $currentIndex ? 'active' : ($index < $currentIndex ? 'completed' : 'disabled') }}" href="#">
																<div class="nav-no">{{ $index + 1 }}</div>
																<div class="nav-text">{{ $stageNames[$stage] }}</div>
															</a>
														</div>
													@endforeach
												</nav>
											</div>

											<!-- Form Content -->
											<form wire:submit.prevent="saveSection" method="POST">
												@csrf
												@if($currentStage == 'section1')
													<!-- Personal Details Section -->
													<div class="row">
														<div class="col-md-6 mb-3">
															<label class="form-label">Full Name *</label>
															<input type="text" class="form-control" wire:model="fullname"
																   required maxlength="100"
																   pattern="[a-zA-Z\s\-\.']+"
																   title="Only letters, spaces, hyphens, dots and apostrophes allowed"
																   autocomplete="given-name family-name"
																   placeholder="Enter your full name">
															@error('fullname') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Gender *</label>
															<select class="form-control" wire:model="gender" required>
																<option value="">Select Gender</option>
																<option value="male">Male</option>
																<option value="female">Female</option>
																<option value="other">Other</option>
															</select>
															@error('gender') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Marital Status *</label>
															<select class="form-control" wire:model="marriage" required>
																<option value="">Select Status</option>
																<option value="single">Single</option>
																<option value="married">Married</option>
																<option value="divorced">Divorced</option>
																<option value="widowed">Widowed</option>
															</select>
															@error('marriage') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Date of Birth *</label>
															<input type="date" class="form-control" wire:model="date_of_birth"
																   min="1900-01-01"
																   max="{{ date('Y-m-d', strtotime('-18 years')) }}"
																   required>
															@error('date_of_birth') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Nationality *</label>
															<input type="text" class="form-control" wire:model="nationality" required>
															@error('nationality') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">GH Card  Number (or National) *</label>
															<div class="input-group">
																<span class="input-group-text">GHA -</span>
																<input type="text" class="form-control" wire:model="id_number"
																	   required maxlength="30"
																	   pattern="[a-zA-Z0-9\-]+"
																	   title="Only letters, numbers and hyphens allowed"
																	   autocomplete="off">
															</div>
															@error('id_number') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Place of Birth *</label>
															<input type="text" class="form-control" wire:model="place_of_birth" required>
															@error('place_of_birth') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section2')
													<!-- Contact Information Section -->
													<div class="row">
														<div class="col-md-6 mb-3">
															<label class="form-label">Email Address *</label>
															<input type="email" class="form-control" wire:model="email"
																   required maxlength="255"
																   autocomplete="email"
																   title="Please enter a valid email address">
															@error('email') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Phone Number *</label>
															<input type="tel" class="form-control" wire:model="phone"
																   required maxlength="20"
																   pattern="[\+]?[0-9\s\-\(\)]+"
																   title="Please enter a valid phone number"
																   autocomplete="tel">
															@error('phone') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Residential Address *</label>
															<textarea class="form-control" wire:model="residential_address" rows="3" required></textarea>
															@error('residential_address') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Postal Address</label>
															<textarea class="form-control" wire:model="postal_address" rows="3"></textarea>
															@error('postal_address') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section3')
													<!-- Education Section -->
													<div class="mb-4">
														<div class="d-flex justify-content-between align-items-center mb-3">
															<h5 class="mb-0">Educational Background</h5>
															<button type="button" class="btn btn-outline-primary btn-sm" wire:click="addEducationalBackground">
																<i class="fa fa-plus"></i> Add Education
															</button>
														</div>

														@foreach($educationalBackgrounds as $index => $education)
															<div class="card mb-3">
																<div class="card-header d-flex justify-content-between align-items-center">
																	<h6 class="mb-0">Education #{{ $index + 1 }}</h6>
																	@if(count($educationalBackgrounds) > 1)
																		<button type="button" class="btn btn-outline-danger btn-sm" wire:click="removeEducationalBackground({{ $index }})">
																			<i class="fa fa-trash"></i> Remove
																		</button>
																	@endif
																</div>
																<div class="card-body">
																	<div class="row">
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Education Level *</label>
																			<select class="form-control" wire:model="educationalBackgrounds.{{ $index }}.education_level" required>
																				<option value="">Select Education Level</option>
																				<option value="jhs">Junior High School (JHS)</option>
																				<option value="shs">Senior High School (SHS)</option>
																				<option value="diploma">Diploma</option>
																				<option value="degree">Bachelor's Degree</option>
																				<option value="masters">Master's Degree</option>
																				<option value="phd">PhD</option>
																				<option value="other">Other</option>
																			</select>
																			@error('educationalBackgrounds.' . $index . '.education_level') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Institution Name *</label>
																			<input type="text" class="form-control" wire:model="educationalBackgrounds.{{ $index }}.institution_name"
																				   required maxlength="200"
																				   pattern="[a-zA-Z0-9\s\-\.\,\'&]+"
																				   title="Only letters, numbers, spaces, and common punctuation allowed">
																			@error('educationalBackgrounds.' . $index . '.institution_name') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Course/Program Studied *</label>
																			<input type="text" class="form-control" wire:model="educationalBackgrounds.{{ $index }}.course_studied"
																				   required maxlength="200"
																				   pattern="[a-zA-Z0-9\s\-\.\,\'&\(\)]+"
																				   title="Only letters, numbers, spaces, and common punctuation allowed">
																			@error('educationalBackgrounds.' . $index . '.course_studied') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Graduation Year *</label>
																			<input type="number" class="form-control" wire:model="educationalBackgrounds.{{ $index }}.graduation_year"
																				   min="1980" max="{{ date('Y') + 1 }}" required>
																			@error('educationalBackgrounds.' . $index . '.graduation_year') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-12 mb-3">
																			<label class="form-label">Certificate/Diploma Upload</label>
																			<input type="file" class="form-control" wire:model="educationalBackgrounds.{{ $index }}.certificate"
																				   accept=".pdf,.jpg,.jpeg,.png">
																			<small class="form-text text-muted">Upload certificate, diploma, or transcript (PDF, JPG, PNG - Max 2MB)</small>
																			@error('educationalBackgrounds.' . $index . '.certificate') <span class="text-danger">{{ $message }}</span> @enderror

																			@if(!empty($education['certificate_path']))
																				<div class="mt-2 d-flex justify-content-between align-items-center">
																					<div>
																						<small class="text-success">
																							<i class="fa fa-check-circle"></i>
																							Certificate uploaded: {{ basename($education['certificate_path']) }}
																						</small>
																						<br>
																						<a href="{{ Storage::url($education['certificate_path']) }}"
																						   target="_blank"
																						   class="btn btn-outline-primary btn-sm mt-1">
																							<i class="fa fa-eye"></i> View Certificate
																						</a>
																					</div>
																					<button type="button" class="btn btn-outline-danger btn-sm"
																							wire:click="removeCertificate({{ $index }})"
																							title="Remove certificate">
																						<i class="fa fa-trash"></i>
																					</button>
																				</div>
																			@endif
																		</div>
																	</div>
																</div>
															</div>
														@endforeach

														<div class="col-12 mb-3">
															<label class="form-label">Additional Qualifications</label>
															<textarea class="form-control" wire:model="additional_qualifications" rows="3"
																	  maxlength="1000"
																	  placeholder="List any additional certifications, courses, or qualifications"></textarea>
															@error('additional_qualifications') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section4')
													<!-- Work Experience Section -->
													<div class="mb-4">
														<div class="d-flex justify-content-between align-items-center mb-3">
															<h5 class="mb-0">Work Experience</h5>
															<button type="button" class="btn btn-outline-primary btn-sm" wire:click="addWorkExperience">
																<i class="fa fa-plus"></i> Add Experience
															</button>
														</div>

														@foreach($workExperiences as $index => $work)
															<div class="card mb-3">
																<div class="card-header d-flex justify-content-between align-items-center">
																	<h6 class="mb-0">Work Experience #{{ $index + 1 }}</h6>
																	@if(count($workExperiences) > 1)
																		<button type="button" class="btn btn-outline-danger btn-sm" wire:click="removeWorkExperience({{ $index }})">
																			<i class="fa fa-trash"></i> Remove
																		</button>
																	@endif
																</div>
																<div class="card-body">
																	<div class="row">
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Employer/Company *</label>
																			<input type="text" class="form-control" wire:model="workExperiences.{{ $index }}.employer"
																				   maxlength="200"
																				   pattern="[a-zA-Z0-9\s\-\.\,\'&\(\)]+"
																				   title="Only letters, numbers, spaces, and common punctuation allowed"
																				   placeholder="Company/Organization name">
																			@error('workExperiences.' . $index . '.employer') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Job Title/Position *</label>
																			<input type="text" class="form-control" wire:model="workExperiences.{{ $index }}.job_title"
																				   maxlength="100"
																				   pattern="[a-zA-Z0-9\s\-\.\,\'&\(\)\/]+"
																				   title="Only letters, numbers, spaces, and common punctuation allowed"
																				   placeholder="Your position/role">
																			@error('workExperiences.' . $index . '.job_title') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">Start Date</label>
																			<input type="date" class="form-control" wire:model="workExperiences.{{ $index }}.start_date"
																				   min="1950-01-01" max="{{ date('Y-m-d') }}">
																			@error('workExperiences.' . $index . '.start_date') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-6 mb-3">
																			<label class="form-label">End Date</label>
																			<div class="d-flex align-items-center">
																				<input type="date" class="form-control me-2" wire:model="workExperiences.{{ $index }}.end_date"
																					   min="1950-01-01" max="{{ date('Y-m-d') }}"
																					   @if($work['is_current'] ?? false) disabled @endif>
																				<div class="form-check">
																					<input class="form-check-input" type="checkbox"
																						   wire:model="workExperiences.{{ $index }}.is_current"
																						   wire:click="$set('workExperiences.{{ $index }}.end_date', '')">
																					<label class="form-check-label">Current</label>
																				</div>
																			</div>
																			@error('workExperiences.' . $index . '.end_date') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																		<div class="col-md-12 mb-3">
																			<label class="form-label">Job Responsibilities</label>
																			<textarea class="form-control" wire:model="workExperiences.{{ $index }}.responsibilities"
																					  rows="3" maxlength="2000"
																					  placeholder="Describe your main responsibilities and duties"></textarea>
																			@error('workExperiences.' . $index . '.responsibilities') <span class="text-danger">{{ $message }}</span> @enderror
																		</div>
																	</div>
																</div>
															</div>
														@endforeach

														<div class="col-12 mb-3">
															<label class="form-label">Additional Work Experience</label>
															<textarea class="form-control" wire:model="previous_experience" rows="3"
																	  maxlength="2000"
																	  placeholder="Describe any other relevant work experience, internships, or volunteer work"></textarea>
															@error('previous_experience') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section5')
													<!-- Emergency Contacts Section -->
													<div class="row">
														<div class="col-md-12 mb-4">
															<h5 class="text-primary">Emergency Contact</h5>
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Full Name *</label>
															<input type="text" class="form-control" wire:model="emergency_contact_name" required>
															@error('emergency_contact_name') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Relationship *</label>
															<select class="form-control" wire:model="emergency_contact_relationship" required>
																<option value="">Select Relationship</option>
																<option value="parent">Parent</option>
																<option value="spouse">Spouse</option>
																<option value="sibling">Sibling</option>
																<option value="child">Child</option>
																<option value="friend">Friend</option>
																<option value="relative">Relative</option>
																<option value="other">Other</option>
															</select>
															@error('emergency_contact_relationship') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Phone Number *</label>
															<input type="tel" class="form-control" wire:model="emergency_contact_phone" required>
															@error('emergency_contact_phone') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Address *</label>
															<textarea class="form-control" wire:model="emergency_contact_address" rows="2" required></textarea>
															@error('emergency_contact_address') <span class="text-danger">{{ $message }}</span> @enderror
														</div>

														<div class="col-md-12 mb-4 mt-4">
															<h5 class="text-primary">Lead</h5>
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Lead Name *</label>
															<input type="text" class="form-control" wire:model="next_of_kin_name" required>
															@error('next_of_kin_name') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Lead Phone Number *</label>
															<input type="tel" class="form-control" wire:model="next_of_kin_phone" required>
															@error('next_of_kin_phone') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section6')
													<!-- Position Application Section -->
													<div class="row">
														<div class="col-md-12 mb-4">
															<h5 class="text-primary">Position Application</h5>
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Job Title *</label>
															<input type="text" class="form-control" wire:model="position_job_title"
																   required maxlength="200"
																   pattern="[a-zA-Z0-9\s\-\.\,\'&\(\)\/]+"
																   title="Only letters, numbers, spaces, and common punctuation allowed"
																   placeholder="e.g., Software Developer, Marketing Manager">
															@error('position_job_title') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Reference Number</label>
															<input type="text" class="form-control" wire:model="reference_number"
																   maxlength="50"
																   pattern="[a-zA-Z0-9\-]+"
																   title="Only letters, numbers, and hyphens allowed"
																   placeholder="e.g., REF-2024-001">
															@error('reference_number') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Posting Region *</label>
															<input type="text" class="form-control" wire:model="posting_region"
																   required maxlength="100"
																   pattern="[a-zA-Z\s\-\.\,\'&]+"
																   title="Only letters, spaces, and common punctuation allowed"
																   placeholder="e.g., Greater Accra, Ashanti, Northern">
															@error('posting_region') <span class="text-danger">{{ $message }}</span> @enderror
														</div>

														<div class="col-md-6 mb-3">
															<label class="form-label">Availability Date *</label>
															<input type="date" class="form-control" wire:model="availability_date"
																   min="{{ date('Y-m-d') }}"
																   required>
															@error('availability_date') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Employment Type *</label>
															<select class="form-control" wire:model="employment_type" required>
																<option value="">Select Employment Type</option>
																<option value="full-time">Full-time</option>
																<option value="part-time">Part-time</option>
																<option value="contract">Contract</option>
																<option value="internship">Internship</option>
																<option value="temporary">Temporary</option>
															</select>
															@error('employment_type') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Cover Letter</label>
															<textarea class="form-control" wire:model="cover_letter" rows="6"
																	  maxlength="2000"
																	  placeholder="Write a brief cover letter explaining why you're interested in this position and what makes you a good fit..."></textarea>
															<small class="form-text text-muted">Optional - Maximum 2000 characters</small>
															@error('cover_letter') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section7')
													<!-- Declaration Section -->
													<div class="row">
														<div class="col-md-12 mb-4">
															<div class="card bg-light">
																<div class="card-body">
																	<h5 class="card-title">Declaration</h5>
																	<p class="card-text">
																		I hereby declare that the information provided in this application is true, complete, and accurate to the best of my knowledge.
																		I understand that any false or misleading information may result in the rejection of my application or termination of employment if discovered after hiring.
																	</p>
																	<p class="card-text">
																		I consent to the verification of the information provided and authorize the organization to conduct background checks as deemed necessary.
																	</p>
																</div>
															</div>
														</div>
														<div class="col-md-12 mb-3">
															<div class="form-check">
																<input class="form-check-input" type="checkbox" wire:model="declaration" id="declaration" required>
																<label class="form-check-label" for="declaration">
																	<strong>I agree to the above declaration *</strong>
																</label>
															</div>
															@error('declaration') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Declaration Date *</label>
															<input type="date" class="form-control" wire:model="declaration_date"
																   min="{{ date('Y-m-d', strtotime('-30 days')) }}"
																   max="{{ date('Y-m-d') }}"
																   required>
															@error('declaration_date') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Digital Signature (Full Name) *</label>
															<input type="text" class="form-control" wire:model="applicant_signature" placeholder="Type your full name as signature" required>
															@error('applicant_signature') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@else
													<!-- Fallback for unknown sections -->
													<div class="text-center py-5">
														<h4>{{ $stageNames[$currentStage] ?? 'Section' }}</h4>
														<p class="text-muted">This section is under development.</p>
													</div>
												@endif

												<!-- Form Actions -->
												<div class="d-flex justify-content-between mt-4">
													@if($currentStage != 'section1')
														<button type="button" class="btn btn-secondary" wire:click="previousSection">
															<i class="fa fa-arrow-left"></i> Previous
														</button>
													@else
														<div></div>
													@endif

													<div>
														<button type="submit" class="btn btn-primary me-2">
															<i class="fa fa-save"></i> Save
														</button>

														@if($currentStage == 'section7')
															<button type="button" class="btn btn-success" wire:click="submitApplication">
																<i class="fa fa-check"></i> Submit Application
															</button>
														@else
															<button type="button" class="btn btn-success" wire:click="nextSection">
																Next <i class="fa fa-arrow-right"></i>
															</button>
														@endif
													</div>
												</div>
											</form>
										</div>
									</div>
								</div>
								<!-- END Application Form -->
							</div>
							<!-- END col-9-->
							<!-- BEGIN col-3 -->
							<div class="col-xl-3">
								<!-- BEGIN #sidebar-bootstrap -->
								<nav id="sidebar-bootstrap" class="navbar navbar-sticky d-none d-xl-block">
									<nav class="nav">
										<a class="nav-link {{ $currentStage == 'section1' ? 'active' : '' }}" href="#section1">
											<i class="fa fa-user"></i> Personal Details
										</a>
										<a class="nav-link {{ $currentStage == 'section2' ? 'active' : '' }}" href="#section2">
											<i class="fa fa-home"></i> Contact & Address
										</a>
										<a class="nav-link {{ $currentStage == 'section3' ? 'active' : '' }}" href="#section3">
											<i class="fa fa-graduation-cap"></i> Education
										</a>
										<a class="nav-link {{ $currentStage == 'section4' ? 'active' : '' }}" href="#section4">
											<i class="fa fa-briefcase"></i> Work Experience
										</a>
										<a class="nav-link {{ $currentStage == 'section5' ? 'active' : '' }}" href="#section5">
											<i class="fa fa-phone"></i> Emergency Contact
										</a>
										<a class="nav-link {{ $currentStage == 'section6' ? 'active' : '' }}" href="#section6">
											<i class="fa fa-star"></i> Position Application
										</a>
										<a class="nav-link {{ $currentStage == 'section7' ? 'active' : '' }}" href="#section7">
											<i class="fa fa-check-circle"></i> Declaration
										</a>
									</nav>
								</nav>
								<!-- END #sidebar-bootstrap -->
							</div>
							<!-- END col-3 -->
						</div>
						<!-- END row -->
					</div>
					<!-- END col-10 -->
				</div>
				<!-- END row -->
			</div>
			<!-- END container -->
		</div>
		<!-- END #content -->
