<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<!-- BEGIN container -->
			<div class="container">
				<!-- BEGIN row -->
				<div class="row justify-content-center">
					<!-- BEGIN col-10 -->
					<div class="col-xl-10">
						<!-- BEGIN row -->
						<div class="row">
							<!-- BEGIN col-9 -->
							<div class="col-xl-9">
								<ul class="breadcrumb">
									<li class="breadcrumb-item"><a href="#">Application</a></li>
									<li class="breadcrumb-item active">Application Form</li>
								</ul>

								<h1 class="page-header">
                                    Application Form - {{ ucfirst($currentStage ?? 'Section 1') }} <small>Complete your application form</small>
								</h1>

								<hr class="mb-4">

								@if (session()->has('message'))
									<div class="alert alert-success">
										{{ session('message') }}
									</div>
								@endif

								<!-- BEGIN Application Form -->
								<div class="mb-5">
									<div class="card">
										<div class="card-body">
											<!-- Progress Navigation -->
											<div class="nav-wizards-container">
												<nav class="nav nav-wizards-1 mb-4">
													@php
														$stages = ['section1', 'section2', 'section3', 'section4', 'section5', 'section6'];
														$stageNames = [
															'section1' => 'Personal Details',
															'section2' => 'Contact Information',
															'section3' => 'Education',
															'section4' => 'Work Experience',
															'section5' => 'Emergency Contacts',
															'section6' => 'Declaration'
														];
														$currentIndex = array_search($currentStage, $stages);
													@endphp

													@foreach($stages as $index => $stage)
														<div class="nav-item col">
															<a class="nav-link {{ $index == $currentIndex ? 'active' : ($index < $currentIndex ? 'completed' : 'disabled') }}" href="#">
																<div class="nav-no">{{ $index + 1 }}</div>
																<div class="nav-text">{{ $stageNames[$stage] }}</div>
															</a>
														</div>
													@endforeach
												</nav>
											</div>

											<!-- Form Content -->
											<form wire:submit.prevent="saveSection">
												@if($currentStage == 'section1')
													<!-- Personal Details Section -->
													<div class="row">
														<div class="col-md-6 mb-3">
															<label class="form-label">Full Name *</label>
															<input type="text" class="form-control" wire:model="fullname" required>
															@error('fullname') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Gender *</label>
															<select class="form-control" wire:model="gender" required>
																<option value="">Select Gender</option>
																<option value="male">Male</option>
																<option value="female">Female</option>
																<option value="other">Other</option>
															</select>
															@error('gender') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Marital Status *</label>
															<select class="form-control" wire:model="marriage" required>
																<option value="">Select Status</option>
																<option value="single">Single</option>
																<option value="married">Married</option>
																<option value="divorced">Divorced</option>
																<option value="widowed">Widowed</option>
															</select>
															@error('marriage') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Date of Birth *</label>
															<input type="date" class="form-control" wire:model="date_of_birth" required>
															@error('date_of_birth') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Nationality *</label>
															<input type="text" class="form-control" wire:model="nationality" required>
															@error('nationality') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">GH Card  Number (or National) *</label>
															GHA - <input type="text" class="form-control" wire:model="id_number" required>
															@error('id_number') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Place of Birth *</label>
															<input type="text" class="form-control" wire:model="place_of_birth" required>
															@error('place_of_birth') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@elseif($currentStage == 'section2')
													<!-- Contact Information Section -->
													<div class="row">
														<div class="col-md-6 mb-3">
															<label class="form-label">Email Address *</label>
															<input type="email" class="form-control" wire:model="email" required>
															@error('email') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-6 mb-3">
															<label class="form-label">Phone Number *</label>
															<input type="tel" class="form-control" wire:model="phone" required>
															@error('phone') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Residential Address *</label>
															<textarea class="form-control" wire:model="residential_address" rows="3" required></textarea>
															@error('residential_address') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
														<div class="col-md-12 mb-3">
															<label class="form-label">Postal Address</label>
															<textarea class="form-control" wire:model="postal_address" rows="3"></textarea>
															@error('postal_address') <span class="text-danger">{{ $message }}</span> @enderror
														</div>
													</div>
												@else
													<!-- Other sections placeholder -->
													<div class="text-center py-5">
														<h4>{{ $stageNames[$currentStage] ?? 'Section' }}</h4>
														<p class="text-muted">This section is under development.</p>
													</div>
												@endif

												<!-- Form Actions -->
												<div class="d-flex justify-content-between mt-4">
													@if($currentStage != 'section1')
														<button type="button" class="btn btn-secondary" wire:click="previousSection">
															<i class="fa fa-arrow-left"></i> Previous
														</button>
													@else
														<div></div>
													@endif

													<div>
														<button type="submit" class="btn btn-primary me-2">
															<i class="fa fa-save"></i> Save
														</button>

														@if($currentStage != 'section6')
															<button type="button" class="btn btn-success" wire:click="nextSection">
																Next <i class="fa fa-arrow-right"></i>
															</button>
														@endif
													</div>
												</div>
											</form>
										</div>
									</div>
								</div>
								<!-- END Application Form -->
							</div>
							<!-- END col-9-->
							<!-- BEGIN col-3 -->
							<div class="col-xl-3">
								<!-- BEGIN #sidebar-bootstrap -->
								<nav id="sidebar-bootstrap" class="navbar navbar-sticky d-none d-xl-block">
									<nav class="nav">
										<a class="nav-link" href="#wizardLayout1" data-toggle="scroll-to">Personal Details</a>
										<a class="nav-link" href="#wizardLayout2" data-toggle="scroll-to">Wizard layout 2</a>
										<a class="nav-link" href="#wizardLayout3" data-toggle="scroll-to">Wizard layout 3</a>
									</nav>
								</nav>
								<!-- END #sidebar-bootstrap -->
							</div>
							<!-- END col-3 -->
						</div>
						<!-- END row -->
					</div>
					<!-- END col-10 -->
				</div>
				<!-- END row -->
			</div>
			<!-- END container -->
		</div>
		<!-- END #content -->
