    <div class="login-content">
        <form wire:submit.prevent="submit">
            <h1 class="text-center">GSPPS JOB PORTAL</h1>
            <div class="text-muted text-center mb-4">
                Submit your details to continue
            </div>
            <div>

                @if (session()->has('message'))
                    <div class="alert alert-danger d-flex align-items-center" role="alert">
                        <i class="fa fa-exclamation-triangle me-2"></i>
                        {{ session('message') }}
                    </div>
                @endif
            </div>
            <div class="mb-3">
                <div class="d-flex">
                    <label class="form-label">Full Name</label>
                </div>
                <input type="text" class="form-control form-control-lg fs-15px" wire:model="full_name"
                    placeholder="Enter Official Name">
                @error('full_name')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>
            <div class="mb-3">
                <label class="form-label">Email Address</label>
                <input type="email" class="form-control form-control-lg fs-15px" wire:model="email"
                    placeholder="<EMAIL>">
                @error('email')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>
            <div class="mb-3">
                <div class="d-flex">
                    <label class="form-label">Phone Number</label>
                </div>
                <input type="tel" class="form-control form-control-lg fs-15px" wire:model="phone"
                    placeholder="Mobile Number">
                @error('phone')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>
            <div class="form-group mb-3">
                <label class="form-label" for="industry">Select Sector</label>
                <select class="form-select" id="industry" wire:model="industry">
                    <option value="">Select Industry</option>
                    @foreach ($industries as $industry)
                        <option value="{{ $industry->id }}">{{ ucwords($industry->name) }}</option>
                    @endforeach

                </select>
                @error('industry')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>
            <div class="mb-3">
                <div class="d-flex">
                    <label class="form-label">Referral Code</label>
                </div>
                <input type="text" class="form-control form-control-lg fs-15px" wire:model="referral_code"
                    placeholder="Enter Referral Code">
                @error('referral_code')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <button type="submit" class="btn btn-theme btn-lg d-block w-100 fw-500 mb-3">Submit</button>
            <div class="text-center text-muted">
                Returning applicant? <a href="{{ route('applicant.returning-applicant') }}">click here to continue</a>.
            </div>
            <div wire:loading>
                <span class="loader"></span> Processing...
            </div>
        </form>
    </div>
