@import "elements.scss";

/* Theme Variables
 ------------------------------------------ */
$border-color: #00000032;
$background-color: #8080801d;

$img-margin-left: 10px;
$img-margin-right: 10px;

/* Layout
 ------------------------------------------ */
.note-editor {
  position: relative;

  // dropzone
  $dropzone-color: lightskyblue;
  $dropzone-active-color: darken($dropzone-color, 30);
  .note-dropzone {
    position: absolute;
    display: none;
    z-index: 100;
    color: $dropzone-color;
    background-color: #fff;
    opacity: 0.95;

    .note-dropzone-message {
      display: table-cell;
      vertical-align: middle;
      text-align: center;
      font-size: 28px;
      font-weight: 700;
    }

    &.hover {
      color: $dropzone-active-color;
    }
  }

  &.dragover .note-dropzone {
    display: table;
  }

  .note-editing-area {
    position: relative;

    .note-editable {
      outline: none;

      sup {
        vertical-align: super;
      }

      sub {
        vertical-align: sub;
      }

      img.note-float-left {
        margin-right: $img-margin-right;
      }

      img.note-float-right {
        margin-left: $img-margin-left;
      }
    }
  }
}

/* Frame mode layout
 ------------------------------------------ */
.note-editor.note-frame,
.note-editor.note-airframe {
  border: 1px solid $border-color;

  // codeview mode
  &.codeview {
    .note-editing-area {
      .note-editable {
        display: none;
      }
      .note-codable {
        display: block;
      }
    }
  }

  .note-editing-area {
    overflow: hidden;

    // editable
    .note-editable {
      padding: 10px;
      overflow: auto;
      word-wrap: break-word;

      &[contenteditable="false"] {
        background-color: $background-color;
      }
    }

    // codeable
    .note-codable {
      display: none;
      width: 100%;
      padding: 10px;
      border: none;
      box-shadow: none;
      font-family: Menlo, Monaco, monospace, sans-serif;
      font-size: 14px;
      color: #ccc;
      background-color: #222;
      resize: none;
      outline: none;

      // override BS2 default style
      @include box-sizing(border-box);
      @include rounded(0);
      margin-bottom: 0;
    }
  }

  // fullscreen mode
  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100% !important;
    z-index: 1050; // bs3 modal-backdrop: 1030, bs2: 1040
    .note-resizebar {
      display: none;
    }
  }

  // Notifications
  .note-status-output {
    display: block;
    width: 100%;
    font-size: 14px;
    line-height: 1.42857143;
    height: 20px;
    margin-bottom: 0;
    color: #000;
    border: 0;
    border-top: 1px solid #e2e2e2;
  }

  .note-status-output:empty {
    height: 0;
    border-top: 0 solid transparent;
  }

  .note-status-output .pull-right {
    float: right !important;
  }

  .note-status-output .text-muted {
    color: #777;
  }

  .note-status-output .text-primary {
    color: #286090;
  }

  .note-status-output .text-success {
    color: #3c763d;
  }

  .note-status-output .text-info {
    color: #31708f;
  }

  .note-status-output .text-warning {
    color: #8a6d3b;
  }

  .note-status-output .text-danger {
    color: #a94442;
  }

  .note-status-output .alert {
    margin: -7px 0 0 0;
    padding: 7px 10px 2px 10px;
    border-radius: 0;
    color: #000;
    background-color: #f5f5f5;
  }

  .note-status-output .alert .note-icon {
    margin-right: 5px;
  }

  .note-status-output .alert-success {
    color: #3c763d !important;
    background-color: #dff0d8 !important;
  }

  .note-status-output .alert-info {
    color: #31708f !important;
    background-color: #d9edf7 !important;
  }

  .note-status-output .alert-warning {
    color: #8a6d3b !important;
    background-color: #fcf8e3 !important;
  }

  .note-status-output .alert-danger {
    color: #a94442 !important;
    background-color: #f2dede !important;
  }

  // statusbar
  .note-statusbar {
    background-color: $background-color;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top: 1px solid $border-color;
    .note-resizebar {
      padding-top: 1px;
      height: 9px;
      width: 100%;
      cursor: ns-resize;
      .note-icon-bar {
        width: 20px;
        margin: 1px auto;
        border-top: 1px solid $border-color;
      }
    }

    &.locked {
      .note-resizebar {
        cursor: default;
        .note-icon-bar {
          display: none;
        }
      }
    }
  }
  .note-placeholder {
    padding: 10px;
  }
}

.note-editor.note-airframe {
  border: 0;

  .note-editing-area {
    .note-editable {
      padding: 0;
    }
  }
}


/* Popover
 ------------------------------------------ */
.note-popover.popover {
  display: none;
  max-width: none;

  .popover-content {
    a {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; // for FF
      vertical-align: middle; // for FF
    }
  }
  .arrow {
    left: 20px !important;
  }
}

/* Popover and Toolbar (Button container)
 ------------------------------------------ */
.note-toolbar {
  position: relative;
}

.note-popover .popover-content, .note-editor .note-toolbar {
  margin: 0;
  padding: 0 0 5px 5px;

  & > .note-btn-group {
    margin-top: 5px;
    margin-left: 0;
    margin-right: 5px;
  }

  .note-btn-group {
    .note-table {
      min-width: 0;
      padding: 5px;
      .note-dimension-picker {
        font-size: 18px;
        .note-dimension-picker-mousecatcher {
          position: absolute !important;
          z-index: 3;
          width: 10em;
          height: 10em;
          cursor: pointer;
        }
        .note-dimension-picker-unhighlighted {
          position: relative !important;
          z-index: 1;
          width: 5em;
          height: 5em;
          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIj4+Pjp6ekKlAqjAAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKhmnaJzPAAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC") repeat;
        }
        .note-dimension-picker-highlighted {
          position: absolute !important;
          z-index: 2;
          width: 1em;
          height: 1em;
          background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAACVBMVEUAAIjd6vvD2f9LKLW+AAAAAXRSTlMAQObYZgAAAAFiS0dEAIgFHUgAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAHdElNRQfYAR0BKwNDEVT0AAAAG0lEQVQI12NgAAOtVatWMTCohoaGUY+EmIkEAEruEzK2J7tvAAAAAElFTkSuQmCC") repeat;
        }
      }
    }
  }

  .note-style {
    .dropdown-style {
      blockquote, pre {
        margin: 0;
        padding: 5px 10px;
      }
      h1, h2, h3, h4, h5, h6, p {
        margin: 0;
        padding: 0;
      }
    }
  }

  .note-color-all {
    .note-dropdown-menu {
      min-width: 337px;
    }
  }

  .note-color {
    .dropdown-toggle {
      width: 20px;
      padding-left: 5px;
    }
    .note-dropdown-menu {
      .note-palette {
        display: inline-block;
        margin: 0;
        width: 160px;
        &:first-child {
          margin: 0 5px;
        }

        .note-palette-title {
          font-size: 12px;
          margin: 2px 7px;
          text-align: center;
          border-bottom: 1px solid #eee;
        }

        .note-color-reset,
        .note-color-select {
          font-size: 11px;
          margin: 3px;
          padding: 0 3px;
          cursor: pointer;
          width: 100%;
          @include rounded(5px);

          &:hover {
            background: #eee;
          }
        }

        .note-color-row {
          height: 20px;
        }

        .note-color-select-btn {
          display: none;
        }

        .note-holder-custom {
          .note-color-btn {
            border: 1px solid #eee;
          }
        }
      }
    }
  }

  .note-para {
    .note-dropdown-menu {
      min-width: 228px;
      padding: 5px;
      & > div + div {
        margin-left: 5px;
      }
    }
  }

  // dropdown-menu for toolbar and popover
  .note-dropdown-menu {
    min-width: 160px;

    // dropdown-menu right position
    // http://forrst.com/posts/Bootstrap_right_positioned_dropdown-2KB
    &.right {
      right: 0;
      left: auto;
      &::before {
        right: 9px;
        left: auto !important;
      }
      &::after {
        right: 10px;
        left: auto !important;
      }
    }
    // dropdown-menu for selectbox
    &.note-check {
      a i {
        color: deepskyblue;
        visibility: hidden;
      }
      a.checked i {
        visibility: visible;
      }
    }
  }

  .note-fontsize-10 {
    font-size: 10px;
  }

  // color palette for toolbar and popover
  .note-color-palette {
    line-height: 1;
    div {
      .note-color-btn {
        width: 20px;
        height: 20px;
        padding: 0;
        margin: 0;
        border: 0;
        border-radius: 0;
      }
      .note-color-btn:hover {
        transform: scale(1.2);
        transition: all 0.2s;
      }
    }
  }
}

/* Dialog
 ------------------------------------------ */
.note-modal {
  .modal-dialog {
    outline: 0;
    border-radius: 5px;
    @include box-shadow(0 3px 9px rgba(0,0,0,.5));
  }
  .form-group { // overwrite BS's form-horizontal minus margins
    margin-left: 0;
    margin-right: 0;
  }
  .note-modal-form {
    margin: 0; // overwrite BS2's form margin bottom
  }
  .note-image-dialog {
    .note-dropzone {
      min-height: 100px;
      font-size: 30px;
      line-height: 4; // vertical-align
      color: lightgray;
      text-align: center;
      border: 4px dashed lightgray;
      margin-bottom: 10px;
    }
  }

  // [workaround] firefox fileinput
  @-moz-document url-prefix() {
    .note-image-input {
      height: auto;
    }
  }
}

/* Placeholder
 ------------------------------------------ */
.note-placeholder {
  position: absolute;
  display: none;
  color: gray;
}

/* Handle
 ------------------------------------------ */
.note-handle {
  // control selection
  .note-control-selection {
    position: absolute;
    display: none;
    border: 1px solid #000;
    & > div {
      position: absolute;
    }

    .note-control-selection-bg {
      width: 100%;
      height: 100%;
      background-color: #000;
      @include opacity(0.3);
    }

    .note-control-handle {
      width: 7px;
      height: 7px;
      border: 1px solid #000;
    }

    .note-control-holder {
      @extend .note-control-handle;
    }

    .note-control-sizing {
      @extend .note-control-handle;
      background-color: #000;
    }

    .note-control-nw {
      top: -5px;
      left: -5px;
      border-right: none;
      border-bottom: none;
    }

    .note-control-ne {
      top: -5px;
      right: -5px;
      border-bottom: none;
      border-left: none;
    }

    .note-control-sw {
      bottom: -5px;
      left: -5px;
      border-top: none;
      border-right: none;
    }

    .note-control-se {
      right: -5px;
      bottom: -5px;
      cursor: se-resize;
    }

    .note-control-se.note-control-holder {
      cursor: default;
      border-top: none;
      border-left: none;
    }

    .note-control-selection-info {
      right: 0;
      bottom: 0;
      padding: 5px;
      margin: 5px;
      color: #fff;
      background-color: #000;
      font-size: 12px;
      @include rounded(5px);
      @include opacity(0.7);
    }
  }
}

.note-hint-popover {
  min-width: 100px;
  padding: 2px;

  .popover-content {
    padding: 3px;
    max-height: 150px;
    overflow: auto;

    .note-hint-group {
      .note-hint-item {
        display: block !important;
        padding: 3px;

        &.active, &:hover {
          display: block;
          clear: both;
          font-weight: 400;
          line-height: 1.4;
          color: white;
          white-space: nowrap;
          text-decoration: none;
          background-color: #428bca;
          outline: 0;
          cursor: pointer;
        }
      }
    }
  }
}

/* Handle
 ------------------------------------------ */
html, body {
  .note-fullscreen-body {
    overflow: hidden !important;
  }
}
//Alignment does not work with or without sequences
.note-editable ul li,.note-editable ol li{
  list-style-position: inside;
}