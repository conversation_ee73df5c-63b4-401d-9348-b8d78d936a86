<!--
Data Source: http://www.htmlhelp.com/reference/html40/entities/symbols.html
HTML To Convert JSON http://convertjson.com/html-table-to-json.htm
Creator : ARGE|LOG﻿ <EMAIL>
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Mathematical symbol hinting</title>
  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libs stylesheets -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <script src="https://www.google.com/jsapi" type="text/javascript"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">

    $(function() {
      $('.summernote').summernote({
        height: 200,
        hint: {
          match: /=(\w{0,})$/,
          search: function(keyword, callback) {
            $.ajax({
              url: 'symbols_mathematical-symbols_Greek-letters.json?v=1'
            }).then(function (data) {
         callback(data.filter(function(item){return item.Character.indexOf(keyword)>-1 || item.FIELD6.indexOf(keyword)>-1;}));
            });
          },
          content: function(item) {
            return item.FIELD6;
          },
          template: function(item) {
            return '[<strong>' + item.FIELD6 + '</strong>] ' + item.Character;
          }
        }
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Summernote with Greek letters</h1>
  <textarea class="summernote">type #su</textarea>
</div>
</body>
</html>
