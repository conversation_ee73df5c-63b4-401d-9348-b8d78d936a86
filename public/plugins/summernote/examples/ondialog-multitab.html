<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Multi-tab dialog</title>

  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libraries -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <script>
    $(function () {
      $('#dropper').on('shown.bs.modal', function() {
        $('.dropping').summernote({ height: 300, focus: true, dialogsInBody: true });
      }).on('hidden.bs.modal', function () {
        $('.dropping').summernote('destroy');
      });

      $('.nav.nav-tabs a').on('click', function (e) {
        e.preventDefault()
          $(this).tab('show')
      })
    });
  </script>
</head>
<body>
<button class="btn btn-primary btn-lg" data-toggle="modal" data-target="#dropper">Show Dialog</button>
<div id="dropper" class="modal fade" tabindex="-1" data-backdrop="static" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-body">
        <div role="tabpanel">
          <!-- Nav tabs -->
          <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="nav-item active"><a href="#tab1" class="nav-link active" aria-controls="tab1" role="tab" data-toggle="tab">Tab 1</a></li>
            <li role="presentation" class="nav-item"><a href="#tab2" class="nav-link" aria-controls="tab2" role="tab" data-toggle="tab">Tab 2</a></li>
          </ul>
          <!-- Tab panes -->
          <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="tab1">
              <div class="dropping">text...</div>
            </div>
            <div role="tabpanel" class="tab-pane" id="tab2">
              <div class="dropping">text...</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default pull-left">
          <span class="fa fa-paperclip"></span>
          Attach Digital Assets
        </button>
        <div class="btn-group">
          <button type="button" class="btn btn-default opacity75" class="close" data-dismiss="modal">
            &times; Cancel
          </button>
          <button type="button" class="btn btn-warning" href="javascript:postDrop()">
            Post Status Update
            <span class="fa fa-bullhorn"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
</body>
</html>
