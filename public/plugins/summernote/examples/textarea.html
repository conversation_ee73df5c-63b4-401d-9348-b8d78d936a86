<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Using in a form</title>

  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libraries BS -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">
    $(function() {
      $('.summernote').summernote({
        height: 200,
      });

      $('form').on('submit', function (e) {
        e.preventDefault();
        alert($('.summernote').summernote('code'));
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Summernote using textarea</h1>
  <div class="card">
    <div class="card-header">
      Textarea
    </div>
    <div class="card-body">
      <form action="#" novalidate>
        <div class="form-group">
          <label for="input">Text</label>
          <input type="text" class="form-control" id="input" value="Title">
        </div>
        <div class="form-group">
          <label for="contents">Contents</label>
          <textarea name="text" class="summernote" id="contents" title="Contents"></textarea>
        </div>
        <input type="submit" class="btn btn-primary" value="Submit">
      </form>
    </div>
  </div>
</div>
</body>
</html>
