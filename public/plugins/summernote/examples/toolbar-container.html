<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Separated containers</title>
  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libraries BS3 -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">
    $(function() {
      $('.summernote').summernote({
        height: 200,
        tabsize: 2,
        toolbarContainer : "#toolbar"
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Summernote with separated containers</h1>
  <br>
  <h2>Here is a toolbar.</h2>
  <div id="toolbar"></div>
  <br>
  <h2>Here is a summernote.</h2>
  <textarea class="summernote">Seasons coming up</textarea>
</div>
</body>
</html>
