<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Hinting from ajax</title>
  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libs stylesheets -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <script src="https://www.google.com/jsapi" type="text/javascript"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">
    $(function() {
      $('.summernote').summernote({
        height: 200,
        hint: {
          match: /#(\w{2,})$/,
          search: function(keyword, callback) {
            $.ajax({
              url: 'https://api.github.com/search/repositories?q=' + keyword + '&order=asc'
            }).then(function (data) {
              callback(data.items);
            });
          },
          content: function(item) {
            return '[' + item.full_name + '] ' + item.description;
          },
          template: function(item) {
            return '[<strong>' + item.full_name + '</strong>] ' + item.description;
          }
        }
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Summernote with User-defined hint</h1>
  <p>Please wait a moment while retrieving search result from GitHub after typing.</p>
  <textarea class="summernote">type #su</textarea>
</div>
</body>
</html>
