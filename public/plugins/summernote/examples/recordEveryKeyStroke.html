<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Record every key stroke</title>
  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libraries BS -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">
    $(document).ready(function() {
      $('.summernote').summernote({
        height: 200,
        tabsize: 2,
        recordEveryKeystroke: true 
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Record every key stroke</h1>
  <textarea class="summernote"><p>Seasons <b>coming up</b></p></textarea>
  <pre>

    $(document).ready(function() {
      $('.summernote').summernote({
        height: 200,
        tabsize: 2,
        recordEveryKeystroke: true 
      });
    });
  </pre>
</div>
</body>
</html>
