<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Limit history stack size</title>
  <!-- include jquery -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.js"></script>

  <!-- include libs stylesheets -->
  <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.css" />
  <script src="//cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.5/umd/popper.js"></script>
  <script src="//maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.js"></script>

  <!-- include summernote -->
  <script type="text/javascript" src="/summernote-bs4.js"></script>

  <link rel="stylesheet" href="example.css">
  <script type="text/javascript">
    $(document).ready(function() {
      $('.summernote').summernote({
        height: 300,
        tabsize: 2,
        followingToolbar: true,
        historyLimit: 5
      });
    });
  </script>
</head>
<body>
<div class="container">
  <h1>Test history limited count </h1>
  <p>
    <span class="badge badge-primary">jQuery v3.3.1</span>
    <span class="badge badge-info">Bootstrap v4.1.3</span>
  </p>
  <div class="summernote"><p>Hello World</p></div>
  <pre>

    $(document).ready(function() {
      $('.summernote').summernote({
        height: 300,
        tabsize: 2,
        followingToolbar: true,
        historyLimit: 5
      });
    });
  </pre>
</div>
</body>
</html>
