{"name": "summernote", "description": "Super simple WYSIWYG editor", "version": "0.9.1", "license": "MIT", "keywords": ["editor", "WYSIWYG"], "repository": {"type": "git", "url": "https://github.com/summernote/summernote.git"}, "author": {"name": "hackerwins", "email": "<EMAIL>"}, "main": "dist/summernote.js", "engines": {"node": ">=17.0.0"}, "scripts": {"dev": "eslint config && webpack serve --config=./config/webpack.development.js --progress", "prebuild": "node ./config/build-fonts.js", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider webpack --node-env production --mode=production --config=./config/webpack.production.js --progress", "lint": "eslint src plugin test config", "test": "vitest run", "test:watch": "vitest", "prepublishOnly": "pinst --disable", "postpublish": "pinst --enable"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-external-helpers": "^7.8.3", "@babel/preset-env": "^7.9.5", "@popperjs/core": "^2.9.2", "@vitest/browser": "^1.6.0", "autoprefixer": "^10.2.4", "babel-loader": "^9.1.3", "babel-plugin-module-resolver": "^5.0.2", "bootstrap": "^5.0.1", "chromedriver": "^126.0.4", "concurrently": "^8.2.2", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.0", "cssnano": "^7.0.3", "eslint": "^8.13.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-chai-friendly": "^1.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-n": "^16.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.2.0", "eslint-webpack-plugin": "^4.2.0", "file-loader": "^6.0.0", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.3.2", "jquery": "^3.6.0", "less": "^4.1.1", "less-loader": "^12.2.0", "mini-css-extract-plugin": "^2.3.0", "mocha": "^10.5.2", "pinst": "^3.0.0", "postcss": "^8.2.6", "postcss-escape-generated-content-string": "^3.0.0", "postcss-loader": "^8.1.1", "resolve-url-loader": "^5.0.0", "sass": "^1.26.3", "sass-loader": "^14.2.1", "serve-static": "^1.14.1", "string-replace-loader": "^3.0.1", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.1.1", "uglify-js": "^3.9.1", "url-loader": "^4.1.0", "vitest": "^1.6.0", "webdriverio": "^8.35.1", "webfont": "^11.1.1", "webpack": "^5.40.0", "webpack-bundle-analyzer": "^4.4.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-sources": "^3.2.3", "zip-webpack-plugin": "^4.0.1"}}