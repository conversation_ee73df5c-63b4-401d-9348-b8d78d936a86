{"version": 3, "file": "lang/summernote-ar-AR.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,SAAS;QACpBC,KAAK,EAAE,aAAa;QACpBC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,YAAY;QAC3BC,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,YAAY;QACpBC,UAAU,EAAE,eAAe;QAC3BC,UAAU,EAAE,aAAa;QACzBC,aAAa,EAAE,aAAa;QAC5BC,SAAS,EAAE,cAAc;QACzBC,UAAU,EAAE,cAAc;QAC1BC,SAAS,EAAE,OAAO;QAClBC,YAAY,EAAE,cAAc;QAC5BC,WAAW,EAAE,cAAc;QAC3BC,cAAc,EAAE,mBAAmB;QACnCC,SAAS,EAAE,eAAe;QAC1BC,aAAa,EAAE,iBAAiB;QAChCC,SAAS,EAAE,kBAAkB;QAC7BC,eAAe,EAAE,SAAS;QAC1BC,eAAe,EAAE,wBAAwB;QACzCC,oBAAoB,EAAE,iCAAiC;QACvDC,GAAG,EAAE,aAAa;QAClBC,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE,cAAc;QACzBpB,MAAM,EAAE,eAAe;QACvBgB,GAAG,EAAE,cAAc;QACnBK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,MAAM;QACZtB,MAAM,EAAE,OAAO;QACfuB,MAAM,EAAE,YAAY;QACpBC,IAAI,EAAE,OAAO;QACbC,aAAa,EAAE,MAAM;QACrBT,GAAG,EAAE,aAAa;QAClBU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,MAAM;QACbC,WAAW,EAAE,iBAAiB;QAC9BC,WAAW,EAAE,iBAAiB;QAC9BC,UAAU,EAAE,iBAAiB;QAC7BC,WAAW,EAAE,iBAAiB;QAC9BC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,OAAO;QACdC,CAAC,EAAE,MAAM;QACTC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,OAAO;QACZC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE,eAAe;QACnBC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,cAAc;QACzBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,oBAAoB;QAChCC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,eAAe;QACxBC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE,eAAe;QACrBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,eAAe;QACtBC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,aAAa;QACzBC,UAAU,EAAE,UAAU;QACtBC,WAAW,EAAE,MAAM;QACnBC,cAAc,EAAE,YAAY;QAC5BC,KAAK,EAAE,aAAa;QACpBC,cAAc,EAAE,aAAa;QAC7BC,QAAQ,EAAE;MACZ,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,UAAU;QACrBC,KAAK,EAAE,KAAK;QACZC,cAAc,EAAE,YAAY;QAC5BC,MAAM,EAAE,QAAQ;QAChBC,mBAAmB,EAAE,cAAc;QACnCC,aAAa,EAAE,eAAe;QAC9BC,SAAS,EAAE;MACb,CAAC;MACD3B,IAAI,EAAE;QACJ,iBAAiB,EAAE,YAAY;QAC/B,MAAM,EAAE,kBAAkB;QAC1B,MAAM,EAAE,qBAAqB;QAC7B,KAAK,EAAE,aAAa;QACpB,OAAO,EAAE,yBAAyB;QAClC,MAAM,EAAE,YAAY;QACpB,QAAQ,EAAE,YAAY;QACtB,WAAW,EAAE,eAAe;QAC5B,eAAe,EAAE,qBAAqB;QACtC,cAAc,EAAE,iBAAiB;QACjC,aAAa,EAAE,eAAe;QAC9B,eAAe,EAAE,cAAc;QAC/B,cAAc,EAAE,eAAe;QAC/B,aAAa,EAAE,cAAc;QAC7B,qBAAqB,EAAE,cAAc;QACrC,mBAAmB,EAAE,cAAc;QACnC,SAAS,EAAE,iCAAiC;QAC5C,QAAQ,EAAE,gCAAgC;QAC1C,YAAY,EAAE,uCAAuC;QACrD,UAAU,EAAE,2CAA2C;QACvD,UAAU,EAAE,2CAA2C;QACvD,UAAU,EAAE,2CAA2C;QACvD,UAAU,EAAE,2CAA2C;QACvD,UAAU,EAAE,2CAA2C;QACvD,UAAU,EAAE,2CAA2C;QACvD,sBAAsB,EAAE,eAAe;QACvC,iBAAiB,EAAE;MACrB,CAAC;MACD4B,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXA,WAAW,EAAE,YAAY;QACzBC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-ar-AR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'ar-AR': {\n      font: {\n        bold: 'عريض',\n        italic: 'مائل',\n        underline: 'تحته خط',\n        clear: 'مسح التنسيق',\n        height: 'إرتفاع السطر',\n        name: 'الخط',\n        strikethrough: 'فى وسطه خط',\n        subscript: 'مخطوطة',\n        superscript: 'حرف فوقي',\n        size: 'الحجم',\n      },\n      image: {\n        image: 'صورة',\n        insert: 'إضافة صورة',\n        resizeFull: 'الحجم بالكامل',\n        resizeHalf: 'تصغير للنصف',\n        resizeQuarter: 'تصغير للربع',\n        floatLeft: 'تطيير لليسار',\n        floatRight: 'تطيير لليمين',\n        floatNone: 'ثابته',\n        shapeRounded: 'الشكل: تقريب',\n        shapeCircle: 'الشكل: دائرة',\n        shapeThumbnail: 'الشكل: صورة مصغرة',\n        shapeNone: 'الشكل: لا شيء',\n        dragImageHere: 'إدرج الصورة هنا',\n        dropImage: 'إسقاط صورة أو نص',\n        selectFromFiles: 'حدد ملف',\n        maximumFileSize: 'الحد الأقصى لحجم الملف',\n        maximumFileSizeError: 'تم تجاوز الحد الأقصى لحجم الملف',\n        url: 'رابط الصورة',\n        remove: 'حذف الصورة',\n        original: 'Original',\n      },\n      video: {\n        video: 'فيديو',\n        videoLink: 'رابط الفيديو',\n        insert: 'إدراج الفيديو',\n        url: 'رابط الفيديو',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'رابط',\n        insert: 'إدراج',\n        unlink: 'حذف الرابط',\n        edit: 'تعديل',\n        textToDisplay: 'النص',\n        url: 'مسار الرابط',\n        openInNewWindow: 'فتح في نافذة جديدة',\n      },\n      table: {\n        table: 'جدول',\n        addRowAbove: 'إضافة سطر أعلاه',\n        addRowBelow: 'إضافة سطر أدناه',\n        addColLeft: 'إضافة عمود قبله',\n        addColRight: 'إضافة عمود بعده',\n        delRow: 'حذف سطر',\n        delCol: 'حذف عمود',\n        delTable: 'حذف الجدول',\n      },\n      hr: {\n        insert: 'إدراج خط أفقي',\n      },\n      style: {\n        style: 'تنسيق',\n        p: 'عادي',\n        blockquote: 'إقتباس',\n        pre: 'شفيرة',\n        h1: 'عنوان رئيسي 1',\n        h2: 'عنوان رئيسي 2',\n        h3: 'عنوان رئيسي 3',\n        h4: 'عنوان رئيسي 4',\n        h5: 'عنوان رئيسي 5',\n        h6: 'عنوان رئيسي 6',\n      },\n      lists: {\n        unordered: 'قائمة مُنقطة',\n        ordered: 'قائمة مُرقمة',\n      },\n      options: {\n        help: 'مساعدة',\n        fullscreen: 'حجم الشاشة بالكامل',\n        codeview: 'شفيرة المصدر',\n      },\n      paragraph: {\n        paragraph: 'فقرة',\n        outdent: 'محاذاة للخارج',\n        indent: 'محاذاة للداخل',\n        left: 'محاذاة لليسار',\n        center: 'توسيط',\n        right: 'محاذاة لليمين',\n        justify: 'ملئ السطر',\n      },\n      color: {\n        recent: 'تم إستخدامه',\n        more: 'المزيد',\n        background: 'لون الخلفية',\n        foreground: 'لون النص',\n        transparent: 'شفاف',\n        setTransparent: 'بدون خلفية',\n        reset: 'إعادة الضبط',\n        resetToDefault: 'إعادة الضبط',\n        cpSelect: 'اختار',\n      },\n      shortcut: {\n        shortcuts: 'إختصارات',\n        close: 'غلق',\n        textFormatting: 'تنسيق النص',\n        action: 'Action',\n        paragraphFormatting: 'تنسيق الفقرة',\n        documentStyle: 'تنسيق المستند',\n        extraKeys: 'أزرار إضافية',\n      },\n      help: {\n        'insertParagraph': 'إدراج فقرة',\n        'undo': 'تراجع عن آخر أمر',\n        'redo': 'إعادة تنفيذ آخر أمر',\n        'tab': 'إزاحة (تاب)',\n        'untab': 'سحب النص باتجاه البداية',\n        'bold': 'تنسيق عريض',\n        'italic': 'تنسيق مائل',\n        'underline': 'تنسيق خط سفلي',\n        'strikethrough': 'تنسيق خط متوسط للنص',\n        'removeFormat': 'إزالة التنسيقات',\n        'justifyLeft': 'محاذاة لليسار',\n        'justifyCenter': 'محاذاة توسيط',\n        'justifyRight': 'محاذاة لليمين',\n        'justifyFull': 'محاذاة كاملة',\n        'insertUnorderedList': 'قائمة منقّطة',\n        'insertOrderedList': 'قائمة مرقّمة',\n        'outdent': 'إزاحة للأمام على الفقرة الحالية',\n        'indent': 'إزاحة للخلف على الفقرة الحالية',\n        'formatPara': 'تغيير التنسيق للكتلة الحالية إلى فقرة',\n        'formatH1': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 1',\n        'formatH2': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 2',\n        'formatH3': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 3',\n        'formatH4': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 4',\n        'formatH5': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 5',\n        'formatH6': 'تغيير التنسيق للكتلة الحالية إلى ترويسة 6',\n        'insertHorizontalRule': 'إدراج خط أفقي',\n        'linkDialog.show': 'إظهار خصائص الرابط',\n      },\n      history: {\n        undo: 'تراجع',\n        redo: 'إعادة',\n      },\n      specialChar: {\n        specialChar: 'محارف خاصة',\n        select: 'اختر المحرف الخاص',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}