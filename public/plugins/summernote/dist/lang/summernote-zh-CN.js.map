{"version": 3, "file": "lang/summernote-zh-CN.js", "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAC,EAAE;EACXA,CAAC,CAACC,MAAM,CAAC,IAAI,EAAED,CAAC,CAACE,UAAU,CAACC,IAAI,EAAE;IAChC,OAAO,EAAE;MACPC,IAAI,EAAE;QACJC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,IAAI;QACVC,aAAa,EAAE,KAAK;QACpBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,IAAI,EAAE;MACR,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,UAAU;QACtBC,UAAU,EAAE,SAAS;QACrBC,aAAa,EAAE,SAAS;QACxBC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,MAAM;QACjBC,YAAY,EAAE,QAAQ;QACtBC,WAAW,EAAE,OAAO;QACpBC,cAAc,EAAE,SAAS;QACzBC,SAAS,EAAE,OAAO;QAClBC,aAAa,EAAE,UAAU;QACzBC,SAAS,EAAE,SAAS;QACpBC,eAAe,EAAE,OAAO;QACxBC,eAAe,EAAE,SAAS;QAC1BC,oBAAoB,EAAE,YAAY;QAClCC,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE;MACZ,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,MAAM;QACjBpB,MAAM,EAAE,MAAM;QACdgB,GAAG,EAAE,MAAM;QACXK,SAAS,EAAE;MACb,CAAC;MACDC,IAAI,EAAE;QACJA,IAAI,EAAE,IAAI;QACVtB,MAAM,EAAE,MAAM;QACduB,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,MAAM;QACZC,aAAa,EAAE,MAAM;QACrBT,GAAG,EAAE,MAAM;QACXU,eAAe,EAAE;MACnB,CAAC;MACDC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,QAAQ;QACrBC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBC,WAAW,EAAE,QAAQ;QACrBC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAE,KAAK;QACbC,QAAQ,EAAE;MACZ,CAAC;MACDC,EAAE,EAAE;QACFnC,MAAM,EAAE;MACV,CAAC;MACDoC,KAAK,EAAE;QACLA,KAAK,EAAE,IAAI;QACXC,CAAC,EAAE,IAAI;QACPC,UAAU,EAAE,IAAI;QAChBC,GAAG,EAAE,IAAI;QACTC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE,MAAM;QACVC,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAE;QACLC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE;MACX,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE;MACZ,CAAC;MACDC,SAAS,EAAE;QACTA,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE;MACX,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAE,IAAI;QACVC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,IAAI;QACpBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE;MAClB,CAAC;MACDC,QAAQ,EAAE;QACRC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE,IAAI;QACXC,cAAc,EAAE,MAAM;QACtBC,MAAM,EAAE,IAAI;QACZC,mBAAmB,EAAE,MAAM;QAC3BC,aAAa,EAAE,MAAM;QACrBC,SAAS,EAAE;MACb,CAAC;MACD1B,IAAI,EAAE;QACJ2B,eAAe,EAAE,MAAM;QACvBC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,IAAI;QACVC,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACb5F,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChBI,aAAa,EAAE,KAAK;QACpBuF,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,KAAK;QAClBC,aAAa,EAAE,MAAM;QACrBC,YAAY,EAAE,KAAK;QACnBC,WAAW,EAAE,MAAM;QACnBC,mBAAmB,EAAE,MAAM;QAC3BC,iBAAiB,EAAE,MAAM;QACzBlC,OAAO,EAAE,MAAM;QACfC,MAAM,EAAE,MAAM;QACdkC,UAAU,EAAE,cAAc;QAC1BC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,eAAe;QACzBC,QAAQ,EAAE,eAAe;QACzBC,oBAAoB,EAAE,OAAO;QAC7B,iBAAiB,EAAE;MACrB,CAAC;MACDC,OAAO,EAAE;QACPnB,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR,CAAC;MACDmB,WAAW,EAAE;QACXA,WAAW,EAAE,MAAM;QACnBC,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;AACJ,CAAC,EAAEC,MAAM,CAAC", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-zh-CN.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, () => {\nreturn ", "(function($) {\n  $.extend(true, $.summernote.lang, {\n    'zh-CN': {\n      font: {\n        bold: '粗体',\n        italic: '斜体',\n        underline: '下划线',\n        clear: '清除格式',\n        height: '行高',\n        name: '字体',\n        strikethrough: '删除线',\n        subscript: '下标',\n        superscript: '上标',\n        size: '字号',\n      },\n      image: {\n        image: '图片',\n        insert: '插入图片',\n        resizeFull: '缩放至 100%',\n        resizeHalf: '缩放至 50%',\n        resizeQuarter: '缩放至 25%',\n        floatLeft: '靠左浮动',\n        floatRight: '靠右浮动',\n        floatNone: '取消浮动',\n        shapeRounded: '形状: 圆角',\n        shapeCircle: '形状: 圆',\n        shapeThumbnail: '形状: 缩略图',\n        shapeNone: '形状: 无',\n        dragImageHere: '将图片拖拽至此处',\n        dropImage: '拖拽图片或文本',\n        selectFromFiles: '从本地上传',\n        maximumFileSize: '文件大小最大值',\n        maximumFileSizeError: '文件大小超出最大值。',\n        url: '图片地址',\n        remove: '移除图片',\n        original: '原始图片',\n      },\n      video: {\n        video: '视频',\n        videoLink: '视频链接',\n        insert: '插入视频',\n        url: '视频地址',\n        providers: '(优酷, 腾讯, Instagram, DailyMotion, Youtube等)',\n      },\n      link: {\n        link: '链接',\n        insert: '插入链接',\n        unlink: '去除链接',\n        edit: '编辑链接',\n        textToDisplay: '显示文本',\n        url: '链接地址',\n        openInNewWindow: '在新窗口打开',\n      },\n      table: {\n        table: '表格',\n        addRowAbove: '在上方插入行',\n        addRowBelow: '在下方插入行',\n        addColLeft: '在左侧插入列',\n        addColRight: '在右侧插入列',\n        delRow: '删除行',\n        delCol: '删除列',\n        delTable: '删除表格',\n      },\n      hr: {\n        insert: '水平线',\n      },\n      style: {\n        style: '样式',\n        p: '普通',\n        blockquote: '引用',\n        pre: '代码',\n        h1: '标题 1',\n        h2: '标题 2',\n        h3: '标题 3',\n        h4: '标题 4',\n        h5: '标题 5',\n        h6: '标题 6',\n      },\n      lists: {\n        unordered: '无序列表',\n        ordered: '有序列表',\n      },\n      options: {\n        help: '帮助',\n        fullscreen: '全屏',\n        codeview: '源代码',\n      },\n      paragraph: {\n        paragraph: '段落',\n        outdent: '减少缩进',\n        indent: '增加缩进',\n        left: '左对齐',\n        center: '居中对齐',\n        right: '右对齐',\n        justify: '两端对齐',\n      },\n      color: {\n        recent: '最近使用',\n        more: '更多',\n        background: '背景',\n        foreground: '前景',\n        transparent: '透明',\n        setTransparent: '透明',\n        reset: '重置',\n        resetToDefault: '默认',\n      },\n      shortcut: {\n        shortcuts: '快捷键',\n        close: '关闭',\n        textFormatting: '文本格式',\n        action: '动作',\n        paragraphFormatting: '段落格式',\n        documentStyle: '文档样式',\n        extraKeys: '额外按键',\n      },\n      help: {\n        insertParagraph: '插入段落',\n        undo: '撤销',\n        redo: '重做',\n        tab: '增加缩进',\n        untab: '减少缩进',\n        bold: '粗体',\n        italic: '斜体',\n        underline: '下划线',\n        strikethrough: '删除线',\n        removeFormat: '清除格式',\n        justifyLeft: '左对齐',\n        justifyCenter: '居中对齐',\n        justifyRight: '右对齐',\n        justifyFull: '两端对齐',\n        insertUnorderedList: '无序列表',\n        insertOrderedList: '有序列表',\n        outdent: '减少缩进',\n        indent: '增加缩进',\n        formatPara: '设置选中内容样式为 普通',\n        formatH1: '设置选中内容样式为 标题1',\n        formatH2: '设置选中内容样式为 标题2',\n        formatH3: '设置选中内容样式为 标题3',\n        formatH4: '设置选中内容样式为 标题4',\n        formatH5: '设置选中内容样式为 标题5',\n        formatH6: '设置选中内容样式为 标题6',\n        insertHorizontalRule: '插入水平线',\n        'linkDialog.show': '显示链接对话框',\n      },\n      history: {\n        undo: '撤销',\n        redo: '重做',\n      },\n      specialChar: {\n        specialChar: '特殊字符',\n        select: '选取特殊字符',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "insertParagraph", "undo", "redo", "tab", "untab", "removeFormat", "justifyLeft", "justifyCenter", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "formatPara", "formatH1", "formatH2", "formatH3", "formatH4", "formatH5", "formatH6", "insertHorizontalRule", "history", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}