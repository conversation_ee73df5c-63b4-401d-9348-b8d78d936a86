.picker {
  display: inline-block;
  vertical-align: middle;
}
.picker .pc-element,
.picker .pc-trigger {
  display: inline-block;
  color: #666666;
  position: relative;
  z-index: 10;
  border: 1px solid #666666;
  border-radius: 2px;
  word-wrap: break-word;
  cursor: default;
  background-color: #ffffff;
  margin-right: 7px;
  margin-bottom: 5px;
  padding: 0 24px 0 6px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.picker .pc-element .pc-close,
.picker .pc-trigger .pc-close {
  position: absolute;
  top: 50%;
  right: 4px;
  margin-top: -10px;
  font-size: 16px;
  cursor: pointer;
}
.picker .pc-element .pc-close:after,
.picker .pc-trigger .pc-close:after {
  content: '\2716';
}
.picker .pc-element:hover,
.picker .pc-trigger:hover {
  background-color: #666666;
  color: #ffffff;
}
.picker .pc-select {
  position: relative;
  display: inline-block;
  min-width: 165px;
  max-width: 200px;
}
.picker .pc-select .pc-trigger {
  cursor: pointer;
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.picker .pc-select .pc-list {
  position: absolute;
  text-align: left;
  left: 0;
  top: calc(100% - 6px);
  width: 100%;
  border: 1px solid #666666;
  z-index: 11;
  background-color: #ffffff;
}
.picker .pc-select .pc-list input[type="search"] {
  width: 100%;
  outline: none;
  color: #666666;
  border: none;
  position: relative;
  background-color: #DDDDDD;
  border-bottom: 2px solid #666;
  padding-left: 8px;
}
.picker .pc-select .pc-list ul {
  margin: 0;
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
}
.picker .pc-select .pc-list li {
  display: block;
  list-style: none;
  padding: 0 0 0 8px;
  cursor: pointer;
  color: #666666;
  word-wrap: break-word;
}
.picker .pc-select .pc-list li:nth-child(even) {
  background-color: #f2f2f2;
}
.picker .pc-select .pc-list li:hover {
  background-color: #666666;
  color: #ffffff;
}
.picker .pc-select .pc-list li.not-found {
  font-style: italic;
  text-align: center;
  cursor: default;
}
.picker .pc-select .pc-list li .searched {
  font-weight: bold;
}
.picker .pc-select .pc-trigger {
  margin-bottom: 5px;
}
