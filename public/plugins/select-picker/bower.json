{"name": "select-picker", "version": "0.3.1", "authors": ["<PERSON> <<EMAIL>>"], "description": "Multi-select tags like picker as plugin for jQuery", "main": ["dist/picker.min.js", "dist/picker.min.css"], "keywords": ["j<PERSON>y", "js", "multiselect", "tags"], "license": "MIT", "dependencies": {"jquery": "^3.1.0"}, "devDependencies": {"bootstrap": "~3.3.5"}, "ignore": ["doc/css/main.less", ".giti<PERSON>re", "gulpfile.js"], "overrides": {"bootstrap": {"main": ["dist/css/bootstrap.css", "dist/js/bootstrap.js"]}}}