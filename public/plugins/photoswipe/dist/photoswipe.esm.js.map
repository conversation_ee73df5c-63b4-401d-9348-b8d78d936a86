{"version": 3, "file": "photoswipe.esm.js", "sources": ["../../../src/js/util/util.js", "../../../src/js/util/dom-events.js", "../../../src/js/util/viewport-size.js", "../../../src/js/slide/pan-bounds.js", "../../../src/js/slide/zoom-level.js", "../../../src/js/slide/slide.js", "../../../src/js/gestures/drag-handler.js", "../../../src/js/gestures/zoom-handler.js", "../../../src/js/gestures/tap-handler.js", "../../../src/js/gestures/gestures.js", "../../../src/js/main-scroll.js", "../../../src/js/keyboard.js", "../../../src/js/util/css-animation.js", "../../../src/js/util/spring-easer.js", "../../../src/js/util/spring-animation.js", "../../../src/js/util/animations.js", "../../../src/js/scroll-wheel.js", "../../../src/js/ui/ui-element.js", "../../../src/js/ui/button-arrow.js", "../../../src/js/ui/button-close.js", "../../../src/js/ui/button-zoom.js", "../../../src/js/ui/loading-indicator.js", "../../../src/js/ui/counter-indicator.js", "../../../src/js/ui/ui.js", "../../../src/js/slide/get-thumb-bounds.js", "../../../src/js/core/eventable.js", "../../../src/js/slide/placeholder.js", "../../../src/js/slide/content.js", "../../../src/js/slide/loader.js", "../../../src/js/core/base.js", "../../../src/js/opener.js", "../../../src/js/photoswipe.js"], "sourcesContent": ["/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/**\r\n * @template {keyof HTMLElementTagNameMap} T\r\n * @param {string} className\r\n * @param {T} tagName\r\n * @param {Node} [appendToEl]\r\n * @returns {HTMLElementTagNameMap[T]}\r\n */\r\nexport function createElement(className, tagName, appendToEl) {\r\n  const el = document.createElement(tagName);\r\n  if (className) {\r\n    el.className = className;\r\n  }\r\n  if (appendToEl) {\r\n    appendToEl.appendChild(el);\r\n  }\r\n  return el;\r\n}\r\n\r\n/**\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\r\nexport function equalizePoints(p1, p2) {\r\n  p1.x = p2.x;\r\n  p1.y = p2.y;\r\n  if (p2.id !== undefined) {\r\n    p1.id = p2.id;\r\n  }\r\n  return p1;\r\n}\r\n\r\n/**\r\n * @param {Point} p\r\n */\r\nexport function roundPoint(p) {\r\n  p.x = Math.round(p.x);\r\n  p.y = Math.round(p.y);\r\n}\r\n\r\n/**\r\n * Returns distance between two points.\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {number}\r\n */\r\nexport function getDistanceBetween(p1, p2) {\r\n  const x = Math.abs(p1.x - p2.x);\r\n  const y = Math.abs(p1.y - p2.y);\r\n  return Math.sqrt((x * x) + (y * y));\r\n}\r\n\r\n/**\r\n * Whether X and Y positions of points are equal\r\n *\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {boolean}\r\n */\r\nexport function pointsEqual(p1, p2) {\r\n  return p1.x === p2.x && p1.y === p2.y;\r\n}\r\n\r\n/**\r\n * The float result between the min and max values.\r\n *\r\n * @param {number} val\r\n * @param {number} min\r\n * @param {number} max\r\n * @returns {number}\r\n */\r\nexport function clamp(val, min, max) {\r\n  return Math.min(Math.max(val, min), max);\r\n}\r\n\r\n/**\r\n * Get transform string\r\n *\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n * @returns {string}\r\n */\r\nexport function toTransformString(x, y, scale) {\r\n  let propValue = `translate3d(${x}px,${y || 0}px,0)`;\r\n\r\n  if (scale !== undefined) {\r\n    propValue += ` scale3d(${scale},${scale},1)`;\r\n  }\r\n\r\n  return propValue;\r\n}\r\n\r\n/**\r\n * Apply transform:translate(x, y) scale(scale) to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {number} x\r\n * @param {number} [y]\r\n * @param {number} [scale]\r\n */\r\nexport function setTransform(el, x, y, scale) {\r\n  el.style.transform = toTransformString(x, y, scale);\r\n}\r\n\r\nconst defaultCSSEasing = 'cubic-bezier(.4,0,.22,1)';\r\n\r\n/**\r\n * Apply CSS transition to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string} [prop] CSS property to animate\r\n * @param {number} [duration] in ms\r\n * @param {string} [ease] CSS easing function\r\n */\r\nexport function setTransitionStyle(el, prop, duration, ease) {\r\n  // inOut: 'cubic-bezier(.4, 0, .22, 1)', // for \"toggle state\" transitions\r\n  // out: 'cubic-bezier(0, 0, .22, 1)', // for \"show\" transitions\r\n  // in: 'cubic-bezier(.4, 0, 1, 1)'// for \"hide\" transitions\r\n  el.style.transition = prop\r\n    ? `${prop} ${duration}ms ${ease || defaultCSSEasing}`\r\n    : 'none';\r\n}\r\n\r\n/**\r\n * Apply width and height CSS properties to element\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string | number} w\r\n * @param {string | number} h\r\n */\r\nexport function setWidthHeight(el, w, h) {\r\n  el.style.width = (typeof w === 'number') ? `${w}px` : w;\r\n  el.style.height = (typeof h === 'number') ? `${h}px` : h;\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n */\r\nexport function removeTransitionStyle(el) {\r\n  setTransitionStyle(el);\r\n}\r\n\r\n/**\r\n * @param {HTMLImageElement} img\r\n * @returns {Promise<HTMLImageElement | void>}\r\n */\r\nexport function decodeImage(img) {\r\n  if ('decode' in img) {\r\n    return img.decode().catch(() => {});\r\n  }\r\n\r\n  if (img.complete) {\r\n    return Promise.resolve(img);\r\n  }\r\n\r\n  return new Promise((resolve, reject) => {\r\n    img.onload = () => resolve(img);\r\n    img.onerror = reject;\r\n  });\r\n}\r\n\r\n/** @typedef {LOAD_STATE[keyof LOAD_STATE]} LoadState */\r\n/** @type {{ IDLE: 'idle'; LOADING: 'loading'; LOADED: 'loaded'; ERROR: 'error' }} */\r\nexport const LOAD_STATE = {\r\n  IDLE: 'idle',\r\n  LOADING: 'loading',\r\n  LOADED: 'loaded',\r\n  ERROR: 'error',\r\n};\r\n\r\n\r\n/**\r\n * Check if click or keydown event was dispatched\r\n * with a special key or via mouse wheel.\r\n *\r\n * @param {MouseEvent | KeyboardEvent} e\r\n * @returns {boolean}\r\n */\r\nexport function specialKeyUsed(e) {\r\n  return ('button' in e && e.button === 1) || e.ctrlKey || e.metaKey || e.altKey || e.shiftKey;\r\n}\r\n\r\n/**\r\n * Parse `gallery` or `children` options.\r\n *\r\n * @param {import('../photoswipe.js').ElementProvider} [option]\r\n * @param {string} [legacySelector]\r\n * @param {HTMLElement | Document} [parent]\r\n * @returns HTMLElement[]\r\n */\r\nexport function getElementsFromOption(option, legacySelector, parent = document) {\r\n  /** @type {HTMLElement[]} */\r\n  let elements = [];\r\n\r\n  if (option instanceof Element) {\r\n    elements = [option];\r\n  } else if (option instanceof NodeList || Array.isArray(option)) {\r\n    elements = Array.from(option);\r\n  } else {\r\n    const selector = typeof option === 'string' ? option : legacySelector;\r\n    if (selector) {\r\n      elements = Array.from(parent.querySelectorAll(selector));\r\n    }\r\n  }\r\n\r\n  return elements;\r\n}\r\n\r\n/**\r\n * Check if variable is PhotoSwipe class\r\n *\r\n * @param {any} fn\r\n * @returns {boolean}\r\n */\r\nexport function isPswpClass(fn) {\r\n  return typeof fn === 'function'\r\n    && fn.prototype\r\n    && fn.prototype.goTo;\r\n}\r\n\r\n/**\r\n * Check if browser is Safari\r\n *\r\n * @returns {boolean}\r\n */\r\nexport function isSafari() {\r\n  return !!(navigator.vendor && navigator.vendor.match(/apple/i));\r\n}\r\n\r\n", "// Detect passive event listener support\r\nlet supportsPassive = false;\r\n/* eslint-disable */\r\ntry {\r\n  /* @ts-ignore */\r\n  window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\r\n    get: () => {\r\n      supportsPassive = true;\r\n    }\r\n  }));\r\n} catch (e) {}\r\n/* eslint-enable */\r\n\r\n/**\r\n * @typedef {Object} PoolItem\r\n * @prop {HTMLElement | Window | Document | undefined | null} target\r\n * @prop {string} type\r\n * @prop {EventListenerOrEventListenerObject} listener\r\n * @prop {boolean} [passive]\r\n */\r\n\r\nclass DOMEvents {\r\n  constructor() {\r\n    /**\r\n     * @type {PoolItem[]}\r\n     * @private\r\n     */\r\n    this._pool = [];\r\n  }\r\n\r\n  /**\r\n   * Adds event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type Can be multiple, separated by space.\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\r\n  add(target, type, listener, passive) {\r\n    this._toggleListener(target, type, listener, passive);\r\n  }\r\n\r\n  /**\r\n   * Removes event listeners\r\n   *\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   */\r\n  remove(target, type, listener, passive) {\r\n    this._toggleListener(target, type, listener, passive, true);\r\n  }\r\n\r\n  /**\r\n   * Removes all bound events\r\n   */\r\n  removeAll() {\r\n    this._pool.forEach((poolItem) => {\r\n      this._toggleListener(\r\n        poolItem.target,\r\n        poolItem.type,\r\n        poolItem.listener,\r\n        poolItem.passive,\r\n        true,\r\n        true\r\n      );\r\n    });\r\n    this._pool = [];\r\n  }\r\n\r\n  /**\r\n   * Adds or removes event\r\n   *\r\n   * @private\r\n   * @param {PoolItem['target']} target\r\n   * @param {PoolItem['type']} type\r\n   * @param {PoolItem['listener']} listener\r\n   * @param {PoolItem['passive']} [passive]\r\n   * @param {boolean} [unbind] Whether the event should be added or removed\r\n   * @param {boolean} [skipPool] Whether events pool should be skipped\r\n   */\r\n  _toggleListener(target, type, listener, passive, unbind, skipPool) {\r\n    if (!target) {\r\n      return;\r\n    }\r\n\r\n    const methodName = unbind ? 'removeEventListener' : 'addEventListener';\r\n    const types = type.split(' ');\r\n    types.forEach((eType) => {\r\n      if (eType) {\r\n        // Events pool is used to easily unbind all events when PhotoSwipe is closed,\r\n        // so developer doesn't need to do this manually\r\n        if (!skipPool) {\r\n          if (unbind) {\r\n            // Remove from the events pool\r\n            this._pool = this._pool.filter((poolItem) => {\r\n              return poolItem.type !== eType\r\n                || poolItem.listener !== listener\r\n                || poolItem.target !== target;\r\n            });\r\n          } else {\r\n            // Add to the events pool\r\n            this._pool.push({\r\n              target,\r\n              type: eType,\r\n              listener,\r\n              passive\r\n            });\r\n          }\r\n        }\r\n\r\n        // most PhotoSwipe events call preventDefault,\r\n        // and we do not need browser to scroll the page\r\n        const eventOptions = supportsPassive ? { passive: (passive || false) } : false;\r\n\r\n        target[methodName](\r\n          eType,\r\n          listener,\r\n          eventOptions\r\n        );\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport default DOMEvents;\r\n", "/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {PhotoSwipeBase} pswp\r\n * @returns {Point}\r\n */\r\nexport function getViewportSize(options, pswp) {\r\n  if (options.getViewportSizeFn) {\r\n    const newViewportSize = options.getViewportSizeFn(options, pswp);\r\n    if (newViewportSize) {\r\n      return newViewportSize;\r\n    }\r\n  }\r\n\r\n  return {\r\n    x: document.documentElement.clientWidth,\r\n\r\n    // TODO: height on mobile is very incosistent due to toolbar\r\n    // find a way to improve this\r\n    //\r\n    // document.documentElement.clientHeight - doesn't seem to work well\r\n    y: window.innerHeight\r\n  };\r\n}\r\n\r\n/**\r\n * Parses padding option.\r\n * Supported formats:\r\n *\r\n * // Object\r\n * padding: {\r\n *  top: 0,\r\n *  bottom: 0,\r\n *  left: 0,\r\n *  right: 0\r\n * }\r\n *\r\n * // A function that returns the object\r\n * paddingFn: (viewportSize, itemData, index) => {\r\n *  return {\r\n *    top: 0,\r\n *    bottom: 0,\r\n *    left: 0,\r\n *    right: 0\r\n *  };\r\n * }\r\n *\r\n * // Legacy variant\r\n * paddingLeft: 0,\r\n * paddingRight: 0,\r\n * paddingTop: 0,\r\n * paddingBottom: 0,\r\n *\r\n * @param {'left' | 'top' | 'bottom' | 'right'} prop\r\n * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n * @param {Point} viewportSize PhotoSwipe viewport size, for example: { x:800, y:600 }\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {number} index Slide index\r\n * @returns {number}\r\n */\r\nexport function parsePaddingOption(prop, options, viewportSize, itemData, index) {\r\n  let paddingValue = 0;\r\n\r\n  if (options.paddingFn) {\r\n    paddingValue = options.paddingFn(viewportSize, itemData, index)[prop];\r\n  } else if (options.padding) {\r\n    paddingValue = options.padding[prop];\r\n  } else {\r\n    const legacyPropName = 'padding' + prop[0].toUpperCase() + prop.slice(1);\r\n    // @ts-expect-error\r\n    if (options[legacyPropName]) {\r\n      // @ts-expect-error\r\n      paddingValue = options[legacyPropName];\r\n    }\r\n  }\r\n\r\n  return Number(paddingValue) || 0;\r\n}\r\n\r\n/**\r\n * @param {PhotoSwipeOptions} options\r\n * @param {Point} viewportSize\r\n * @param {SlideData} itemData\r\n * @param {number} index\r\n * @returns {Point}\r\n */\r\nexport function getPanAreaSize(options, viewportSize, itemData, index) {\r\n  return {\r\n    x: viewportSize.x\r\n      - parsePaddingOption('left', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('right', options, viewportSize, itemData, index),\r\n    y: viewportSize.y\r\n      - parsePaddingOption('top', options, viewportSize, itemData, index)\r\n      - parsePaddingOption('bottom', options, viewportSize, itemData, index)\r\n  };\r\n}\r\n", "import { clamp } from '../util/util.js';\r\nimport { parsePaddingOption } from '../util/viewport-size.js';\r\n\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {Record<Axis, number>} Point */\r\n/** @typedef {'x' | 'y'} Axis */\r\n\r\n/**\r\n * Calculates minimum, maximum and initial (center) bounds of a slide\r\n */\r\nclass PanBounds {\r\n  /**\r\n   * @param {Slide} slide\r\n   */\r\n  constructor(slide) {\r\n    this.slide = slide;\r\n    this.currZoomLevel = 1;\r\n    this.center = /** @type {Point} */ { x: 0, y: 0 };\r\n    this.max = /** @type {Point} */ { x: 0, y: 0 };\r\n    this.min = /** @type {Point} */ { x: 0, y: 0 };\r\n  }\r\n\r\n  /**\r\n   * _getItemBounds\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\r\n  update(currZoomLevel) {\r\n    this.currZoomLevel = currZoomLevel;\r\n\r\n    if (!this.slide.width) {\r\n      this.reset();\r\n    } else {\r\n      this._updateAxis('x');\r\n      this._updateAxis('y');\r\n      this.slide.pswp.dispatch('calcBounds', { slide: this.slide });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * _calculateItemBoundsForAxis\r\n   *\r\n   * @param {Axis} axis\r\n   */\r\n  _updateAxis(axis) {\r\n    const { pswp } = this.slide;\r\n    const elSize = this.slide[axis === 'x' ? 'width' : 'height'] * this.currZoomLevel;\r\n    const paddingProp = axis === 'x' ? 'left' : 'top';\r\n    const padding = parsePaddingOption(\r\n      paddingProp,\r\n      pswp.options,\r\n      pswp.viewportSize,\r\n      this.slide.data,\r\n      this.slide.index\r\n    );\r\n\r\n    const panAreaSize = this.slide.panAreaSize[axis];\r\n\r\n    // Default position of element.\r\n    // By default, it is center of viewport:\r\n    this.center[axis] = Math.round((panAreaSize - elSize) / 2) + padding;\r\n\r\n    // maximum pan position\r\n    this.max[axis] = (elSize > panAreaSize)\r\n      ? Math.round(panAreaSize - elSize) + padding\r\n      : this.center[axis];\r\n\r\n    // minimum pan position\r\n    this.min[axis] = (elSize > panAreaSize)\r\n      ? padding\r\n      : this.center[axis];\r\n  }\r\n\r\n  // _getZeroBounds\r\n  reset() {\r\n    this.center.x = 0;\r\n    this.center.y = 0;\r\n    this.max.x = 0;\r\n    this.max.y = 0;\r\n    this.min.x = 0;\r\n    this.min.y = 0;\r\n  }\r\n\r\n  /**\r\n   * Correct pan position if it's beyond the bounds\r\n   *\r\n   * @param {Axis} axis x or y\r\n   * @param {number} panOffset\r\n   * @returns {number}\r\n   */\r\n  correctPan(axis, panOffset) { // checkPanBounds\r\n    return clamp(panOffset, this.max[axis], this.min[axis]);\r\n  }\r\n}\r\n\r\nexport default PanBounds;\r\n", "const MAX_IMAGE_WIDTH = 4000;\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n\r\n/** @typedef {'fit' | 'fill' | number | ((zoomLevelObject: ZoomLevel) => number)} ZoomLevelOption */\r\n\r\n/**\r\n * Calculates zoom levels for specific slide.\r\n * Depends on viewport size and image size.\r\n */\r\nclass ZoomLevel {\r\n  /**\r\n   * @param {PhotoSwipeOptions} options PhotoSwipe options\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {number} index Slide index\r\n   * @param {PhotoSwipe} [pswp] PhotoSwipe instance, can be undefined if not initialized yet\r\n   */\r\n  constructor(options, itemData, index, pswp) {\r\n    this.pswp = pswp;\r\n    this.options = options;\r\n    this.itemData = itemData;\r\n    this.index = index;\r\n    /** @type { Point | null } */\r\n    this.panAreaSize = null;\r\n    /** @type { Point | null } */\r\n    this.elementSize = null;\r\n    this.fit = 1;\r\n    this.fill = 1;\r\n    this.vFill = 1;\r\n    this.initial = 1;\r\n    this.secondary = 1;\r\n    this.max = 1;\r\n    this.min = 1;\r\n  }\r\n\r\n  /**\r\n   * Calculate initial, secondary and maximum zoom level for the specified slide.\r\n   *\r\n   * It should be called when either image or viewport size changes.\r\n   *\r\n   * @param {number} maxWidth\r\n   * @param {number} maxHeight\r\n   * @param {Point} panAreaSize\r\n   */\r\n  update(maxWidth, maxHeight, panAreaSize) {\r\n    /** @type {Point} */\r\n    const elementSize = { x: maxWidth, y: maxHeight };\r\n    this.elementSize = elementSize;\r\n    this.panAreaSize = panAreaSize;\r\n\r\n    const hRatio = panAreaSize.x / elementSize.x;\r\n    const vRatio = panAreaSize.y / elementSize.y;\r\n\r\n    this.fit = Math.min(1, hRatio < vRatio ? hRatio : vRatio);\r\n    this.fill = Math.min(1, hRatio > vRatio ? hRatio : vRatio);\r\n\r\n    // zoom.vFill defines zoom level of the image\r\n    // when it has 100% of viewport vertical space (height)\r\n    this.vFill = Math.min(1, vRatio);\r\n\r\n    this.initial = this._getInitial();\r\n    this.secondary = this._getSecondary();\r\n    this.max = Math.max(\r\n      this.initial,\r\n      this.secondary,\r\n      this._getMax()\r\n    );\r\n\r\n    this.min = Math.min(\r\n      this.fit,\r\n      this.initial,\r\n      this.secondary\r\n    );\r\n\r\n    if (this.pswp) {\r\n      this.pswp.dispatch('zoomLevelsUpdate', { zoomLevels: this, slideData: this.itemData });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses user-defined zoom option.\r\n   *\r\n   * @private\r\n   * @param {'initial' | 'secondary' | 'max'} optionPrefix Zoom level option prefix (initial, secondary, max)\r\n   * @returns { number | undefined }\r\n   */\r\n  _parseZoomLevelOption(optionPrefix) {\r\n    const optionName = /** @type {'initialZoomLevel' | 'secondaryZoomLevel' | 'maxZoomLevel'} */ (\r\n      optionPrefix + 'ZoomLevel'\r\n    );\r\n    const optionValue = this.options[optionName];\r\n\r\n    if (!optionValue) {\r\n      return;\r\n    }\r\n\r\n    if (typeof optionValue === 'function') {\r\n      return optionValue(this);\r\n    }\r\n\r\n    if (optionValue === 'fill') {\r\n      return this.fill;\r\n    }\r\n\r\n    if (optionValue === 'fit') {\r\n      return this.fit;\r\n    }\r\n\r\n    return Number(optionValue);\r\n  }\r\n\r\n  /**\r\n   * Get zoom level to which image will be zoomed after double-tap gesture,\r\n   * or when user clicks on zoom icon,\r\n   * or mouse-click on image itself.\r\n   * If you return 1 image will be zoomed to its original size.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getSecondary() {\r\n    let currZoomLevel = this._parseZoomLevelOption('secondary');\r\n\r\n    if (currZoomLevel) {\r\n      return currZoomLevel;\r\n    }\r\n\r\n    // 3x of \"fit\" state, but not larger than original\r\n    currZoomLevel = Math.min(1, this.fit * 3);\r\n\r\n    if (this.elementSize && currZoomLevel * this.elementSize.x > MAX_IMAGE_WIDTH) {\r\n      currZoomLevel = MAX_IMAGE_WIDTH / this.elementSize.x;\r\n    }\r\n\r\n    return currZoomLevel;\r\n  }\r\n\r\n  /**\r\n   * Get initial image zoom level.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getInitial() {\r\n    return this._parseZoomLevelOption('initial') || this.fit;\r\n  }\r\n\r\n  /**\r\n   * Maximum zoom level when user zooms\r\n   * via zoom/pinch gesture,\r\n   * via cmd/ctrl-wheel or via trackpad.\r\n   *\r\n   * @private\r\n   * @return {number}\r\n   */\r\n  _getMax() {\r\n    // max zoom level is x4 from \"fit state\",\r\n    // used for zoom gesture and ctrl/trackpad zoom\r\n    return this._parseZoomLevelOption('max') || Math.max(1, this.fit * 4);\r\n  }\r\n}\r\n\r\nexport default ZoomLevel;\r\n", "/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/**\r\n * @typedef {_SlideData & Record<string, any>} SlideData\r\n * @typedef {Object} _SlideData\r\n * @prop {HTMLElement} [element] thumbnail element\r\n * @prop {string} [src] image URL\r\n * @prop {string} [srcset] image srcset\r\n * @prop {number} [w] image width (deprecated)\r\n * @prop {number} [h] image height (deprecated)\r\n * @prop {number} [width] image width\r\n * @prop {number} [height] image height\r\n * @prop {string} [msrc] placeholder image URL that's displayed before large image is loaded\r\n * @prop {string} [alt] image alt text\r\n * @prop {boolean} [thumbCropped] whether thumbnail is cropped client-side or not\r\n * @prop {string} [html] html content of a slide\r\n * @prop {'image' | 'html' | string} [type] slide type\r\n */\r\n\r\nimport {\r\n  createElement,\r\n  setTransform,\r\n  equalizePoints,\r\n  roundPoint,\r\n  toTransformString,\r\n  clamp,\r\n} from '../util/util.js';\r\n\r\nimport PanBounds from './pan-bounds.js';\r\nimport ZoomLevel from './zoom-level.js';\r\nimport { getPanAreaSize } from '../util/viewport-size.js';\r\n\r\n/**\r\n * Renders and allows to control a single slide\r\n */\r\nclass Slide {\r\n  /**\r\n   * @param {SlideData} data\r\n   * @param {number} index\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(data, index, pswp) {\r\n    this.data = data;\r\n    this.index = index;\r\n    this.pswp = pswp;\r\n    this.isActive = (index === pswp.currIndex);\r\n    this.currentResolution = 0;\r\n    /** @type {Point} */\r\n    this.panAreaSize = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.pan = { x: 0, y: 0 };\r\n\r\n    this.isFirstSlide = (this.isActive && !pswp.opener.isOpen);\r\n\r\n    this.zoomLevels = new ZoomLevel(pswp.options, data, index, pswp);\r\n\r\n    this.pswp.dispatch('gettingData', {\r\n      slide: this,\r\n      data: this.data,\r\n      index\r\n    });\r\n\r\n    this.content = this.pswp.contentLoader.getContentBySlide(this);\r\n    this.container = createElement('pswp__zoom-wrap', 'div');\r\n    /** @type {HTMLElement | null} */\r\n    this.holderElement = null;\r\n\r\n    this.currZoomLevel = 1;\r\n    /** @type {number} */\r\n    this.width = this.content.width;\r\n    /** @type {number} */\r\n    this.height = this.content.height;\r\n    this.heavyAppended = false;\r\n    this.bounds = new PanBounds(this);\r\n\r\n    this.prevDisplayedWidth = -1;\r\n    this.prevDisplayedHeight = -1;\r\n\r\n    this.pswp.dispatch('slideInit', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * If this slide is active/current/visible\r\n   *\r\n   * @param {boolean} isActive\r\n   */\r\n  setIsActive(isActive) {\r\n    if (isActive && !this.isActive) {\r\n      // slide just became active\r\n      this.activate();\r\n    } else if (!isActive && this.isActive) {\r\n      // slide just became non-active\r\n      this.deactivate();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Appends slide content to DOM\r\n   *\r\n   * @param {HTMLElement} holderElement\r\n   */\r\n  append(holderElement) {\r\n    this.holderElement = holderElement;\r\n\r\n    this.container.style.transformOrigin = '0 0';\r\n\r\n    // Slide appended to DOM\r\n    if (!this.data) {\r\n      return;\r\n    }\r\n\r\n    this.calculateSize();\r\n\r\n    this.load();\r\n    this.updateContentSize();\r\n    this.appendHeavy();\r\n\r\n    this.holderElement.appendChild(this.container);\r\n\r\n    this.zoomAndPanToInitial();\r\n\r\n    this.pswp.dispatch('firstZoomPan', { slide: this });\r\n\r\n    this.applyCurrentZoomPan();\r\n\r\n    this.pswp.dispatch('afterSetContent', { slide: this });\r\n\r\n    if (this.isActive) {\r\n      this.activate();\r\n    }\r\n  }\r\n\r\n  load() {\r\n    this.content.load(false);\r\n    this.pswp.dispatch('slideLoad', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Append \"heavy\" DOM elements\r\n   *\r\n   * This may depend on a type of slide,\r\n   * but generally these are large images.\r\n   */\r\n  appendHeavy() {\r\n    const { pswp } = this;\r\n    const appendHeavyNearby = true; // todo\r\n\r\n    // Avoid appending heavy elements during animations\r\n    if (this.heavyAppended\r\n        || !pswp.opener.isOpen\r\n        || pswp.mainScroll.isShifted()\r\n        || (!this.isActive && !appendHeavyNearby)) {\r\n      return;\r\n    }\r\n\r\n    if (this.pswp.dispatch('appendHeavy', { slide: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.heavyAppended = true;\r\n\r\n    this.content.append();\r\n\r\n    this.pswp.dispatch('appendHeavyContent', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Triggered when this slide is active (selected).\r\n   *\r\n   * If it's part of opening/closing transition -\r\n   * activate() will trigger after the transition is ended.\r\n   */\r\n  activate() {\r\n    this.isActive = true;\r\n    this.appendHeavy();\r\n    this.content.activate();\r\n    this.pswp.dispatch('slideActivate', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Triggered when this slide becomes inactive.\r\n   *\r\n   * Slide can become inactive only after it was active.\r\n   */\r\n  deactivate() {\r\n    this.isActive = false;\r\n    this.content.deactivate();\r\n\r\n    if (this.currZoomLevel !== this.zoomLevels.initial) {\r\n      // allow filtering\r\n      this.calculateSize();\r\n    }\r\n\r\n    // reset zoom level\r\n    this.currentResolution = 0;\r\n    this.zoomAndPanToInitial();\r\n    this.applyCurrentZoomPan();\r\n    this.updateContentSize();\r\n\r\n    this.pswp.dispatch('slideDeactivate', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * The slide should destroy itself, it will never be used again.\r\n   * (unbind all events and destroy internal components)\r\n   */\r\n  destroy() {\r\n    this.content.hasSlide = false;\r\n    this.content.remove();\r\n    this.container.remove();\r\n    this.pswp.dispatch('slideDestroy', { slide: this });\r\n  }\r\n\r\n  resize() {\r\n    if (this.currZoomLevel === this.zoomLevels.initial || !this.isActive) {\r\n      // Keep initial zoom level if it was before the resize,\r\n      // as well as when this slide is not active\r\n\r\n      // Reset position and scale to original state\r\n      this.calculateSize();\r\n      this.currentResolution = 0;\r\n      this.zoomAndPanToInitial();\r\n      this.applyCurrentZoomPan();\r\n      this.updateContentSize();\r\n    } else {\r\n      // readjust pan position if it's beyond the bounds\r\n      this.calculateSize();\r\n      this.bounds.update(this.currZoomLevel);\r\n      this.panTo(this.pan.x, this.pan.y);\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Apply size to current slide content,\r\n   * based on the current resolution and scale.\r\n   *\r\n   * @param {boolean} [force] if size should be updated even if dimensions weren't changed\r\n   */\r\n  updateContentSize(force) {\r\n    // Use initial zoom level\r\n    // if resolution is not defined (user didn't zoom yet)\r\n    const scaleMultiplier = this.currentResolution || this.zoomLevels.initial;\r\n\r\n    if (!scaleMultiplier) {\r\n      return;\r\n    }\r\n\r\n    const width = Math.round(this.width * scaleMultiplier) || this.pswp.viewportSize.x;\r\n    const height = Math.round(this.height * scaleMultiplier) || this.pswp.viewportSize.y;\r\n\r\n    if (!this.sizeChanged(width, height) && !force) {\r\n      return;\r\n    }\r\n    this.content.setDisplayedSize(width, height);\r\n  }\r\n\r\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\r\n  sizeChanged(width, height) {\r\n    if (width !== this.prevDisplayedWidth\r\n        || height !== this.prevDisplayedHeight) {\r\n      this.prevDisplayedWidth = width;\r\n      this.prevDisplayedHeight = height;\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  /** @returns {HTMLImageElement | HTMLDivElement | null | undefined} */\r\n  getPlaceholderElement() {\r\n    return this.content.placeholder?.element;\r\n  }\r\n\r\n  /**\r\n   * Zoom current slide image to...\r\n   *\r\n   * @param {number} destZoomLevel Destination zoom level.\r\n   * @param {Point} [centerPoint]\r\n   * Transform origin center point, or false if viewport center should be used.\r\n   * @param {number | false} [transitionDuration] Transition duration, may be set to 0.\r\n   * @param {boolean} [ignoreBounds] Minimum and maximum zoom levels will be ignored.\r\n   */\r\n  zoomTo(destZoomLevel, centerPoint, transitionDuration, ignoreBounds) {\r\n    const { pswp } = this;\r\n    if (!this.isZoomable()\r\n        || pswp.mainScroll.isShifted()) {\r\n      return;\r\n    }\r\n\r\n    pswp.dispatch('beforeZoomTo', {\r\n      destZoomLevel, centerPoint, transitionDuration\r\n    });\r\n\r\n    // stop all pan and zoom transitions\r\n    pswp.animations.stopAllPan();\r\n\r\n    // if (!centerPoint) {\r\n    //   centerPoint = pswp.getViewportCenterPoint();\r\n    // }\r\n\r\n    const prevZoomLevel = this.currZoomLevel;\r\n\r\n    if (!ignoreBounds) {\r\n      destZoomLevel = clamp(destZoomLevel, this.zoomLevels.min, this.zoomLevels.max);\r\n    }\r\n\r\n    // if (transitionDuration === undefined) {\r\n    //   transitionDuration = this.pswp.options.zoomAnimationDuration;\r\n    // }\r\n\r\n    this.setZoomLevel(destZoomLevel);\r\n    this.pan.x = this.calculateZoomToPanOffset('x', centerPoint, prevZoomLevel);\r\n    this.pan.y = this.calculateZoomToPanOffset('y', centerPoint, prevZoomLevel);\r\n    roundPoint(this.pan);\r\n\r\n    const finishTransition = () => {\r\n      this._setResolution(destZoomLevel);\r\n      this.applyCurrentZoomPan();\r\n    };\r\n\r\n    if (!transitionDuration) {\r\n      finishTransition();\r\n    } else {\r\n      pswp.animations.startTransition({\r\n        isPan: true,\r\n        name: 'zoomTo',\r\n        target: this.container,\r\n        transform: this.getCurrentTransform(),\r\n        onComplete: finishTransition,\r\n        duration: transitionDuration,\r\n        easing: pswp.options.easing\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} [centerPoint]\r\n   */\r\n  toggleZoom(centerPoint) {\r\n    this.zoomTo(\r\n      this.currZoomLevel === this.zoomLevels.initial\r\n        ? this.zoomLevels.secondary : this.zoomLevels.initial,\r\n      centerPoint,\r\n      this.pswp.options.zoomAnimationDuration\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Updates zoom level property and recalculates new pan bounds,\r\n   * unlike zoomTo it does not apply transform (use applyCurrentZoomPan)\r\n   *\r\n   * @param {number} currZoomLevel\r\n   */\r\n  setZoomLevel(currZoomLevel) {\r\n    this.currZoomLevel = currZoomLevel;\r\n    this.bounds.update(this.currZoomLevel);\r\n  }\r\n\r\n  /**\r\n   * Get pan position after zoom at a given `point`.\r\n   *\r\n   * Always call setZoomLevel(newZoomLevel) beforehand to recalculate\r\n   * pan bounds according to the new zoom level.\r\n   *\r\n   * @param {'x' | 'y'} axis\r\n   * @param {Point} [point]\r\n   * point based on which zoom is performed, usually refers to the current mouse position,\r\n   * if false - viewport center will be used.\r\n   * @param {number} [prevZoomLevel] Zoom level before new zoom was applied.\r\n   * @returns {number}\r\n   */\r\n  calculateZoomToPanOffset(axis, point, prevZoomLevel) {\r\n    const totalPanDistance = this.bounds.max[axis] - this.bounds.min[axis];\r\n    if (totalPanDistance === 0) {\r\n      return this.bounds.center[axis];\r\n    }\r\n\r\n    if (!point) {\r\n      point = this.pswp.getViewportCenterPoint();\r\n    }\r\n\r\n    if (!prevZoomLevel) {\r\n      prevZoomLevel = this.zoomLevels.initial;\r\n    }\r\n\r\n    const zoomFactor = this.currZoomLevel / prevZoomLevel;\r\n    return this.bounds.correctPan(\r\n      axis,\r\n      (this.pan[axis] - point[axis]) * zoomFactor + point[axis]\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Apply pan and keep it within bounds.\r\n   *\r\n   * @param {number} panX\r\n   * @param {number} panY\r\n   */\r\n  panTo(panX, panY) {\r\n    this.pan.x = this.bounds.correctPan('x', panX);\r\n    this.pan.y = this.bounds.correctPan('y', panY);\r\n    this.applyCurrentZoomPan();\r\n  }\r\n\r\n  /**\r\n   * If the slide in the current state can be panned by the user\r\n   * @returns {boolean}\r\n   */\r\n  isPannable() {\r\n    return Boolean(this.width) && (this.currZoomLevel > this.zoomLevels.fit);\r\n  }\r\n\r\n  /**\r\n   * If the slide can be zoomed\r\n   * @returns {boolean}\r\n   */\r\n  isZoomable() {\r\n    return Boolean(this.width) && this.content.isZoomable();\r\n  }\r\n\r\n  /**\r\n   * Apply transform and scale based on\r\n   * the current pan position (this.pan) and zoom level (this.currZoomLevel)\r\n   */\r\n  applyCurrentZoomPan() {\r\n    this._applyZoomTransform(this.pan.x, this.pan.y, this.currZoomLevel);\r\n    if (this === this.pswp.currSlide) {\r\n      this.pswp.dispatch('zoomPanUpdate', { slide: this });\r\n    }\r\n  }\r\n\r\n  zoomAndPanToInitial() {\r\n    this.currZoomLevel = this.zoomLevels.initial;\r\n\r\n    // pan according to the zoom level\r\n    this.bounds.update(this.currZoomLevel);\r\n    equalizePoints(this.pan, this.bounds.center);\r\n    this.pswp.dispatch('initialZoomPan', { slide: this });\r\n  }\r\n\r\n  /**\r\n   * Set translate and scale based on current resolution\r\n   *\r\n   * @param {number} x\r\n   * @param {number} y\r\n   * @param {number} zoom\r\n   * @private\r\n   */\r\n  _applyZoomTransform(x, y, zoom) {\r\n    zoom /= this.currentResolution || this.zoomLevels.initial;\r\n    setTransform(this.container, x, y, zoom);\r\n  }\r\n\r\n  calculateSize() {\r\n    const { pswp } = this;\r\n\r\n    equalizePoints(\r\n      this.panAreaSize,\r\n      getPanAreaSize(pswp.options, pswp.viewportSize, this.data, this.index)\r\n    );\r\n\r\n    this.zoomLevels.update(this.width, this.height, this.panAreaSize);\r\n\r\n    pswp.dispatch('calcSlideSize', {\r\n      slide: this\r\n    });\r\n  }\r\n\r\n  /** @returns {string} */\r\n  getCurrentTransform() {\r\n    const scale = this.currZoomLevel / (this.currentResolution || this.zoomLevels.initial);\r\n    return toTransformString(this.pan.x, this.pan.y, scale);\r\n  }\r\n\r\n  /**\r\n   * Set resolution and re-render the image.\r\n   *\r\n   * For example, if the real image size is 2000x1500,\r\n   * and resolution is 0.5 - it will be rendered as 1000x750.\r\n   *\r\n   * Image with zoom level 2 and resolution 0.5 is\r\n   * the same as image with zoom level 1 and resolution 1.\r\n   *\r\n   * Used to optimize animations and make\r\n   * sure that browser renders image in the highest quality.\r\n   * Also used by responsive images to load the correct one.\r\n   *\r\n   * @param {number} newResolution\r\n   */\r\n  _setResolution(newResolution) {\r\n    if (newResolution === this.currentResolution) {\r\n      return;\r\n    }\r\n\r\n    this.currentResolution = newResolution;\r\n    this.updateContentSize();\r\n\r\n    this.pswp.dispatch('resolutionChanged');\r\n  }\r\n}\r\n\r\nexport default Slide;\r\n", "import {\r\n  equalizePoints, roundPoint, clamp\r\n} from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n\r\nconst PAN_END_FRICTION = 0.35;\r\nconst VERTICAL_DRAG_FRICTION = 0.6;\r\n\r\n// 1 corresponds to the third of viewport height\r\nconst MIN_RATIO_TO_CLOSE = 0.4;\r\n\r\n// Minimum speed required to navigate\r\n// to next or previous slide\r\nconst MIN_NEXT_SLIDE_SPEED = 0.5;\r\n\r\n/**\r\n * @param {number} initialVelocity\r\n * @param {number} decelerationRate\r\n * @returns {number}\r\n */\r\nfunction project(initialVelocity, decelerationRate) {\r\n  return initialVelocity * decelerationRate / (1 - decelerationRate);\r\n}\r\n\r\n/**\r\n * Handles single pointer dragging\r\n */\r\nclass DragHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n    this.pswp = gestures.pswp;\r\n    /** @type {Point} */\r\n    this.startPan = { x: 0, y: 0 };\r\n  }\r\n\r\n  start() {\r\n    if (this.pswp.currSlide) {\r\n      equalizePoints(this.startPan, this.pswp.currSlide.pan);\r\n    }\r\n    this.pswp.animations.stopAll();\r\n  }\r\n\r\n  change() {\r\n    const { p1, prevP1, dragAxis } = this.gestures;\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (dragAxis === 'y'\r\n        && this.pswp.options.closeOnVerticalDrag\r\n        && (currSlide && currSlide.currZoomLevel <= currSlide.zoomLevels.fit)\r\n        && !this.gestures.isMultitouch) {\r\n      // Handle vertical drag to close\r\n      const panY = currSlide.pan.y + (p1.y - prevP1.y);\r\n      if (!this.pswp.dispatch('verticalDrag', { panY }).defaultPrevented) {\r\n        this._setPanWithFriction('y', panY, VERTICAL_DRAG_FRICTION);\r\n        const bgOpacity = 1 - Math.abs(this._getVerticalDragRatio(currSlide.pan.y));\r\n        this.pswp.applyBgOpacity(bgOpacity);\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    } else {\r\n      const mainScrollChanged = this._panOrMoveMainScroll('x');\r\n      if (!mainScrollChanged) {\r\n        this._panOrMoveMainScroll('y');\r\n\r\n        if (currSlide) {\r\n          roundPoint(currSlide.pan);\r\n          currSlide.applyCurrentZoomPan();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  end() {\r\n    const { velocity } = this.gestures;\r\n    const { mainScroll, currSlide } = this.pswp;\r\n    let indexDiff = 0;\r\n\r\n    this.pswp.animations.stopAll();\r\n\r\n    // Handle main scroll if it's shifted\r\n    if (mainScroll.isShifted()) {\r\n      // Position of the main scroll relative to the viewport\r\n      const mainScrollShiftDiff = mainScroll.x - mainScroll.getCurrSlideX();\r\n\r\n      // Ratio between 0 and 1:\r\n      // 0 - slide is not visible at all,\r\n      // 0.5 - half of the slide is visible\r\n      // 1 - slide is fully visible\r\n      const currentSlideVisibilityRatio = (mainScrollShiftDiff / this.pswp.viewportSize.x);\r\n\r\n      // Go next slide.\r\n      //\r\n      // - if velocity and its direction is matched,\r\n      //   and we see at least tiny part of the next slide\r\n      //\r\n      // - or if we see less than 50% of the current slide\r\n      //   and velocity is close to 0\r\n      //\r\n      if ((velocity.x < -MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio < 0)\r\n          || (velocity.x < 0.1 && currentSlideVisibilityRatio < -0.5)) {\r\n        // Go to next slide\r\n        indexDiff = 1;\r\n        velocity.x = Math.min(velocity.x, 0);\r\n      } else if ((velocity.x > MIN_NEXT_SLIDE_SPEED && currentSlideVisibilityRatio > 0)\r\n          || (velocity.x > -0.1 && currentSlideVisibilityRatio > 0.5)) {\r\n        // Go to prev slide\r\n        indexDiff = -1;\r\n        velocity.x = Math.max(velocity.x, 0);\r\n      }\r\n\r\n      mainScroll.moveIndexBy(indexDiff, true, velocity.x);\r\n    }\r\n\r\n    // Restore zoom level\r\n    if ((currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.max)\r\n        || this.gestures.isMultitouch) {\r\n      this.gestures.zoomLevels.correctZoomPan(true);\r\n    } else {\r\n      // we run two animations instead of one,\r\n      // as each axis has own pan boundaries and thus different spring function\r\n      // (correctZoomPan does not have this functionality,\r\n      //  it animates all properties with single timing function)\r\n      this._finishPanGestureForAxis('x');\r\n      this._finishPanGestureForAxis('y');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   */\r\n  _finishPanGestureForAxis(axis) {\r\n    const { velocity } = this.gestures;\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const { pan, bounds } = currSlide;\r\n    const panPos = pan[axis];\r\n    const restoreBgOpacity = (this.pswp.bgOpacity < 1 && axis === 'y');\r\n\r\n    // 0.995 means - scroll view loses 0.5% of its velocity per millisecond\r\n    // Increasing this number will reduce travel distance\r\n    const decelerationRate = 0.995; // 0.99\r\n\r\n    // Pan position if there is no bounds\r\n    const projectedPosition = panPos + project(velocity[axis], decelerationRate);\r\n\r\n    if (restoreBgOpacity) {\r\n      const vDragRatio = this._getVerticalDragRatio(panPos);\r\n      const projectedVDragRatio = this._getVerticalDragRatio(projectedPosition);\r\n\r\n      // If we are above and moving upwards,\r\n      // or if we are below and moving downwards\r\n      if ((vDragRatio < 0 && projectedVDragRatio < -MIN_RATIO_TO_CLOSE)\r\n          || (vDragRatio > 0 && projectedVDragRatio > MIN_RATIO_TO_CLOSE)) {\r\n        this.pswp.close();\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Pan position with corrected bounds\r\n    const correctedPanPosition = bounds.correctPan(axis, projectedPosition);\r\n\r\n    // Exit if pan position should not be changed\r\n    // or if speed it too low\r\n    if (panPos === correctedPanPosition) {\r\n      return;\r\n    }\r\n\r\n    // Overshoot if the final position is out of pan bounds\r\n    const dampingRatio = (correctedPanPosition === projectedPosition) ? 1 : 0.82;\r\n\r\n    const initialBgOpacity = this.pswp.bgOpacity;\r\n    const totalPanDist = correctedPanPosition - panPos;\r\n\r\n    this.pswp.animations.startSpring({\r\n      name: 'panGesture' + axis,\r\n      isPan: true,\r\n      start: panPos,\r\n      end: correctedPanPosition,\r\n      velocity: velocity[axis],\r\n      dampingRatio,\r\n      onUpdate: (pos) => {\r\n        // Animate opacity of background relative to Y pan position of an image\r\n        if (restoreBgOpacity && this.pswp.bgOpacity < 1) {\r\n          // 0 - start of animation, 1 - end of animation\r\n          const animationProgressRatio = 1 - (correctedPanPosition - pos) / totalPanDist;\r\n\r\n          // We clamp opacity to keep it between 0 and 1.\r\n          // As progress ratio can be larger than 1 due to overshoot,\r\n          // and we do not want to bounce opacity.\r\n          this.pswp.applyBgOpacity(clamp(\r\n            initialBgOpacity + (1 - initialBgOpacity) * animationProgressRatio,\r\n            0,\r\n            1\r\n          ));\r\n        }\r\n\r\n        pan[axis] = Math.floor(pos);\r\n        currSlide.applyCurrentZoomPan();\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Update position of the main scroll,\r\n   * or/and update pan position of the current slide.\r\n   *\r\n   * Should return true if it changes (or can change) main scroll.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @returns {boolean}\r\n   */\r\n  _panOrMoveMainScroll(axis) {\r\n    const { p1, dragAxis, prevP1, isMultitouch } = this.gestures;\r\n    const { currSlide, mainScroll } = this.pswp;\r\n    const delta = (p1[axis] - prevP1[axis]);\r\n    const newMainScrollX = mainScroll.x + delta;\r\n\r\n    if (!delta || !currSlide) {\r\n      return false;\r\n    }\r\n\r\n    // Always move main scroll if image can not be panned\r\n    if (axis === 'x' && !currSlide.isPannable() && !isMultitouch) {\r\n      mainScroll.moveTo(newMainScrollX, true);\r\n      return true; // changed main scroll\r\n    }\r\n\r\n    const { bounds } = currSlide;\r\n    const newPan = currSlide.pan[axis] + delta;\r\n\r\n    if (this.pswp.options.allowPanToNext\r\n        && dragAxis === 'x'\r\n        && axis === 'x'\r\n        && !isMultitouch) {\r\n      const currSlideMainScrollX = mainScroll.getCurrSlideX();\r\n\r\n      // Position of the main scroll relative to the viewport\r\n      const mainScrollShiftDiff = mainScroll.x - currSlideMainScrollX;\r\n\r\n      const isLeftToRight = delta > 0;\r\n      const isRightToLeft = !isLeftToRight;\r\n\r\n      if (newPan > bounds.min[axis] && isLeftToRight) {\r\n        // Panning from left to right, beyond the left edge\r\n\r\n        // Wether the image was at minimum pan position (or less)\r\n        // when this drag gesture started.\r\n        // Minimum pan position refers to the left edge of the image.\r\n        const wasAtMinPanPosition = (bounds.min[axis] <= this.startPan[axis]);\r\n\r\n        if (wasAtMinPanPosition) {\r\n          mainScroll.moveTo(newMainScrollX, true);\r\n          return true;\r\n        } else {\r\n          this._setPanWithFriction(axis, newPan);\r\n          //currSlide.pan[axis] = newPan;\r\n        }\r\n      } else if (newPan < bounds.max[axis] && isRightToLeft) {\r\n        // Paning from right to left, beyond the right edge\r\n\r\n        // Maximum pan position refers to the right edge of the image.\r\n        const wasAtMaxPanPosition = (this.startPan[axis] <= bounds.max[axis]);\r\n\r\n        if (wasAtMaxPanPosition) {\r\n          mainScroll.moveTo(newMainScrollX, true);\r\n          return true;\r\n        } else {\r\n          this._setPanWithFriction(axis, newPan);\r\n          //currSlide.pan[axis] = newPan;\r\n        }\r\n      } else {\r\n        // If main scroll is shifted\r\n        if (mainScrollShiftDiff !== 0) {\r\n          // If main scroll is shifted right\r\n          if (mainScrollShiftDiff > 0 /*&& isRightToLeft*/) {\r\n            mainScroll.moveTo(Math.max(newMainScrollX, currSlideMainScrollX), true);\r\n            return true;\r\n          } else if (mainScrollShiftDiff < 0 /*&& isLeftToRight*/) {\r\n            // Main scroll is shifted left (Position is less than 0 comparing to the viewport 0)\r\n            mainScroll.moveTo(Math.min(newMainScrollX, currSlideMainScrollX), true);\r\n            return true;\r\n          }\r\n        } else {\r\n          // We are within pan bounds, so just pan\r\n          this._setPanWithFriction(axis, newPan);\r\n        }\r\n      }\r\n    } else {\r\n      if (axis === 'y') {\r\n        // Do not pan vertically if main scroll is shifted o\r\n        if (!mainScroll.isShifted() && bounds.min.y !== bounds.max.y) {\r\n          this._setPanWithFriction(axis, newPan);\r\n        }\r\n      } else {\r\n        this._setPanWithFriction(axis, newPan);\r\n      }\r\n    }\r\n\r\n    return false;\r\n  }\r\n\r\n  // If we move above - the ratio is negative\r\n  // If we move below the ratio is positive\r\n\r\n  /**\r\n   * Relation between pan Y position and third of viewport height.\r\n   *\r\n   * When we are at initial position (center bounds) - the ratio is 0,\r\n   * if position is shifted upwards - the ratio is negative,\r\n   * if position is shifted downwards - the ratio is positive.\r\n   *\r\n   * @private\r\n   * @param {number} panY The current pan Y position.\r\n   * @returns {number}\r\n   */\r\n  _getVerticalDragRatio(panY) {\r\n    return (panY - (this.pswp.currSlide?.bounds.center.y ?? 0)) / (this.pswp.viewportSize.y / 3);\r\n  }\r\n\r\n  /**\r\n   * Set pan position of the current slide.\r\n   * Apply friction if the position is beyond the pan bounds,\r\n   * or if custom friction is defined.\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} potentialPan\r\n   * @param {number} [customFriction] (0.1 - 1)\r\n   */\r\n  _setPanWithFriction(axis, potentialPan, customFriction) {\r\n    const { currSlide } = this.pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const { pan, bounds } = currSlide;\r\n    const correctedPan = bounds.correctPan(axis, potentialPan);\r\n    // If we are out of pan bounds\r\n    if (correctedPan !== potentialPan || customFriction) {\r\n      const delta = Math.round(potentialPan - pan[axis]);\r\n      pan[axis] += delta * (customFriction || PAN_END_FRICTION);\r\n    } else {\r\n      pan[axis] = potentialPan;\r\n    }\r\n  }\r\n}\r\n\r\nexport default DragHandler;\r\n", "import {\r\n  equalizePoints, getDistanceBetween, clamp, pointsEqual\r\n} from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n\r\nconst UPPER_ZOOM_FRICTION = 0.05;\r\nconst LOWER_ZOOM_FRICTION = 0.15;\r\n\r\n\r\n/**\r\n * Get center point between two points\r\n *\r\n * @param {Point} p\r\n * @param {Point} p1\r\n * @param {Point} p2\r\n * @returns {Point}\r\n */\r\nfunction getZoomPointsCenter(p, p1, p2) {\r\n  p.x = (p1.x + p2.x) / 2;\r\n  p.y = (p1.y + p2.y) / 2;\r\n  return p;\r\n}\r\n\r\nclass ZoomHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._startPan = { x: 0, y: 0 };\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._startZoomPoint = { x: 0, y: 0 };\r\n    /**\r\n     * @private\r\n     * @type {Point}\r\n     */\r\n    this._zoomPoint = { x: 0, y: 0 };\r\n    /** @private */\r\n    this._wasOverFitZoomLevel = false;\r\n    /** @private */\r\n    this._startZoomLevel = 1;\r\n  }\r\n\r\n  start() {\r\n    const { currSlide } = this.gestures.pswp;\r\n    if (currSlide) {\r\n      this._startZoomLevel = currSlide.currZoomLevel;\r\n      equalizePoints(this._startPan, currSlide.pan);\r\n    }\r\n\r\n    this.gestures.pswp.animations.stopAllPan();\r\n    this._wasOverFitZoomLevel = false;\r\n  }\r\n\r\n  change() {\r\n    const { p1, startP1, p2, startP2, pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    const minZoomLevel = currSlide.zoomLevels.min;\r\n    const maxZoomLevel = currSlide.zoomLevels.max;\r\n\r\n    if (!currSlide.isZoomable() || pswp.mainScroll.isShifted()) {\r\n      return;\r\n    }\r\n\r\n    getZoomPointsCenter(this._startZoomPoint, startP1, startP2);\r\n    getZoomPointsCenter(this._zoomPoint, p1, p2);\r\n\r\n    let currZoomLevel = (1 / getDistanceBetween(startP1, startP2))\r\n                      * getDistanceBetween(p1, p2)\r\n                      * this._startZoomLevel;\r\n\r\n    // slightly over the zoom.fit\r\n    if (currZoomLevel > currSlide.zoomLevels.initial + (currSlide.zoomLevels.initial / 15)) {\r\n      this._wasOverFitZoomLevel = true;\r\n    }\r\n\r\n    if (currZoomLevel < minZoomLevel) {\r\n      if (pswp.options.pinchToClose\r\n          && !this._wasOverFitZoomLevel\r\n          && this._startZoomLevel <= currSlide.zoomLevels.initial) {\r\n        // fade out background if zooming out\r\n        const bgOpacity = 1 - ((minZoomLevel - currZoomLevel) / (minZoomLevel / 1.2));\r\n        if (!pswp.dispatch('pinchClose', { bgOpacity }).defaultPrevented) {\r\n          pswp.applyBgOpacity(bgOpacity);\r\n        }\r\n      } else {\r\n        // Apply the friction if zoom level is below the min\r\n        currZoomLevel = minZoomLevel - (minZoomLevel - currZoomLevel) * LOWER_ZOOM_FRICTION;\r\n      }\r\n    } else if (currZoomLevel > maxZoomLevel) {\r\n      // Apply the friction if zoom level is above the max\r\n      currZoomLevel = maxZoomLevel + (currZoomLevel - maxZoomLevel) * UPPER_ZOOM_FRICTION;\r\n    }\r\n\r\n    currSlide.pan.x = this._calculatePanForZoomLevel('x', currZoomLevel);\r\n    currSlide.pan.y = this._calculatePanForZoomLevel('y', currZoomLevel);\r\n\r\n    currSlide.setZoomLevel(currZoomLevel);\r\n    currSlide.applyCurrentZoomPan();\r\n  }\r\n\r\n  end() {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n    if ((!currSlide || currSlide.currZoomLevel < currSlide.zoomLevels.initial)\r\n        && !this._wasOverFitZoomLevel\r\n        && pswp.options.pinchToClose) {\r\n      pswp.close();\r\n    } else {\r\n      this.correctZoomPan();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} currZoomLevel\r\n   * @returns {number}\r\n   */\r\n  _calculatePanForZoomLevel(axis, currZoomLevel) {\r\n    const zoomFactor = currZoomLevel / this._startZoomLevel;\r\n    return this._zoomPoint[axis]\r\n            - ((this._startZoomPoint[axis] - this._startPan[axis]) * zoomFactor);\r\n  }\r\n\r\n  /**\r\n   * Correct currZoomLevel and pan if they are\r\n   * beyond minimum or maximum values.\r\n   * With animation.\r\n   *\r\n   * @param {boolean} [ignoreGesture]\r\n   * Wether gesture coordinates should be ignored when calculating destination pan position.\r\n   */\r\n  correctZoomPan(ignoreGesture) {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n\r\n    if (!currSlide?.isZoomable()) {\r\n      return;\r\n    }\r\n\r\n    if (this._zoomPoint.x === 0) {\r\n      ignoreGesture = true;\r\n    }\r\n\r\n    const prevZoomLevel = currSlide.currZoomLevel;\r\n\r\n    /** @type {number} */\r\n    let destinationZoomLevel;\r\n    let currZoomLevelNeedsChange = true;\r\n\r\n    if (prevZoomLevel < currSlide.zoomLevels.initial) {\r\n      destinationZoomLevel = currSlide.zoomLevels.initial;\r\n      // zoom to min\r\n    } else if (prevZoomLevel > currSlide.zoomLevels.max) {\r\n      destinationZoomLevel = currSlide.zoomLevels.max;\r\n      // zoom to max\r\n    } else {\r\n      currZoomLevelNeedsChange = false;\r\n      destinationZoomLevel = prevZoomLevel;\r\n    }\r\n\r\n    const initialBgOpacity = pswp.bgOpacity;\r\n    const restoreBgOpacity = pswp.bgOpacity < 1;\r\n\r\n    const initialPan = equalizePoints({ x: 0, y: 0 }, currSlide.pan);\r\n    let destinationPan = equalizePoints({ x: 0, y: 0 }, initialPan);\r\n\r\n    if (ignoreGesture) {\r\n      this._zoomPoint.x = 0;\r\n      this._zoomPoint.y = 0;\r\n      this._startZoomPoint.x = 0;\r\n      this._startZoomPoint.y = 0;\r\n      this._startZoomLevel = prevZoomLevel;\r\n      equalizePoints(this._startPan, initialPan);\r\n    }\r\n\r\n    if (currZoomLevelNeedsChange) {\r\n      destinationPan = {\r\n        x: this._calculatePanForZoomLevel('x', destinationZoomLevel),\r\n        y: this._calculatePanForZoomLevel('y', destinationZoomLevel)\r\n      };\r\n    }\r\n\r\n    // set zoom level, so pan bounds are updated according to it\r\n    currSlide.setZoomLevel(destinationZoomLevel);\r\n\r\n    destinationPan = {\r\n      x: currSlide.bounds.correctPan('x', destinationPan.x),\r\n      y: currSlide.bounds.correctPan('y', destinationPan.y)\r\n    };\r\n\r\n    // return zoom level and its bounds to initial\r\n    currSlide.setZoomLevel(prevZoomLevel);\r\n\r\n    const panNeedsChange = !pointsEqual(destinationPan, initialPan);\r\n\r\n    if (!panNeedsChange && !currZoomLevelNeedsChange && !restoreBgOpacity) {\r\n      // update resolution after gesture\r\n      currSlide._setResolution(destinationZoomLevel);\r\n      currSlide.applyCurrentZoomPan();\r\n\r\n      // nothing to animate\r\n      return;\r\n    }\r\n\r\n    pswp.animations.stopAllPan();\r\n\r\n    pswp.animations.startSpring({\r\n      isPan: true,\r\n      start: 0,\r\n      end: 1000,\r\n      velocity: 0,\r\n      dampingRatio: 1,\r\n      naturalFrequency: 40,\r\n      onUpdate: (now) => {\r\n        now /= 1000; // 0 - start, 1 - end\r\n\r\n        if (panNeedsChange || currZoomLevelNeedsChange) {\r\n          if (panNeedsChange) {\r\n            currSlide.pan.x = initialPan.x + (destinationPan.x - initialPan.x) * now;\r\n            currSlide.pan.y = initialPan.y + (destinationPan.y - initialPan.y) * now;\r\n          }\r\n\r\n          if (currZoomLevelNeedsChange) {\r\n            const newZoomLevel = prevZoomLevel\r\n                        + (destinationZoomLevel - prevZoomLevel) * now;\r\n            currSlide.setZoomLevel(newZoomLevel);\r\n          }\r\n\r\n          currSlide.applyCurrentZoomPan();\r\n        }\r\n\r\n        // Restore background opacity\r\n        if (restoreBgOpacity && pswp.bgOpacity < 1) {\r\n          // We clamp opacity to keep it between 0 and 1.\r\n          // As progress ratio can be larger than 1 due to overshoot,\r\n          // and we do not want to bounce opacity.\r\n          pswp.applyBgOpacity(clamp(\r\n            initialBgOpacity + (1 - initialBgOpacity) * now, 0, 1\r\n          ));\r\n        }\r\n      },\r\n      onComplete: () => {\r\n        // update resolution after transition ends\r\n        currSlide._setResolution(destinationZoomLevel);\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport default ZoomHandler;\r\n", "/**\r\n * @template {string} T\r\n * @template {string} P\r\n * @typedef {import('../types.js').AddPostfix<T, P>} AddPostfix<T, P>\r\n */\r\n\r\n/** @typedef {import('./gestures.js').default} Gestures */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n/** @typedef {'imageClick' | 'bgClick' | 'tap' | 'doubleTap'} Actions */\r\n\r\n/**\r\n * Whether the tap was performed on the main slide\r\n * (rather than controls or caption).\r\n *\r\n * @param {PointerEvent} event\r\n * @returns {boolean}\r\n */\r\nfunction didTapOnMainContent(event) {\r\n  return !!(/** @type {HTMLElement} */ (event.target).closest('.pswp__container'));\r\n}\r\n\r\n/**\r\n * Tap, double-tap handler.\r\n */\r\nclass TapHandler {\r\n  /**\r\n   * @param {Gestures} gestures\r\n   */\r\n  constructor(gestures) {\r\n    this.gestures = gestures;\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  click(point, originalEvent) {\r\n    const targetClassList = /** @type {HTMLElement} */ (originalEvent.target).classList;\r\n    const isImageClick = targetClassList.contains('pswp__img');\r\n    const isBackgroundClick = targetClassList.contains('pswp__item')\r\n                              || targetClassList.contains('pswp__zoom-wrap');\r\n\r\n    if (isImageClick) {\r\n      this._doClickOrTapAction('imageClick', point, originalEvent);\r\n    } else if (isBackgroundClick) {\r\n      this._doClickOrTapAction('bgClick', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  tap(point, originalEvent) {\r\n    if (didTapOnMainContent(originalEvent)) {\r\n      this._doClickOrTapAction('tap', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  doubleTap(point, originalEvent) {\r\n    if (didTapOnMainContent(originalEvent)) {\r\n      this._doClickOrTapAction('doubleTap', point, originalEvent);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {Actions} actionName\r\n   * @param {Point} point\r\n   * @param {PointerEvent} originalEvent\r\n   */\r\n  _doClickOrTapAction(actionName, point, originalEvent) {\r\n    const { pswp } = this.gestures;\r\n    const { currSlide } = pswp;\r\n    const actionFullName = /** @type {AddPostfix<Actions, 'Action'>} */ (actionName + 'Action');\r\n    const optionValue = pswp.options[actionFullName];\r\n\r\n    if (pswp.dispatch(actionFullName, { point, originalEvent }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (typeof optionValue === 'function') {\r\n      optionValue.call(pswp, point, originalEvent);\r\n      return;\r\n    }\r\n\r\n    switch (optionValue) {\r\n      case 'close':\r\n      case 'next':\r\n        pswp[optionValue]();\r\n        break;\r\n      case 'zoom':\r\n        currSlide?.toggleZoom(point);\r\n        break;\r\n      case 'zoom-or-close':\r\n        // by default click zooms current image,\r\n        // if it can not be zoomed - gallery will be closed\r\n        if (currSlide?.isZoomable()\r\n            && currSlide.zoomLevels.secondary !== currSlide.zoomLevels.initial) {\r\n          currSlide.toggleZoom(point);\r\n        } else if (pswp.options.clickToCloseNonZoomable) {\r\n          pswp.close();\r\n        }\r\n        break;\r\n      case 'toggle-controls':\r\n        this.gestures.pswp.element?.classList.toggle('pswp--ui-visible');\r\n        // if (_controlsVisible) {\r\n        //   _ui.hideControls();\r\n        // } else {\r\n        //   _ui.showControls();\r\n        // }\r\n        break;\r\n    }\r\n  }\r\n}\r\n\r\nexport default TapHandler;\r\n", "import {\r\n  equalizePoints, pointsEqual, getDistanceBetween\r\n} from '../util/util.js';\r\n\r\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from './drag-handler.js';\r\nimport ZoomHandler from './zoom-handler.js';\r\nimport <PERSON><PERSON><PERSON>andler from './tap-handler.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').Point} Point */\r\n\r\n// How far should user should drag\r\n// until we can determine that the gesture is swipe and its direction\r\nconst AXIS_SWIPE_HYSTERISIS = 10;\r\n//const PAN_END_FRICTION = 0.35;\r\n\r\nconst DOUBLE_TAP_DELAY = 300; // ms\r\nconst MIN_TAP_DISTANCE = 25; // px\r\n\r\n/**\r\n * Gestures class bind touch, pointer or mouse events\r\n * and emits drag to drag-handler and zoom events zoom-handler.\r\n *\r\n * Drag and zoom events are emited in requestAnimationFrame,\r\n * and only when one of pointers was actually changed.\r\n */\r\nclass Gestures {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n\r\n    /** @type {'x' | 'y' | null} */\r\n    this.dragAxis = null;\r\n\r\n    // point objects are defined once and reused\r\n    // PhotoSwipe keeps track only of two pointers, others are ignored\r\n    /** @type {Point} */\r\n    this.p1 = { x: 0, y: 0 }; // the first pressed pointer\r\n    /** @type {Point} */\r\n    this.p2 = { x: 0, y: 0 }; // the second pressed pointer\r\n    /** @type {Point} */\r\n    this.prevP1 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.prevP2 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.startP1 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.startP2 = { x: 0, y: 0 };\r\n    /** @type {Point} */\r\n    this.velocity = { x: 0, y: 0 };\r\n\r\n    /** @type {Point}\r\n     * @private\r\n     */\r\n    this._lastStartP1 = { x: 0, y: 0 };\r\n    /** @type {Point}\r\n     * @private\r\n     */\r\n    this._intervalP1 = { x: 0, y: 0 };\r\n    /** @private */\r\n    this._numActivePoints = 0;\r\n    /** @type {Point[]}\r\n     * @private\r\n     */\r\n    this._ongoingPointers = [];\r\n    /** @private */\r\n    this._touchEventEnabled = 'ontouchstart' in window;\r\n    /** @private */\r\n    this._pointerEventEnabled = !!(window.PointerEvent);\r\n    this.supportsTouch = this._touchEventEnabled\r\n                          || (this._pointerEventEnabled && navigator.maxTouchPoints > 1);\r\n    /** @private */\r\n    this._numActivePoints = 0;\r\n    /** @private */\r\n    this._intervalTime = 0;\r\n    /** @private */\r\n    this._velocityCalculated = false;\r\n    this.isMultitouch = false;\r\n    this.isDragging = false;\r\n    this.isZooming = false;\r\n    /** @type {number | null} */\r\n    this.raf = null;\r\n    /** @type {NodeJS.Timeout | null}\r\n     * @private\r\n     */\r\n    this._tapTimer = null;\r\n\r\n    if (!this.supportsTouch) {\r\n      // disable pan to next slide for non-touch devices\r\n      pswp.options.allowPanToNext = false;\r\n    }\r\n\r\n    this.drag = new DragHandler(this);\r\n    this.zoomLevels = new ZoomHandler(this);\r\n    this.tapHandler = new TapHandler(this);\r\n\r\n    pswp.on('bindEvents', () => {\r\n      pswp.events.add(\r\n        pswp.scrollWrap,\r\n        'click',\r\n        /** @type EventListener */(this._onClick.bind(this))\r\n      );\r\n\r\n      if (this._pointerEventEnabled) {\r\n        this._bindEvents('pointer', 'down', 'up', 'cancel');\r\n      } else if (this._touchEventEnabled) {\r\n        this._bindEvents('touch', 'start', 'end', 'cancel');\r\n\r\n        // In previous versions we also bound mouse event here,\r\n        // in case device supports both touch and mouse events,\r\n        // but newer versions of browsers now support PointerEvent.\r\n\r\n        // on iOS10 if you bind touchmove/end after touchstart,\r\n        // and you don't preventDefault touchstart (which PhotoSwipe does),\r\n        // preventDefault will have no effect on touchmove and touchend.\r\n        // Unless you bind it previously.\r\n        if (pswp.scrollWrap) {\r\n          pswp.scrollWrap.ontouchmove = () => {};\r\n          pswp.scrollWrap.ontouchend = () => {};\r\n        }\r\n      } else {\r\n        this._bindEvents('mouse', 'down', 'up');\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {'mouse' | 'touch' | 'pointer'} pref\r\n   * @param {'down' | 'start'} down\r\n   * @param {'up' | 'end'} up\r\n   * @param {'cancel'} [cancel]\r\n   */\r\n  _bindEvents(pref, down, up, cancel) {\r\n    const { pswp } = this;\r\n    const { events } = pswp;\r\n\r\n    const cancelEvent = cancel ? pref + cancel : '';\r\n\r\n    events.add(\r\n      pswp.scrollWrap,\r\n      pref + down,\r\n      /** @type EventListener */(this.onPointerDown.bind(this))\r\n    );\r\n    events.add(window, pref + 'move', /** @type EventListener */(this.onPointerMove.bind(this)));\r\n    events.add(window, pref + up, /** @type EventListener */(this.onPointerUp.bind(this)));\r\n    if (cancelEvent) {\r\n      events.add(\r\n        pswp.scrollWrap,\r\n        cancelEvent,\r\n        /** @type EventListener */(this.onPointerUp.bind(this))\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerDown(e) {\r\n    // We do not call preventDefault for touch events\r\n    // to allow browser to show native dialog on longpress\r\n    // (the one that allows to save image or open it in new tab).\r\n    //\r\n    // Desktop Safari allows to drag images when preventDefault isn't called on mousedown,\r\n    // even though preventDefault IS called on mousemove. That's why we preventDefault mousedown.\r\n    const isMousePointer = e.type === 'mousedown' || e.pointerType === 'mouse';\r\n\r\n    // Allow dragging only via left mouse button.\r\n    // http://www.quirksmode.org/js/events_properties.html\r\n    // https://developer.mozilla.org/en-US/docs/Web/API/event.button\r\n    if (isMousePointer && e.button > 0) {\r\n      return;\r\n    }\r\n\r\n    const { pswp } = this;\r\n\r\n    // if PhotoSwipe is opening or closing\r\n    if (!pswp.opener.isOpen) {\r\n      e.preventDefault();\r\n      return;\r\n    }\r\n\r\n    if (pswp.dispatch('pointerDown', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (isMousePointer) {\r\n      pswp.mouseDetected();\r\n\r\n      // preventDefault mouse event to prevent\r\n      // browser image drag feature\r\n      this._preventPointerEventBehaviour(e, 'down');\r\n    }\r\n\r\n    pswp.animations.stopAll();\r\n\r\n    this._updatePoints(e, 'down');\r\n\r\n    if (this._numActivePoints === 1) {\r\n      this.dragAxis = null;\r\n      // we need to store initial point to determine the main axis,\r\n      // drag is activated only after the axis is determined\r\n      equalizePoints(this.startP1, this.p1);\r\n    }\r\n\r\n    if (this._numActivePoints > 1) {\r\n      // Tap or double tap should not trigger if more than one pointer\r\n      this._clearTapTimer();\r\n      this.isMultitouch = true;\r\n    } else {\r\n      this.isMultitouch = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerMove(e) {\r\n    this._preventPointerEventBehaviour(e, 'move');\r\n\r\n    if (!this._numActivePoints) {\r\n      return;\r\n    }\r\n\r\n    this._updatePoints(e, 'move');\r\n\r\n    if (this.pswp.dispatch('pointerMove', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this._numActivePoints === 1 && !this.isDragging) {\r\n      if (!this.dragAxis) {\r\n        this._calculateDragDirection();\r\n      }\r\n\r\n      // Drag axis was detected, emit drag.start\r\n      if (this.dragAxis && !this.isDragging) {\r\n        if (this.isZooming) {\r\n          this.isZooming = false;\r\n          this.zoomLevels.end();\r\n        }\r\n\r\n        this.isDragging = true;\r\n        this._clearTapTimer(); // Tap can not trigger after drag\r\n\r\n        // Adjust starting point\r\n        this._updateStartPoints();\r\n        this._intervalTime = Date.now();\r\n        //this._startTime = this._intervalTime;\r\n        this._velocityCalculated = false;\r\n        equalizePoints(this._intervalP1, this.p1);\r\n        this.velocity.x = 0;\r\n        this.velocity.y = 0;\r\n        this.drag.start();\r\n\r\n        this._rafStopLoop();\r\n        this._rafRenderLoop();\r\n      }\r\n    } else if (this._numActivePoints > 1 && !this.isZooming) {\r\n      this._finishDrag();\r\n\r\n      this.isZooming = true;\r\n\r\n      // Adjust starting points\r\n      this._updateStartPoints();\r\n\r\n      this.zoomLevels.start();\r\n\r\n      this._rafStopLoop();\r\n      this._rafRenderLoop();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _finishDrag() {\r\n    if (this.isDragging) {\r\n      this.isDragging = false;\r\n\r\n      // Try to calculate velocity,\r\n      // if it wasn't calculated yet in drag.change\r\n      if (!this._velocityCalculated) {\r\n        this._updateVelocity(true);\r\n      }\r\n\r\n      this.drag.end();\r\n      this.dragAxis = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {PointerEvent} e\r\n   */\r\n  onPointerUp(e) {\r\n    if (!this._numActivePoints) {\r\n      return;\r\n    }\r\n\r\n    this._updatePoints(e, 'up');\r\n\r\n    if (this.pswp.dispatch('pointerUp', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this._numActivePoints === 0) {\r\n      this._rafStopLoop();\r\n\r\n      if (this.isDragging) {\r\n        this._finishDrag();\r\n      } else if (!this.isZooming && !this.isMultitouch) {\r\n        //this.zoomLevels.correctZoomPan();\r\n        this._finishTap(e);\r\n      }\r\n    }\r\n\r\n    if (this._numActivePoints < 2 && this.isZooming) {\r\n      this.isZooming = false;\r\n      this.zoomLevels.end();\r\n\r\n      if (this._numActivePoints === 1) {\r\n        // Since we have 1 point left, we need to reinitiate drag\r\n        this.dragAxis = null;\r\n        this._updateStartPoints();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _rafRenderLoop() {\r\n    if (this.isDragging || this.isZooming) {\r\n      this._updateVelocity();\r\n\r\n      if (this.isDragging) {\r\n        // make sure that pointer moved since the last update\r\n        if (!pointsEqual(this.p1, this.prevP1)) {\r\n          this.drag.change();\r\n        }\r\n      } else /* if (this.isZooming) */ {\r\n        if (!pointsEqual(this.p1, this.prevP1)\r\n            || !pointsEqual(this.p2, this.prevP2)) {\r\n          this.zoomLevels.change();\r\n        }\r\n      }\r\n\r\n      this._updatePrevPoints();\r\n      this.raf = requestAnimationFrame(this._rafRenderLoop.bind(this));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update velocity at 50ms interval\r\n   *\r\n   * @private\r\n   * @param {boolean} [force]\r\n   */\r\n  _updateVelocity(force) {\r\n    const time = Date.now();\r\n    const duration = time - this._intervalTime;\r\n\r\n    if (duration < 50 && !force) {\r\n      return;\r\n    }\r\n\r\n\r\n    this.velocity.x = this._getVelocity('x', duration);\r\n    this.velocity.y = this._getVelocity('y', duration);\r\n\r\n    this._intervalTime = time;\r\n    equalizePoints(this._intervalP1, this.p1);\r\n    this._velocityCalculated = true;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\r\n  _finishTap(e) {\r\n    const { mainScroll } = this.pswp;\r\n\r\n    // Do not trigger tap events if main scroll is shifted\r\n    if (mainScroll.isShifted()) {\r\n      // restore main scroll position\r\n      // (usually happens if stopped in the middle of animation)\r\n      mainScroll.moveIndexBy(0, true);\r\n      return;\r\n    }\r\n\r\n    // Do not trigger tap for touchcancel or pointercancel\r\n    if (e.type.indexOf('cancel') > 0) {\r\n      return;\r\n    }\r\n\r\n    // Trigger click instead of tap for mouse events\r\n    if (e.type === 'mouseup' || e.pointerType === 'mouse') {\r\n      this.tapHandler.click(this.startP1, e);\r\n      return;\r\n    }\r\n\r\n    // Disable delay if there is no doubleTapAction\r\n    const tapDelay = this.pswp.options.doubleTapAction ? DOUBLE_TAP_DELAY : 0;\r\n\r\n    // If tapTimer is defined - we tapped recently,\r\n    // check if the current tap is close to the previous one,\r\n    // if yes - trigger double tap\r\n    if (this._tapTimer) {\r\n      this._clearTapTimer();\r\n      // Check if two taps were more or less on the same place\r\n      if (getDistanceBetween(this._lastStartP1, this.startP1) < MIN_TAP_DISTANCE) {\r\n        this.tapHandler.doubleTap(this.startP1, e);\r\n      }\r\n    } else {\r\n      equalizePoints(this._lastStartP1, this.startP1);\r\n      this._tapTimer = setTimeout(() => {\r\n        this.tapHandler.tap(this.startP1, e);\r\n        this._clearTapTimer();\r\n      }, tapDelay);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _clearTapTimer() {\r\n    if (this._tapTimer) {\r\n      clearTimeout(this._tapTimer);\r\n      this._tapTimer = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get velocity for axis\r\n   *\r\n   * @private\r\n   * @param {'x' | 'y'} axis\r\n   * @param {number} duration\r\n   * @returns {number}\r\n   */\r\n  _getVelocity(axis, duration) {\r\n    // displacement is like distance, but can be negative.\r\n    const displacement = this.p1[axis] - this._intervalP1[axis];\r\n\r\n    if (Math.abs(displacement) > 1 && duration > 5) {\r\n      return displacement / duration;\r\n    }\r\n\r\n    return 0;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _rafStopLoop() {\r\n    if (this.raf) {\r\n      cancelAnimationFrame(this.raf);\r\n      this.raf = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\r\n  _preventPointerEventBehaviour(e, pointerType) {\r\n    const preventPointerEvent = this.pswp.applyFilters('preventPointerEvent', true, e, pointerType);\r\n    if (preventPointerEvent) {\r\n      e.preventDefault();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses and normalizes points from the touch, mouse or pointer event.\r\n   * Updates p1 and p2.\r\n   *\r\n   * @private\r\n   * @param {PointerEvent | TouchEvent} e\r\n   * @param {'up' | 'down' | 'move'} pointerType Normalized pointer type\r\n   */\r\n  _updatePoints(e, pointerType) {\r\n    if (this._pointerEventEnabled) {\r\n      const pointerEvent = /** @type {PointerEvent} */ (e);\r\n      // Try to find the current pointer in ongoing pointers by its ID\r\n      const pointerIndex = this._ongoingPointers.findIndex((ongoingPointer) => {\r\n        return ongoingPointer.id === pointerEvent.pointerId;\r\n      });\r\n\r\n      if (pointerType === 'up' && pointerIndex > -1) {\r\n        // release the pointer - remove it from ongoing\r\n        this._ongoingPointers.splice(pointerIndex, 1);\r\n      } else if (pointerType === 'down' && pointerIndex === -1) {\r\n        // add new pointer\r\n        this._ongoingPointers.push(this._convertEventPosToPoint(pointerEvent, { x: 0, y: 0 }));\r\n      } else if (pointerIndex > -1) {\r\n        // update existing pointer\r\n        this._convertEventPosToPoint(pointerEvent, this._ongoingPointers[pointerIndex]);\r\n      }\r\n\r\n      this._numActivePoints = this._ongoingPointers.length;\r\n\r\n      // update points that PhotoSwipe uses\r\n      // to calculate position and scale\r\n      if (this._numActivePoints > 0) {\r\n        equalizePoints(this.p1, this._ongoingPointers[0]);\r\n      }\r\n\r\n      if (this._numActivePoints > 1) {\r\n        equalizePoints(this.p2, this._ongoingPointers[1]);\r\n      }\r\n    } else {\r\n      const touchEvent = /** @type {TouchEvent} */ (e);\r\n\r\n      this._numActivePoints = 0;\r\n      if (touchEvent.type.indexOf('touch') > -1) {\r\n        // Touch Event\r\n        // https://developer.mozilla.org/en-US/docs/Web/API/TouchEvent\r\n        if (touchEvent.touches && touchEvent.touches.length > 0) {\r\n          this._convertEventPosToPoint(touchEvent.touches[0], this.p1);\r\n          this._numActivePoints++;\r\n          if (touchEvent.touches.length > 1) {\r\n            this._convertEventPosToPoint(touchEvent.touches[1], this.p2);\r\n            this._numActivePoints++;\r\n          }\r\n        }\r\n      } else {\r\n        // Mouse Event\r\n        this._convertEventPosToPoint(/** @type {PointerEvent} */ (e), this.p1);\r\n        if (pointerType === 'up') {\r\n          // clear all points on mouseup\r\n          this._numActivePoints = 0;\r\n        } else {\r\n          this._numActivePoints++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /** update points that were used during previous rAF tick\r\n   * @private\r\n   */\r\n  _updatePrevPoints() {\r\n    equalizePoints(this.prevP1, this.p1);\r\n    equalizePoints(this.prevP2, this.p2);\r\n  }\r\n\r\n  /** update points at the start of gesture\r\n   * @private\r\n   */\r\n  _updateStartPoints() {\r\n    equalizePoints(this.startP1, this.p1);\r\n    equalizePoints(this.startP2, this.p2);\r\n    this._updatePrevPoints();\r\n  }\r\n\r\n  /** @private */\r\n  _calculateDragDirection() {\r\n    if (this.pswp.mainScroll.isShifted()) {\r\n      // if main scroll position is shifted – direction is always horizontal\r\n      this.dragAxis = 'x';\r\n    } else {\r\n      // calculate delta of the last touchmove tick\r\n      const diff = Math.abs(this.p1.x - this.startP1.x) - Math.abs(this.p1.y - this.startP1.y);\r\n\r\n      if (diff !== 0) {\r\n        // check if pointer was shifted horizontally or vertically\r\n        const axisToCheck = diff > 0 ? 'x' : 'y';\r\n\r\n        if (Math.abs(this.p1[axisToCheck] - this.startP1[axisToCheck]) >= AXIS_SWIPE_HYSTERISIS) {\r\n          this.dragAxis = axisToCheck;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Converts touch, pointer or mouse event\r\n   * to PhotoSwipe point.\r\n   *\r\n   * @private\r\n   * @param {Touch | PointerEvent} e\r\n   * @param {Point} p\r\n   * @returns {Point}\r\n   */\r\n  _convertEventPosToPoint(e, p) {\r\n    p.x = e.pageX - this.pswp.offset.x;\r\n    p.y = e.pageY - this.pswp.offset.y;\r\n\r\n    if ('pointerId' in e) {\r\n      p.id = e.pointerId;\r\n    } else if (e.identifier !== undefined) {\r\n      p.id = e.identifier;\r\n    }\r\n\r\n    return p;\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PointerEvent} e\r\n   */\r\n  _onClick(e) {\r\n    // Do not allow click event to pass through after drag\r\n    if (this.pswp.mainScroll.isShifted()) {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Gestures;\r\n", "import {\r\n  setTransform,\r\n  createElement,\r\n} from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./slide/slide.js').default} Slide */\r\n\r\n/** @typedef {{ el: HTMLDivElement; slide?: Slide }} ItemHolder */\r\n\r\nconst MAIN_SCROLL_END_FRICTION = 0.35;\r\n\r\n\r\n// const MIN_SWIPE_TRANSITION_DURATION = 250;\r\n// const MAX_SWIPE_TRABSITION_DURATION = 500;\r\n// const DEFAULT_SWIPE_TRANSITION_DURATION = 333;\r\n\r\n/**\r\n * Handles movement of the main scrolling container\r\n * (for example, it repositions when user swipes left or right).\r\n *\r\n * Also stores its state.\r\n */\r\nclass MainScroll {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.x = 0;\r\n    this.slideWidth = 0;\r\n    /** @private */\r\n    this._currPositionIndex = 0;\r\n    /** @private */\r\n    this._prevPositionIndex = 0;\r\n    /** @private */\r\n    this._containerShiftIndex = -1;\r\n\r\n    /** @type {ItemHolder[]} */\r\n    this.itemHolders = [];\r\n  }\r\n\r\n  /**\r\n   * Position the scroller and slide containers\r\n   * according to viewport size.\r\n   *\r\n   * @param {boolean} [resizeSlides] Whether slides content should resized\r\n   */\r\n  resize(resizeSlides) {\r\n    const { pswp } = this;\r\n    const newSlideWidth = Math.round(\r\n      pswp.viewportSize.x + pswp.viewportSize.x * pswp.options.spacing\r\n    );\r\n    // Mobile browsers might trigger a resize event during a gesture.\r\n    // (due to toolbar appearing or hiding).\r\n    // Avoid re-adjusting main scroll position if width wasn't changed\r\n    const slideWidthChanged = (newSlideWidth !== this.slideWidth);\r\n\r\n    if (slideWidthChanged) {\r\n      this.slideWidth = newSlideWidth;\r\n      this.moveTo(this.getCurrSlideX());\r\n    }\r\n\r\n    this.itemHolders.forEach((itemHolder, index) => {\r\n      if (slideWidthChanged) {\r\n        setTransform(itemHolder.el, (index + this._containerShiftIndex)\r\n                                    * this.slideWidth);\r\n      }\r\n\r\n      if (resizeSlides && itemHolder.slide) {\r\n        itemHolder.slide.resize();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Reset X position of the main scroller to zero\r\n   */\r\n  resetPosition() {\r\n    // Position on the main scroller (offset)\r\n    // it is independent from slide index\r\n    this._currPositionIndex = 0;\r\n    this._prevPositionIndex = 0;\r\n\r\n    // This will force recalculation of size on next resize()\r\n    this.slideWidth = 0;\r\n\r\n    // _containerShiftIndex*viewportSize will give you amount of transform of the current slide\r\n    this._containerShiftIndex = -1;\r\n  }\r\n\r\n  /**\r\n   * Create and append array of three items\r\n   * that hold data about slides in DOM\r\n   */\r\n  appendHolders() {\r\n    this.itemHolders = [];\r\n\r\n    // append our three slide holders -\r\n    // previous, current, and next\r\n    for (let i = 0; i < 3; i++) {\r\n      const el = createElement('pswp__item', 'div', this.pswp.container);\r\n      el.setAttribute('role', 'group');\r\n      el.setAttribute('aria-roledescription', 'slide');\r\n      el.setAttribute('aria-hidden', 'true');\r\n\r\n      // hide nearby item holders until initial zoom animation finishes (to avoid extra Paints)\r\n      el.style.display = (i === 1) ? 'block' : 'none';\r\n\r\n      this.itemHolders.push({\r\n        el,\r\n        //index: -1\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Whether the main scroll can be horizontally swiped to the next or previous slide.\r\n   * @returns {boolean}\r\n   */\r\n  canBeSwiped() {\r\n    return this.pswp.getNumItems() > 1;\r\n  }\r\n\r\n  /**\r\n   * Move main scroll by X amount of slides.\r\n   * For example:\r\n   *   `-1` will move to the previous slide,\r\n   *    `0` will reset the scroll position of the current slide,\r\n   *    `3` will move three slides forward\r\n   *\r\n   * If loop option is enabled - index will be automatically looped too,\r\n   * (for example `-1` will move to the last slide of the gallery).\r\n   *\r\n   * @param {number} diff\r\n   * @param {boolean} [animate]\r\n   * @param {number} [velocityX]\r\n   * @returns {boolean} whether index was changed or not\r\n   */\r\n  moveIndexBy(diff, animate, velocityX) {\r\n    const { pswp } = this;\r\n    let newIndex = pswp.potentialIndex + diff;\r\n    const numSlides = pswp.getNumItems();\r\n\r\n    if (pswp.canLoop()) {\r\n      newIndex = pswp.getLoopedIndex(newIndex);\r\n      const distance = (diff + numSlides) % numSlides;\r\n      if (distance <= numSlides / 2) {\r\n        // go forward\r\n        diff = distance;\r\n      } else {\r\n        // go backwards\r\n        diff = distance - numSlides;\r\n      }\r\n    } else {\r\n      if (newIndex < 0) {\r\n        newIndex = 0;\r\n      } else if (newIndex >= numSlides) {\r\n        newIndex = numSlides - 1;\r\n      }\r\n      diff = newIndex - pswp.potentialIndex;\r\n    }\r\n\r\n    pswp.potentialIndex = newIndex;\r\n    this._currPositionIndex -= diff;\r\n\r\n    pswp.animations.stopMainScroll();\r\n\r\n    const destinationX = this.getCurrSlideX();\r\n    if (!animate) {\r\n      this.moveTo(destinationX);\r\n      this.updateCurrItem();\r\n    } else {\r\n      pswp.animations.startSpring({\r\n        isMainScroll: true,\r\n        start: this.x,\r\n        end: destinationX,\r\n        velocity: velocityX || 0,\r\n        naturalFrequency: 30,\r\n        dampingRatio: 1, //0.7,\r\n        onUpdate: (x) => {\r\n          this.moveTo(x);\r\n        },\r\n        onComplete: () => {\r\n          this.updateCurrItem();\r\n          pswp.appendHeavy();\r\n        }\r\n      });\r\n\r\n      let currDiff = pswp.potentialIndex - pswp.currIndex;\r\n      if (pswp.canLoop()) {\r\n        const currDistance = (currDiff + numSlides) % numSlides;\r\n        if (currDistance <= numSlides / 2) {\r\n          // go forward\r\n          currDiff = currDistance;\r\n        } else {\r\n          // go backwards\r\n          currDiff = currDistance - numSlides;\r\n        }\r\n      }\r\n\r\n      // Force-append new slides during transition\r\n      // if difference between slides is more than 1\r\n      if (Math.abs(currDiff) > 1) {\r\n        this.updateCurrItem();\r\n      }\r\n    }\r\n\r\n    return Boolean(diff);\r\n  }\r\n\r\n  /**\r\n   * X position of the main scroll for the current slide\r\n   * (ignores position during dragging)\r\n   * @returns {number}\r\n   */\r\n  getCurrSlideX() {\r\n    return this.slideWidth * this._currPositionIndex;\r\n  }\r\n\r\n  /**\r\n   * Whether scroll position is shifted.\r\n   * For example, it will return true if the scroll is being dragged or animated.\r\n   * @returns {boolean}\r\n   */\r\n  isShifted() {\r\n    return this.x !== this.getCurrSlideX();\r\n  }\r\n\r\n  /**\r\n   * Update slides X positions and set their content\r\n   */\r\n  updateCurrItem() {\r\n    const { pswp } = this;\r\n    const positionDifference = this._prevPositionIndex - this._currPositionIndex;\r\n\r\n    if (!positionDifference) {\r\n      return;\r\n    }\r\n\r\n    this._prevPositionIndex = this._currPositionIndex;\r\n\r\n    pswp.currIndex = pswp.potentialIndex;\r\n\r\n    let diffAbs = Math.abs(positionDifference);\r\n    /** @type {ItemHolder | undefined} */\r\n    let tempHolder;\r\n\r\n    if (diffAbs >= 3) {\r\n      this._containerShiftIndex += positionDifference + (positionDifference > 0 ? -3 : 3);\r\n      diffAbs = 3;\r\n\r\n      // If slides are changed by 3 screens or more - clean up previous slides\r\n      this.itemHolders.forEach((itemHolder) => {\r\n        itemHolder.slide?.destroy();\r\n        itemHolder.slide = undefined;\r\n      });\r\n    }\r\n\r\n    for (let i = 0; i < diffAbs; i++) {\r\n      if (positionDifference > 0) {\r\n        tempHolder = this.itemHolders.shift();\r\n        if (tempHolder) {\r\n          this.itemHolders[2] = tempHolder; // move first to last\r\n\r\n          this._containerShiftIndex++;\r\n\r\n          setTransform(tempHolder.el, (this._containerShiftIndex + 2) * this.slideWidth);\r\n\r\n          pswp.setContent(tempHolder, (pswp.currIndex - diffAbs) + i + 2);\r\n        }\r\n      } else {\r\n        tempHolder = this.itemHolders.pop();\r\n        if (tempHolder) {\r\n          this.itemHolders.unshift(tempHolder); // move last to first\r\n\r\n          this._containerShiftIndex--;\r\n\r\n          setTransform(tempHolder.el, this._containerShiftIndex * this.slideWidth);\r\n\r\n          pswp.setContent(tempHolder, (pswp.currIndex + diffAbs) - i - 2);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Reset transfrom every 50ish navigations in one direction.\r\n    //\r\n    // Otherwise transform will keep growing indefinitely,\r\n    // which might cause issues as browsers have a maximum transform limit.\r\n    // I wasn't able to reach it, but just to be safe.\r\n    // This should not cause noticable lag.\r\n    if (Math.abs(this._containerShiftIndex) > 50 && !this.isShifted()) {\r\n      this.resetPosition();\r\n      this.resize();\r\n    }\r\n\r\n    // Pan transition might be running (and consntantly updating pan position)\r\n    pswp.animations.stopAllPan();\r\n\r\n    this.itemHolders.forEach((itemHolder, i) => {\r\n      if (itemHolder.slide) {\r\n        // Slide in the 2nd holder is always active\r\n        itemHolder.slide.setIsActive(i === 1);\r\n      }\r\n    });\r\n\r\n    pswp.currSlide = this.itemHolders[1]?.slide;\r\n    pswp.contentLoader.updateLazy(positionDifference);\r\n\r\n    if (pswp.currSlide) {\r\n      pswp.currSlide.applyCurrentZoomPan();\r\n    }\r\n\r\n    pswp.dispatch('change');\r\n  }\r\n\r\n  /**\r\n   * Move the X position of the main scroll container\r\n   *\r\n   * @param {number} x\r\n   * @param {boolean} [dragging]\r\n   */\r\n  moveTo(x, dragging) {\r\n    if (!this.pswp.canLoop() && dragging) {\r\n      // Apply friction\r\n      let newSlideIndexOffset = ((this.slideWidth * this._currPositionIndex) - x) / this.slideWidth;\r\n      newSlideIndexOffset += this.pswp.currIndex;\r\n      const delta = Math.round(x - this.x);\r\n\r\n      if ((newSlideIndexOffset < 0 && delta > 0)\r\n          || (newSlideIndexOffset >= this.pswp.getNumItems() - 1 && delta < 0)) {\r\n        x = this.x + (delta * MAIN_SCROLL_END_FRICTION);\r\n      }\r\n    }\r\n\r\n    this.x = x;\r\n\r\n    if (this.pswp.container) {\r\n      setTransform(this.pswp.container, x);\r\n    }\r\n\r\n    this.pswp.dispatch('moveMainScroll', { x, dragging: dragging ?? false });\r\n  }\r\n}\r\n\r\nexport default MainScroll;\r\n", "import { specialKeyUsed } from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Methods<T>} Methods<T>\r\n */\r\n\r\nconst KeyboardKeyCodesMap = {\r\n  Escape: 27,\r\n  z: 90,\r\n  ArrowLeft: 37,\r\n  ArrowUp: 38,\r\n  ArrowRight: 39,\r\n  ArrowDown: 40,\r\n  Tab: 9,\r\n};\r\n\r\n/**\r\n * @template {keyof KeyboardKeyCodesMap} T\r\n * @param {T} key\r\n * @param {boolean} isKeySupported\r\n * @returns {T | number | undefined}\r\n */\r\nconst getKeyboardEventKey = (key, isKeySupported) => {\r\n  return isKeySupported ? key : KeyboardKeyCodesMap[key];\r\n};\r\n\r\n/**\r\n * - Manages keyboard shortcuts.\r\n * - Helps trap focus within photoswipe.\r\n */\r\nclass Keyboard {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    /** @private */\r\n    this._wasFocused = false;\r\n\r\n    pswp.on('bindEvents', () => {\r\n      if (pswp.options.trapFocus) {\r\n        // Dialog was likely opened by keyboard if initial point is not defined\r\n        if (!pswp.options.initialPointerPos) {\r\n          // focus causes layout,\r\n          // which causes lag during the animation,\r\n          // that's why we delay it until the opener transition ends\r\n          this._focusRoot();\r\n        }\r\n\r\n        pswp.events.add(\r\n          document,\r\n          'focusin',\r\n          /** @type EventListener */(this._onFocusIn.bind(this))\r\n        );\r\n      }\r\n\r\n      pswp.events.add(document, 'keydown', /** @type EventListener */(this._onKeyDown.bind(this)));\r\n    });\r\n\r\n    const lastActiveElement = /** @type {HTMLElement} */ (document.activeElement);\r\n    pswp.on('destroy', () => {\r\n      if (pswp.options.returnFocus\r\n          && lastActiveElement\r\n          && this._wasFocused) {\r\n        lastActiveElement.focus();\r\n      }\r\n    });\r\n  }\r\n\r\n  /** @private */\r\n  _focusRoot() {\r\n    if (!this._wasFocused && this.pswp.element) {\r\n      this.pswp.element.focus();\r\n      this._wasFocused = true;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {KeyboardEvent} e\r\n   */\r\n  _onKeyDown(e) {\r\n    const { pswp } = this;\r\n\r\n    if (pswp.dispatch('keydown', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (specialKeyUsed(e)) {\r\n      // don't do anything if special key pressed\r\n      // to prevent from overriding default browser actions\r\n      // for example, in Chrome on Mac cmd+arrow-left returns to previous page\r\n      return;\r\n    }\r\n\r\n    /** @type {Methods<PhotoSwipe> | undefined} */\r\n    let keydownAction;\r\n    /** @type {'x' | 'y' | undefined} */\r\n    let axis;\r\n    let isForward = false;\r\n    const isKeySupported = 'key' in e;\r\n\r\n    switch (isKeySupported ? e.key : e.keyCode) {\r\n      case getKeyboardEventKey('Escape', isKeySupported):\r\n        if (pswp.options.escKey) {\r\n          keydownAction = 'close';\r\n        }\r\n        break;\r\n      case getKeyboardEventKey('z', isKeySupported):\r\n        keydownAction = 'toggleZoom';\r\n        break;\r\n      case getKeyboardEventKey('ArrowLeft', isKeySupported):\r\n        axis = 'x';\r\n        break;\r\n      case getKeyboardEventKey('ArrowUp', isKeySupported):\r\n        axis = 'y';\r\n        break;\r\n      case getKeyboardEventKey('ArrowRight', isKeySupported):\r\n        axis = 'x';\r\n        isForward = true;\r\n        break;\r\n      case getKeyboardEventKey('ArrowDown', isKeySupported):\r\n        isForward = true;\r\n        axis = 'y';\r\n        break;\r\n      case getKeyboardEventKey('Tab', isKeySupported):\r\n        this._focusRoot();\r\n        break;\r\n      default:\r\n    }\r\n\r\n    // if left/right/top/bottom key\r\n    if (axis) {\r\n      // prevent page scroll\r\n      e.preventDefault();\r\n\r\n      const { currSlide } = pswp;\r\n\r\n      if (pswp.options.arrowKeys\r\n          && axis === 'x'\r\n          && pswp.getNumItems() > 1) {\r\n        keydownAction = isForward ? 'next' : 'prev';\r\n      } else if (currSlide && currSlide.currZoomLevel > currSlide.zoomLevels.fit) {\r\n        // up/down arrow keys pan the image vertically\r\n        // left/right arrow keys pan horizontally.\r\n        // Unless there is only one image,\r\n        // or arrowKeys option is disabled\r\n        currSlide.pan[axis] += isForward ? -80 : 80;\r\n        currSlide.panTo(currSlide.pan.x, currSlide.pan.y);\r\n      }\r\n    }\r\n\r\n    if (keydownAction) {\r\n      e.preventDefault();\r\n      // @ts-ignore\r\n      pswp[keydownAction]();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Trap focus inside photoswipe\r\n   *\r\n   * @private\r\n   * @param {FocusEvent} e\r\n   */\r\n  _onFocusIn(e) {\r\n    const { template } = this.pswp;\r\n    if (template\r\n        && document !== e.target\r\n        && template !== e.target\r\n        && !template.contains(/** @type {Node} */ (e.target))) {\r\n      // focus root element\r\n      template.focus();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Keyboard;\r\n", "import { setTransitionStyle, removeTransitionStyle } from './util.js';\r\n\r\nconst DEFAULT_EASING = 'cubic-bezier(.4,0,.22,1)';\r\n\r\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\r\n\r\n/** @typedef {Object} DefaultCssAnimationProps\r\n *\r\n * @prop {HTMLElement} target\r\n * @prop {number} [duration]\r\n * @prop {string} [easing]\r\n * @prop {string} [transform]\r\n * @prop {string} [opacity]\r\n * */\r\n\r\n/** @typedef {SharedAnimationProps & DefaultCssAnimationProps} CssAnimationProps */\r\n\r\n/**\r\n * Runs CSS transition.\r\n */\r\nclass CSSAnimation {\r\n  /**\r\n   * onComplete can be unpredictable, be careful about current state\r\n   *\r\n   * @param {CssAnimationProps} props\r\n   */\r\n  constructor(props) {\r\n    this.props = props;\r\n    const {\r\n      target,\r\n      onComplete,\r\n      transform,\r\n      onFinish = () => {},\r\n      duration = 333,\r\n      easing = DEFAULT_EASING,\r\n    } = props;\r\n\r\n    this.onFinish = onFinish;\r\n\r\n    // support only transform and opacity\r\n    const prop = transform ? 'transform' : 'opacity';\r\n    const propValue = props[prop] ?? '';\r\n\r\n    /** @private */\r\n    this._target = target;\r\n    /** @private */\r\n    this._onComplete = onComplete;\r\n    /** @private */\r\n    this._finished = false;\r\n\r\n    /** @private */\r\n    this._onTransitionEnd = this._onTransitionEnd.bind(this);\r\n\r\n    // Using timeout hack to make sure that animation\r\n    // starts even if the animated property was changed recently,\r\n    // otherwise transitionend might not fire or transition won't start.\r\n    // https://drafts.csswg.org/css-transitions/#starting\r\n    //\r\n    // ¯\\_(ツ)_/¯\r\n    /** @private */\r\n    this._helperTimeout = setTimeout(() => {\r\n      setTransitionStyle(target, prop, duration, easing);\r\n      this._helperTimeout = setTimeout(() => {\r\n        target.addEventListener('transitionend', this._onTransitionEnd, false);\r\n        target.addEventListener('transitioncancel', this._onTransitionEnd, false);\r\n\r\n        // Safari occasionally does not emit transitionend event\r\n        // if element property was modified during the transition,\r\n        // which may be caused by resize or third party component,\r\n        // using timeout as a safety fallback\r\n        this._helperTimeout = setTimeout(() => {\r\n          this._finalizeAnimation();\r\n        }, duration + 500);\r\n        target.style[prop] = propValue;\r\n      }, 30); // Do not reduce this number\r\n    }, 0);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {TransitionEvent} e\r\n   */\r\n  _onTransitionEnd(e) {\r\n    if (e.target === this._target) {\r\n      this._finalizeAnimation();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   */\r\n  _finalizeAnimation() {\r\n    if (!this._finished) {\r\n      this._finished = true;\r\n      this.onFinish();\r\n      if (this._onComplete) {\r\n        this._onComplete();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Destroy is called automatically onFinish\r\n  destroy() {\r\n    if (this._helperTimeout) {\r\n      clearTimeout(this._helperTimeout);\r\n    }\r\n    removeTransitionStyle(this._target);\r\n    this._target.removeEventListener('transitionend', this._onTransitionEnd, false);\r\n    this._target.removeEventListener('transitioncancel', this._onTransitionEnd, false);\r\n    if (!this._finished) {\r\n      this._finalizeAnimation();\r\n    }\r\n  }\r\n}\r\n\r\nexport default CSSAnimation;\r\n", "const DEFAULT_NATURAL_FREQUENCY = 12;\r\nconst DEFAULT_DAMPING_RATIO = 0.75;\r\n\r\n/**\r\n * Spring easing helper\r\n */\r\nclass SpringEaser {\r\n  /**\r\n   * @param {number} initialVelocity Initial velocity, px per ms.\r\n   *\r\n   * @param {number} [dampingRatio]\r\n   * Determines how bouncy animation will be.\r\n   * From 0 to 1, 0 - always overshoot, 1 - do not overshoot.\r\n   * \"overshoot\" refers to part of animation that\r\n   * goes beyond the final value.\r\n   *\r\n   * @param {number} [naturalFrequency]\r\n   * Determines how fast animation will slow down.\r\n   * The higher value - the stiffer the transition will be,\r\n   * and the faster it will slow down.\r\n   * Recommended value from 10 to 50\r\n   */\r\n  constructor(initialVelocity, dampingRatio, naturalFrequency) {\r\n    this.velocity = initialVelocity * 1000; // convert to \"pixels per second\"\r\n\r\n    // https://en.wikipedia.org/wiki/Damping_ratio\r\n    this._dampingRatio = dampingRatio || DEFAULT_DAMPING_RATIO;\r\n\r\n    // https://en.wikipedia.org/wiki/Natural_frequency\r\n    this._naturalFrequency = naturalFrequency || DEFAULT_NATURAL_FREQUENCY;\r\n\r\n    this._dampedFrequency = this._naturalFrequency;\r\n\r\n    if (this._dampingRatio < 1) {\r\n      this._dampedFrequency *= Math.sqrt(1 - this._dampingRatio * this._dampingRatio);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} deltaPosition Difference between current and end position of the animation\r\n   * @param {number} deltaTime Frame duration in milliseconds\r\n   *\r\n   * @returns {number} Displacement, relative to the end position.\r\n   */\r\n  easeFrame(deltaPosition, deltaTime) {\r\n    // Inspired by Apple Webkit and Android spring function implementation\r\n    // https://en.wikipedia.org/wiki/Oscillation\r\n    // https://en.wikipedia.org/wiki/Damping_ratio\r\n    // we ignore mass (assume that it's 1kg)\r\n\r\n    let displacement = 0;\r\n    let coeff;\r\n\r\n    deltaTime /= 1000;\r\n\r\n    const naturalDumpingPow = Math.E ** (-this._dampingRatio * this._naturalFrequency * deltaTime);\r\n\r\n    if (this._dampingRatio === 1) {\r\n      coeff = this.velocity + this._naturalFrequency * deltaPosition;\r\n\r\n      displacement = (deltaPosition + coeff * deltaTime) * naturalDumpingPow;\r\n\r\n      this.velocity = displacement\r\n                        * (-this._naturalFrequency) + coeff\r\n                        * naturalDumpingPow;\r\n    } else if (this._dampingRatio < 1) {\r\n      coeff = (1 / this._dampedFrequency)\r\n                * (this._dampingRatio * this._naturalFrequency * deltaPosition + this.velocity);\r\n\r\n      const dumpedFCos = Math.cos(this._dampedFrequency * deltaTime);\r\n      const dumpedFSin = Math.sin(this._dampedFrequency * deltaTime);\r\n\r\n      displacement = naturalDumpingPow\r\n                       * (deltaPosition * dumpedFCos + coeff * dumpedFSin);\r\n\r\n      this.velocity = displacement\r\n                        * (-this._naturalFrequency)\r\n                        * this._dampingRatio\r\n                        + naturalDumpingPow\r\n                        * (-this._dampedFrequency * deltaPosition * dumpedFSin\r\n                        + this._dampedFrequency * coeff * dumpedFCos);\r\n    }\r\n\r\n    // Overdamped (>1) damping ratio is not supported\r\n\r\n    return displacement;\r\n  }\r\n}\r\n\r\nexport default SpringEaser;\r\n", "import SpringEaser from './spring-easer.js';\r\n\r\n/** @typedef {import('./animations.js').SharedAnimationProps} SharedAnimationProps */\r\n\r\n/**\r\n * @typedef {Object} DefaultSpringAnimationProps\r\n *\r\n * @prop {number} start\r\n * @prop {number} end\r\n * @prop {number} velocity\r\n * @prop {number} [dampingRatio]\r\n * @prop {number} [naturalFrequency]\r\n * @prop {(end: number) => void} onUpdate\r\n */\r\n\r\n/** @typedef {SharedAnimationProps & DefaultSpringAnimationProps} SpringAnimationProps */\r\n\r\nclass SpringAnimation {\r\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\r\n  constructor(props) {\r\n    this.props = props;\r\n    this._raf = 0;\r\n\r\n    const {\r\n      start,\r\n      end,\r\n      velocity,\r\n      onUpdate,\r\n      onComplete,\r\n      onFinish = () => {},\r\n      dampingRatio,\r\n      naturalFrequency\r\n    } = props;\r\n\r\n    this.onFinish = onFinish;\r\n\r\n    const easer = new SpringEaser(velocity, dampingRatio, naturalFrequency);\r\n    let prevTime = Date.now();\r\n    let deltaPosition = start - end;\r\n\r\n    const animationLoop = () => {\r\n      if (this._raf) {\r\n        deltaPosition = easer.easeFrame(deltaPosition, Date.now() - prevTime);\r\n\r\n        // Stop the animation if velocity is low and position is close to end\r\n        if (Math.abs(deltaPosition) < 1 && Math.abs(easer.velocity) < 50) {\r\n          // Finalize the animation\r\n          onUpdate(end);\r\n          if (onComplete) {\r\n            onComplete();\r\n          }\r\n          this.onFinish();\r\n        } else {\r\n          prevTime = Date.now();\r\n          onUpdate(deltaPosition + end);\r\n          this._raf = requestAnimationFrame(animationLoop);\r\n        }\r\n      }\r\n    };\r\n\r\n    this._raf = requestAnimationFrame(animationLoop);\r\n  }\r\n\r\n  // Destroy is called automatically onFinish\r\n  destroy() {\r\n    if (this._raf >= 0) {\r\n      cancelAnimationFrame(this._raf);\r\n    }\r\n    this._raf = 0;\r\n  }\r\n}\r\n\r\nexport default SpringAnimation;\r\n", "import CSSAnimation from './css-animation.js';\r\nimport SpringAnimation from './spring-animation.js';\r\n\r\n/** @typedef {import('./css-animation.js').CssAnimationProps} CssAnimationProps */\r\n/** @typedef {import('./spring-animation.js').SpringAnimationProps} SpringAnimationProps */\r\n\r\n/** @typedef {Object} SharedAnimationProps\r\n * @prop {string} [name]\r\n * @prop {boolean} [isPan]\r\n * @prop {boolean} [isMainScroll]\r\n * @prop {VoidFunction} [onComplete]\r\n * @prop {VoidFunction} [onFinish]\r\n */\r\n\r\n/** @typedef {SpringAnimation | CSSAnimation} Animation */\r\n/** @typedef {SpringAnimationProps | CssAnimationProps} AnimationProps */\r\n\r\n/**\r\n * Manages animations\r\n */\r\nclass Animations {\r\n  constructor() {\r\n    /** @type {Animation[]} */\r\n    this.activeAnimations = [];\r\n  }\r\n\r\n  /**\r\n   * @param {SpringAnimationProps} props\r\n   */\r\n  startSpring(props) {\r\n    this._start(props, true);\r\n  }\r\n\r\n  /**\r\n   * @param {CssAnimationProps} props\r\n   */\r\n  startTransition(props) {\r\n    this._start(props);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {AnimationProps} props\r\n   * @param {boolean} [isSpring]\r\n   * @returns {Animation}\r\n   */\r\n  _start(props, isSpring) {\r\n    const animation = isSpring\r\n      ? new SpringAnimation(/** @type SpringAnimationProps */ (props))\r\n      : new CSSAnimation(/** @type CssAnimationProps */ (props));\r\n\r\n    this.activeAnimations.push(animation);\r\n    animation.onFinish = () => this.stop(animation);\r\n\r\n    return animation;\r\n  }\r\n\r\n  /**\r\n   * @param {Animation} animation\r\n   */\r\n  stop(animation) {\r\n    animation.destroy();\r\n    const index = this.activeAnimations.indexOf(animation);\r\n    if (index > -1) {\r\n      this.activeAnimations.splice(index, 1);\r\n    }\r\n  }\r\n\r\n  stopAll() { // _stopAllAnimations\r\n    this.activeAnimations.forEach((animation) => {\r\n      animation.destroy();\r\n    });\r\n    this.activeAnimations = [];\r\n  }\r\n\r\n  /**\r\n   * Stop all pan or zoom transitions\r\n   */\r\n  stopAllPan() {\r\n    this.activeAnimations = this.activeAnimations.filter((animation) => {\r\n      if (animation.props.isPan) {\r\n        animation.destroy();\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  stopMainScroll() {\r\n    this.activeAnimations = this.activeAnimations.filter((animation) => {\r\n      if (animation.props.isMainScroll) {\r\n        animation.destroy();\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns true if main scroll transition is running\r\n   */\r\n  // isMainScrollRunning() {\r\n  //   return this.activeAnimations.some((animation) => {\r\n  //     return animation.props.isMainScroll;\r\n  //   });\r\n  // }\r\n\r\n  /**\r\n   * Returns true if any pan or zoom transition is running\r\n   */\r\n  isPanRunning() {\r\n    return this.activeAnimations.some((animation) => {\r\n      return animation.props.isPan;\r\n    });\r\n  }\r\n}\r\n\r\nexport default Animations;\r\n", "/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * <PERSON><PERSON> scroll wheel.\r\n * Can pan and zoom current slide image.\r\n */\r\nclass ScrollWheel {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    pswp.events.add(pswp.element, 'wheel', /** @type EventListener */(this._onWheel.bind(this)));\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {WheelEvent} e\r\n   */\r\n  _onWheel(e) {\r\n    e.preventDefault();\r\n    const { currSlide } = this.pswp;\r\n    let { deltaX, deltaY } = e;\r\n\r\n    if (!currSlide) {\r\n      return;\r\n    }\r\n\r\n    if (this.pswp.dispatch('wheel', { originalEvent: e }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (e.ctrlKey || this.pswp.options.wheelToZoom) {\r\n      // zoom\r\n      if (currSlide.isZoomable()) {\r\n        let zoomFactor = -deltaY;\r\n        if (e.deltaMode === 1 /* DOM_DELTA_LINE */) {\r\n          zoomFactor *= 0.05;\r\n        } else {\r\n          zoomFactor *= e.deltaMode ? 1 : 0.002;\r\n        }\r\n        zoomFactor = 2 ** zoomFactor;\r\n\r\n        const destZoomLevel = currSlide.currZoomLevel * zoomFactor;\r\n        currSlide.zoomTo(destZoomLevel, {\r\n          x: e.clientX,\r\n          y: e.clientY\r\n        });\r\n      }\r\n    } else {\r\n      // pan\r\n      if (currSlide.isPannable()) {\r\n        if (e.deltaMode === 1 /* DOM_DELTA_LINE */) {\r\n          // 18 - average line height\r\n          deltaX *= 18;\r\n          deltaY *= 18;\r\n        }\r\n\r\n        currSlide.panTo(\r\n          currSlide.pan.x - deltaX,\r\n          currSlide.pan.y - deltaY\r\n        );\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport default ScrollWheel;\r\n", "import { createElement } from '../util/util.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('../types.js').Methods<T>} Methods<T>\r\n */\r\n\r\n/**\r\n * @typedef {Object} UIElementMarkupProps\r\n * @prop {boolean} [isCustomSVG]\r\n * @prop {string} inner\r\n * @prop {string} [outlineID]\r\n * @prop {number | string} [size]\r\n */\r\n\r\n/**\r\n * @typedef {Object} UIElementData\r\n * @prop {DefaultUIElements | string} [name]\r\n * @prop {string} [className]\r\n * @prop {UIElementMarkup} [html]\r\n * @prop {boolean} [isButton]\r\n * @prop {keyof HTMLElementTagNameMap} [tagName]\r\n * @prop {string} [title]\r\n * @prop {string} [ariaLabel]\r\n * @prop {(element: HTMLElement, pswp: PhotoSwipe) => void} [onInit]\r\n * @prop {Methods<PhotoSwipe> | ((e: MouseEvent, element: HTMLElement, pswp: PhotoSwipe) => void)} [onClick]\r\n * @prop {'bar' | 'wrapper' | 'root'} [appendTo]\r\n * @prop {number} [order]\r\n */\r\n\r\n/** @typedef {'arrowPrev' | 'arrowNext' | 'close' | 'zoom' | 'counter'} DefaultUIElements */\r\n\r\n/** @typedef {string | UIElementMarkupProps} UIElementMarkup */\r\n\r\n/**\r\n * @param {UIElementMarkup} [htmlData]\r\n * @returns {string}\r\n */\r\nfunction addElementHTML(htmlData) {\r\n  if (typeof htmlData === 'string') {\r\n    // Allow developers to provide full svg,\r\n    // For example:\r\n    // <svg viewBox=\"0 0 32 32\" width=\"32\" height=\"32\" aria-hidden=\"true\" class=\"pswp__icn\">\r\n    //   <path d=\"...\" />\r\n    //   <circle ... />\r\n    // </svg>\r\n    // Can also be any HTML string.\r\n    return htmlData;\r\n  }\r\n\r\n  if (!htmlData || !htmlData.isCustomSVG) {\r\n    return '';\r\n  }\r\n\r\n  const svgData = htmlData;\r\n  let out = '<svg aria-hidden=\"true\" class=\"pswp__icn\" viewBox=\"0 0 %d %d\" width=\"%d\" height=\"%d\">';\r\n  // replace all %d with size\r\n  out = out.split('%d').join(/** @type {string} */ (svgData.size || 32));\r\n\r\n  // Icons may contain outline/shadow,\r\n  // to make it we \"clone\" base icon shape and add border to it.\r\n  // Icon itself and border are styled via CSS.\r\n  //\r\n  // Property shadowID defines ID of element that should be cloned.\r\n  if (svgData.outlineID) {\r\n    out += '<use class=\"pswp__icn-shadow\" xlink:href=\"#' + svgData.outlineID + '\"/>';\r\n  }\r\n\r\n  out += svgData.inner;\r\n\r\n  out += '</svg>';\r\n\r\n  return out;\r\n}\r\n\r\nclass UIElement {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   * @param {UIElementData} data\r\n   */\r\n  constructor(pswp, data) {\r\n    const name = data.name || data.className;\r\n    let elementHTML = data.html;\r\n\r\n    // @ts-expect-error lookup only by `data.name` maybe?\r\n    if (pswp.options[name] === false) {\r\n      // exit if element is disabled from options\r\n      return;\r\n    }\r\n\r\n    // Allow to override SVG icons from options\r\n    // @ts-expect-error lookup only by `data.name` maybe?\r\n    if (typeof pswp.options[name + 'SVG'] === 'string') {\r\n      // arrowPrevSVG\r\n      // arrowNextSVG\r\n      // closeSVG\r\n      // zoomSVG\r\n      // @ts-expect-error lookup only by `data.name` maybe?\r\n      elementHTML = pswp.options[name + 'SVG'];\r\n    }\r\n\r\n    pswp.dispatch('uiElementCreate', { data });\r\n\r\n    let className = '';\r\n    if (data.isButton) {\r\n      className += 'pswp__button ';\r\n      className += (data.className || `pswp__button--${data.name}`);\r\n    } else {\r\n      className += (data.className || `pswp__${data.name}`);\r\n    }\r\n\r\n    let tagName = data.isButton ? (data.tagName || 'button') : (data.tagName || 'div');\r\n    tagName = /** @type {keyof HTMLElementTagNameMap} */ (tagName.toLowerCase());\r\n    /** @type {HTMLElement} */\r\n    const element = createElement(className, tagName);\r\n\r\n    if (data.isButton) {\r\n      if (tagName === 'button') {\r\n        /** @type {HTMLButtonElement} */ (element).type = 'button';\r\n      }\r\n\r\n      let { title } = data;\r\n      const { ariaLabel } = data;\r\n\r\n      // @ts-expect-error lookup only by `data.name` maybe?\r\n      if (typeof pswp.options[name + 'Title'] === 'string') {\r\n        // @ts-expect-error lookup only by `data.name` maybe?\r\n        title = pswp.options[name + 'Title'];\r\n      }\r\n\r\n      if (title) {\r\n        element.title = title;\r\n      }\r\n\r\n      const ariaText = ariaLabel || title;\r\n      if (ariaText) {\r\n        element.setAttribute('aria-label', ariaText);\r\n      }\r\n    }\r\n\r\n    element.innerHTML = addElementHTML(elementHTML);\r\n\r\n    if (data.onInit) {\r\n      data.onInit(element, pswp);\r\n    }\r\n\r\n    if (data.onClick) {\r\n      element.onclick = (e) => {\r\n        if (typeof data.onClick === 'string') {\r\n          // @ts-ignore\r\n          pswp[data.onClick]();\r\n        } else if (typeof data.onClick === 'function') {\r\n          data.onClick(e, element, pswp);\r\n        }\r\n      };\r\n    }\r\n\r\n    // Top bar is default position\r\n    const appendTo = data.appendTo || 'bar';\r\n    /** @type {HTMLElement | undefined} root element by default */\r\n    let container = pswp.element;\r\n    if (appendTo === 'bar') {\r\n      if (!pswp.topBar) {\r\n        pswp.topBar = createElement('pswp__top-bar pswp__hide-on-close', 'div', pswp.scrollWrap);\r\n      }\r\n      container = pswp.topBar;\r\n    } else {\r\n      // element outside of top bar gets a secondary class\r\n      // that makes element fade out on close\r\n      element.classList.add('pswp__hide-on-close');\r\n\r\n      if (appendTo === 'wrapper') {\r\n        container = pswp.scrollWrap;\r\n      }\r\n    }\r\n\r\n    container?.appendChild(pswp.applyFilters('uiElement', element, data));\r\n  }\r\n}\r\n\r\nexport default UIElement;\r\n", "/*\r\n  Backward and forward arrow buttons\r\n */\r\n\r\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/**\r\n *\r\n * @param {HTMLElement} element\r\n * @param {PhotoSwipe} pswp\r\n * @param {boolean} [isNextButton]\r\n */\r\nfunction initArrowButton(element, pswp, isNextButton) {\r\n  element.classList.add('pswp__button--arrow');\r\n  // TODO: this should point to a unique id for this instance\r\n  element.setAttribute('aria-controls', 'pswp__items');\r\n  pswp.on('change', () => {\r\n    if (!pswp.options.loop) {\r\n      if (isNextButton) {\r\n        /** @type {HTMLButtonElement} */\r\n        (element).disabled = !(pswp.currIndex < pswp.getNumItems() - 1);\r\n      } else {\r\n        /** @type {HTMLButtonElement} */\r\n        (element).disabled = !(pswp.currIndex > 0);\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\n/** @type {UIElementData} */\r\nexport const arrowPrev = {\r\n  name: 'arrowPrev',\r\n  className: 'pswp__button--arrow--prev',\r\n  title: 'Previous',\r\n  order: 10,\r\n  isButton: true,\r\n  appendTo: 'wrapper',\r\n  html: {\r\n    isCustomSVG: true,\r\n    size: 60,\r\n    inner: '<path d=\"M29 43l-3 3-16-16 16-16 3 3-13 13 13 13z\" id=\"pswp__icn-arrow\"/>',\r\n    outlineID: 'pswp__icn-arrow'\r\n  },\r\n  onClick: 'prev',\r\n  onInit: initArrowButton\r\n};\r\n\r\n/** @type {UIElementData} */\r\nexport const arrowNext = {\r\n  name: 'arrowNext',\r\n  className: 'pswp__button--arrow--next',\r\n  title: 'Next',\r\n  order: 11,\r\n  isButton: true,\r\n  appendTo: 'wrapper',\r\n  html: {\r\n    isCustomSVG: true,\r\n    size: 60,\r\n    inner: '<use xlink:href=\"#pswp__icn-arrow\"/>',\r\n    outlineID: 'pswp__icn-arrow'\r\n  },\r\n  onClick: 'next',\r\n  onInit: (el, pswp) => {\r\n    initArrowButton(el, pswp, true);\r\n  }\r\n};\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nconst closeButton = {\r\n  name: 'close',\r\n  title: 'Close',\r\n  order: 20,\r\n  isButton: true,\r\n  html: {\r\n    isCustomSVG: true,\r\n    inner: '<path d=\"M24 10l-2-2-6 6-6-6-2 2 6 6-6 6 2 2 6-6 6 6 2-2-6-6z\" id=\"pswp__icn-close\"/>',\r\n    outlineID: 'pswp__icn-close'\r\n  },\r\n  onClick: 'close'\r\n};\r\n\r\nexport default closeButton;\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nconst zoomButton = {\r\n  name: 'zoom',\r\n  title: 'Zoom',\r\n  order: 10,\r\n  isButton: true,\r\n  html: {\r\n    isCustomSVG: true,\r\n    // eslint-disable-next-line max-len\r\n    inner: '<path d=\"M17.426 19.926a6 6 0 1 1 1.5-1.5L23 22.5 21.5 24l-4.074-4.074z\" id=\"pswp__icn-zoom\"/>'\r\n          + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-h\" d=\"M11 16v-2h6v2z\"/>'\r\n          + '<path fill=\"currentColor\" class=\"pswp__zoom-icn-bar-v\" d=\"M13 12h2v6h-2z\"/>',\r\n    outlineID: 'pswp__icn-zoom'\r\n  },\r\n  onClick: 'toggleZoom'\r\n};\r\n\r\nexport default zoomButton;\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nexport const loadingIndicator = {\r\n  name: 'preloader',\r\n  appendTo: 'bar',\r\n  order: 7,\r\n  html: {\r\n    isCustomSVG: true,\r\n    // eslint-disable-next-line max-len\r\n    inner: '<path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M21.2 16a5.2 5.2 0 1 1-5.2-5.2V8a8 8 0 1 0 8 8h-2.8Z\" id=\"pswp__icn-loading\"/>',\r\n    outlineID: 'pswp__icn-loading'\r\n  },\r\n  onInit: (indicatorElement, pswp) => {\r\n    /** @type {boolean | undefined} */\r\n    let isVisible;\r\n    /** @type {NodeJS.Timeout | null} */\r\n    let delayTimeout = null;\r\n\r\n    /**\r\n     * @param {string} className\r\n     * @param {boolean} add\r\n     */\r\n    const toggleIndicatorClass = (className, add) => {\r\n      indicatorElement.classList.toggle('pswp__preloader--' + className, add);\r\n    };\r\n\r\n    /**\r\n     * @param {boolean} visible\r\n     */\r\n    const setIndicatorVisibility = (visible) => {\r\n      if (isVisible !== visible) {\r\n        isVisible = visible;\r\n        toggleIndicatorClass('active', visible);\r\n      }\r\n    };\r\n\r\n    const updatePreloaderVisibility = () => {\r\n      if (!pswp.currSlide?.content.isLoading()) {\r\n        setIndicatorVisibility(false);\r\n        if (delayTimeout) {\r\n          clearTimeout(delayTimeout);\r\n          delayTimeout = null;\r\n        }\r\n        return;\r\n      }\r\n\r\n      if (!delayTimeout) {\r\n        // display loading indicator with delay\r\n        delayTimeout = setTimeout(() => {\r\n          setIndicatorVisibility(Boolean(pswp.currSlide?.content.isLoading()));\r\n          delayTimeout = null;\r\n        }, pswp.options.preloaderDelay);\r\n      }\r\n    };\r\n\r\n    pswp.on('change', updatePreloaderVisibility);\r\n\r\n    pswp.on('loadComplete', (e) => {\r\n      if (pswp.currSlide === e.slide) {\r\n        updatePreloaderVisibility();\r\n      }\r\n    });\r\n\r\n    // expose the method\r\n    if (pswp.ui) {\r\n      pswp.ui.updatePreloaderVisibility = updatePreloaderVisibility;\r\n    }\r\n  }\r\n};\r\n", "/** @type {import('./ui-element.js').UIElementData} UIElementData */\r\nexport const counterIndicator = {\r\n  name: 'counter',\r\n  order: 5,\r\n  onInit: (counterElement, pswp) => {\r\n    pswp.on('change', () => {\r\n      counterElement.innerText = (pswp.currIndex + 1)\r\n                                  + pswp.options.indexIndicatorSep\r\n                                  + pswp.getNumItems();\r\n    });\r\n  }\r\n};\r\n", "import UIElement from './ui-element.js';\r\nimport { arrowPrev, arrowNext } from './button-arrow.js';\r\nimport closeButton from './button-close.js';\r\nimport zoomButton from './button-zoom.js';\r\nimport { loadingIndicator } from './loading-indicator.js';\r\nimport { counterIndicator } from './counter-indicator.js';\r\n\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./ui-element.js').UIElementData} UIElementData */\r\n\r\n/**\r\n * Set special class on element when image is zoomed.\r\n *\r\n * By default, it is used to adjust\r\n * zoom icon and zoom cursor via CSS.\r\n *\r\n * @param {HTMLElement} el\r\n * @param {boolean} isZoomedIn\r\n */\r\nfunction setZoomedIn(el, isZoomedIn) {\r\n  el.classList.toggle('pswp--zoomed-in', isZoomedIn);\r\n}\r\n\r\nclass UI {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.isRegistered = false;\r\n    /** @type {UIElementData[]} */\r\n    this.uiElementsData = [];\r\n    /** @type {(UIElement | UIElementData)[]} */\r\n    this.items = [];\r\n    /** @type {() => void} */\r\n    this.updatePreloaderVisibility = () => {};\r\n\r\n    /**\r\n     * @private\r\n     * @type {number | undefined}\r\n     */\r\n    this._lastUpdatedZoomLevel = undefined;\r\n  }\r\n\r\n  init() {\r\n    const { pswp } = this;\r\n    this.isRegistered = false;\r\n    this.uiElementsData = [\r\n      closeButton,\r\n      arrowPrev,\r\n      arrowNext,\r\n      zoomButton,\r\n      loadingIndicator,\r\n      counterIndicator\r\n    ];\r\n\r\n    pswp.dispatch('uiRegister');\r\n\r\n    // sort by order\r\n    this.uiElementsData.sort((a, b) => {\r\n      // default order is 0\r\n      return (a.order || 0) - (b.order || 0);\r\n    });\r\n\r\n    this.items = [];\r\n\r\n    this.isRegistered = true;\r\n    this.uiElementsData.forEach((uiElementData) => {\r\n      this.registerElement(uiElementData);\r\n    });\r\n\r\n    pswp.on('change', () => {\r\n      pswp.element?.classList.toggle('pswp--one-slide', pswp.getNumItems() === 1);\r\n    });\r\n\r\n    pswp.on('zoomPanUpdate', () => this._onZoomPanUpdate());\r\n  }\r\n\r\n  /**\r\n   * @param {UIElementData} elementData\r\n   */\r\n  registerElement(elementData) {\r\n    if (this.isRegistered) {\r\n      this.items.push(\r\n        new UIElement(this.pswp, elementData)\r\n      );\r\n    } else {\r\n      this.uiElementsData.push(elementData);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fired each time zoom or pan position is changed.\r\n   * Update classes that control visibility of zoom button and cursor icon.\r\n   *\r\n   * @private\r\n   */\r\n  _onZoomPanUpdate() {\r\n    const { template, currSlide, options } = this.pswp;\r\n\r\n    if (this.pswp.opener.isClosing || !template || !currSlide) {\r\n      return;\r\n    }\r\n\r\n    let { currZoomLevel } = currSlide;\r\n\r\n    // if not open yet - check against initial zoom level\r\n    if (!this.pswp.opener.isOpen) {\r\n      currZoomLevel = currSlide.zoomLevels.initial;\r\n    }\r\n\r\n    if (currZoomLevel === this._lastUpdatedZoomLevel) {\r\n      return;\r\n    }\r\n    this._lastUpdatedZoomLevel = currZoomLevel;\r\n\r\n    const currZoomLevelDiff = currSlide.zoomLevels.initial - currSlide.zoomLevels.secondary;\r\n\r\n    // Initial and secondary zoom levels are almost equal\r\n    if (Math.abs(currZoomLevelDiff) < 0.01 || !currSlide.isZoomable()) {\r\n      // disable zoom\r\n      setZoomedIn(template, false);\r\n      template.classList.remove('pswp--zoom-allowed');\r\n      return;\r\n    }\r\n\r\n    template.classList.add('pswp--zoom-allowed');\r\n\r\n    const potentialZoomLevel = currZoomLevel === currSlide.zoomLevels.initial\r\n      ? currSlide.zoomLevels.secondary : currSlide.zoomLevels.initial;\r\n\r\n    setZoomedIn(template, potentialZoomLevel <= currZoomLevel);\r\n\r\n    if (options.imageClickAction === 'zoom'\r\n        || options.imageClickAction === 'zoom-or-close') {\r\n      template.classList.add('pswp--click-to-zoom');\r\n    }\r\n  }\r\n}\r\n\r\nexport default UI;\r\n", "/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\n/** @typedef {{ x: number; y: number; w: number; innerRect?: { w: number; h: number; x: number; y: number } }} Bounds */\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n * @returns Bounds\r\n */\r\nfunction getBoundsByElement(el) {\r\n  const thumbAreaRect = el.getBoundingClientRect();\r\n  return {\r\n    x: thumbAreaRect.left,\r\n    y: thumbAreaRect.top,\r\n    w: thumbAreaRect.width\r\n  };\r\n}\r\n\r\n/**\r\n * @param {HTMLElement} el\r\n * @param {number} imageWidth\r\n * @param {number} imageHeight\r\n * @returns Bounds\r\n */\r\nfunction getCroppedBoundsByElement(el, imageWidth, imageHeight) {\r\n  const thumbAreaRect = el.getBoundingClientRect();\r\n\r\n  // fill image into the area\r\n  // (do they same as object-fit:cover does to retrieve coordinates)\r\n  const hRatio = thumbAreaRect.width / imageWidth;\r\n  const vRatio = thumbAreaRect.height / imageHeight;\r\n  const fillZoomLevel = hRatio > vRatio ? hRatio : vRatio;\r\n\r\n  const offsetX = (thumbAreaRect.width - imageWidth * fillZoomLevel) / 2;\r\n  const offsetY = (thumbAreaRect.height - imageHeight * fillZoomLevel) / 2;\r\n\r\n  /**\r\n   * Coordinates of the image,\r\n   * as if it was not cropped,\r\n   * height is calculated automatically\r\n   *\r\n   * @type {Bounds}\r\n   */\r\n  const bounds = {\r\n    x: thumbAreaRect.left + offsetX,\r\n    y: thumbAreaRect.top + offsetY,\r\n    w: imageWidth * fillZoomLevel\r\n  };\r\n\r\n  // Coordinates of inner crop area\r\n  // relative to the image\r\n  bounds.innerRect = {\r\n    w: thumbAreaRect.width,\r\n    h: thumbAreaRect.height,\r\n    x: offsetX,\r\n    y: offsetY\r\n  };\r\n\r\n  return bounds;\r\n}\r\n\r\n/**\r\n * Get dimensions of thumbnail image\r\n * (click on which opens photoswipe or closes photoswipe to)\r\n *\r\n * @param {number} index\r\n * @param {SlideData} itemData\r\n * @param {PhotoSwipe} instance PhotoSwipe instance\r\n * @returns {Bounds | undefined}\r\n */\r\nexport function getThumbBounds(index, itemData, instance) {\r\n  // legacy event, before filters were introduced\r\n  const event = instance.dispatch('thumbBounds', {\r\n    index,\r\n    itemData,\r\n    instance\r\n  });\r\n  // @ts-expect-error\r\n  if (event.thumbBounds) {\r\n    // @ts-expect-error\r\n    return event.thumbBounds;\r\n  }\r\n\r\n  const { element } = itemData;\r\n  /** @type {Bounds | undefined} */\r\n  let thumbBounds;\r\n  /** @type {HTMLElement | null | undefined} */\r\n  let thumbnail;\r\n\r\n  if (element && instance.options.thumbSelector !== false) {\r\n    const thumbSelector = instance.options.thumbSelector || 'img';\r\n    thumbnail = element.matches(thumbSelector)\r\n      ? element : /** @type {HTMLElement | null} */ (element.querySelector(thumbSelector));\r\n  }\r\n\r\n  thumbnail = instance.applyFilters('thumbEl', thumbnail, itemData, index);\r\n\r\n  if (thumbnail) {\r\n    if (!itemData.thumbCropped) {\r\n      thumbBounds = getBoundsByElement(thumbnail);\r\n    } else {\r\n      thumbBounds = getCroppedBoundsByElement(\r\n        thumbnail,\r\n        itemData.width || itemData.w || 0,\r\n        itemData.height || itemData.h || 0\r\n      );\r\n    }\r\n  }\r\n\r\n  return instance.applyFilters('thumbBounds', thumbBounds, itemData, index);\r\n}\r\n", "/** @typedef {import('../lightbox/lightbox.js').default} PhotoSwipeLightbox */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('../photoswipe.js').PhotoSwipeOptions} PhotoSwipeOptions */\r\n/** @typedef {import('../photoswipe.js').DataSource} DataSource */\r\n/** @typedef {import('../ui/ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('../slide/content.js').default} ContentDefault */\r\n/** @typedef {import('../slide/slide.js').default} Slide */\r\n/** @typedef {import('../slide/slide.js').SlideData} SlideData */\r\n/** @typedef {import('../slide/zoom-level.js').default} ZoomLevel */\r\n/** @typedef {import('../slide/get-thumb-bounds.js').Bounds} Bounds */\r\n\r\n/**\r\n * Allow adding an arbitrary props to the Content\r\n * https://photoswipe.com/custom-content/#using-webp-image-format\r\n * @typedef {ContentDefault & Record<string, any>} Content\r\n */\r\n/** @typedef {{ x?: number; y?: number }} Point */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeEventsMap https://photoswipe.com/events/\r\n *\r\n *\r\n * https://photoswipe.com/adding-ui-elements/\r\n *\r\n * @prop {undefined} uiRegister\r\n * @prop {{ data: UIElementData }} uiElementCreate\r\n *\r\n *\r\n * https://photoswipe.com/events/#initialization-events\r\n *\r\n * @prop {undefined} beforeOpen\r\n * @prop {undefined} firstUpdate\r\n * @prop {undefined} initialLayout\r\n * @prop {undefined} change\r\n * @prop {undefined} afterInit\r\n * @prop {undefined} bindEvents\r\n *\r\n *\r\n * https://photoswipe.com/events/#opening-or-closing-transition-events\r\n *\r\n * @prop {undefined} openingAnimationStart\r\n * @prop {undefined} openingAnimationEnd\r\n * @prop {undefined} closingAnimationStart\r\n * @prop {undefined} closingAnimationEnd\r\n *\r\n *\r\n * https://photoswipe.com/events/#closing-events\r\n *\r\n * @prop {undefined} close\r\n * @prop {undefined} destroy\r\n *\r\n *\r\n * https://photoswipe.com/events/#pointer-and-gesture-events\r\n *\r\n * @prop {{ originalEvent: PointerEvent }} pointerDown\r\n * @prop {{ originalEvent: PointerEvent }} pointerMove\r\n * @prop {{ originalEvent: PointerEvent }} pointerUp\r\n * @prop {{ bgOpacity: number }} pinchClose can be default prevented\r\n * @prop {{ panY: number }} verticalDrag can be default prevented\r\n *\r\n *\r\n * https://photoswipe.com/events/#slide-content-events\r\n *\r\n * @prop {{ content: Content }} contentInit\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoad can be default prevented\r\n * @prop {{ content: Content; isLazy: boolean }} contentLoadImage can be default prevented\r\n * @prop {{ content: Content; slide: Slide; isError?: boolean }} loadComplete\r\n * @prop {{ content: Content; slide: Slide }} loadError\r\n * @prop {{ content: Content; width: number; height: number }} contentResize can be default prevented\r\n * @prop {{ content: Content; width: number; height: number; slide: Slide }} imageSizeChange\r\n * @prop {{ content: Content }} contentLazyLoad can be default prevented\r\n * @prop {{ content: Content }} contentAppend can be default prevented\r\n * @prop {{ content: Content }} contentActivate can be default prevented\r\n * @prop {{ content: Content }} contentDeactivate can be default prevented\r\n * @prop {{ content: Content }} contentRemove can be default prevented\r\n * @prop {{ content: Content }} contentDestroy can be default prevented\r\n *\r\n *\r\n * undocumented\r\n *\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} imageClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} bgClickAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} tapAction can be default prevented\r\n * @prop {{ point: Point; originalEvent: PointerEvent }} doubleTapAction can be default prevented\r\n *\r\n * @prop {{ originalEvent: KeyboardEvent }} keydown can be default prevented\r\n * @prop {{ x: number; dragging: boolean }} moveMainScroll\r\n * @prop {{ slide: Slide }} firstZoomPan\r\n * @prop {{ slide: Slide | undefined, data: SlideData, index: number }} gettingData\r\n * @prop {undefined} beforeResize\r\n * @prop {undefined} resize\r\n * @prop {undefined} viewportSize\r\n * @prop {undefined} updateScrollOffset\r\n * @prop {{ slide: Slide }} slideInit\r\n * @prop {{ slide: Slide }} afterSetContent\r\n * @prop {{ slide: Slide }} slideLoad\r\n * @prop {{ slide: Slide }} appendHeavy can be default prevented\r\n * @prop {{ slide: Slide }} appendHeavyContent\r\n * @prop {{ slide: Slide }} slideActivate\r\n * @prop {{ slide: Slide }} slideDeactivate\r\n * @prop {{ slide: Slide }} slideDestroy\r\n * @prop {{ destZoomLevel: number, centerPoint: Point | undefined, transitionDuration: number | false | undefined }} beforeZoomTo\r\n * @prop {{ slide: Slide }} zoomPanUpdate\r\n * @prop {{ slide: Slide }} initialZoomPan\r\n * @prop {{ slide: Slide }} calcSlideSize\r\n * @prop {undefined} resolutionChanged\r\n * @prop {{ originalEvent: WheelEvent }} wheel can be default prevented\r\n * @prop {{ content: Content }} contentAppendImage can be default prevented\r\n * @prop {{ index: number; itemData: SlideData }} lazyLoadSlide can be default prevented\r\n * @prop {undefined} lazyLoad\r\n * @prop {{ slide: Slide }} calcBounds\r\n * @prop {{ zoomLevels: ZoomLevel, slideData: SlideData }} zoomLevelsUpdate\r\n *\r\n *\r\n * legacy\r\n *\r\n * @prop {undefined} init\r\n * @prop {undefined} initialZoomIn\r\n * @prop {undefined} initialZoomOut\r\n * @prop {undefined} initialZoomInEnd\r\n * @prop {undefined} initialZoomOutEnd\r\n * @prop {{ dataSource: DataSource | undefined, numItems: number }} numItems\r\n * @prop {{ itemData: SlideData; index: number }} itemData\r\n * @prop {{ index: number, itemData: SlideData, instance: PhotoSwipe }} thumbBounds\r\n */\r\n\r\n/**\r\n * @typedef {Object} PhotoSwipeFiltersMap https://photoswipe.com/filters/\r\n *\r\n * @prop {(numItems: number, dataSource: DataSource | undefined) => number} numItems\r\n * Modify the total amount of slides. Example on Data sources page.\r\n * https://photoswipe.com/filters/#numitems\r\n *\r\n * @prop {(itemData: SlideData, index: number) => SlideData} itemData\r\n * Modify slide item data. Example on Data sources page.\r\n * https://photoswipe.com/filters/#itemdata\r\n *\r\n * @prop {(itemData: SlideData, element: HTMLElement, linkEl: HTMLAnchorElement) => SlideData} domItemData\r\n * Modify item data when it's parsed from DOM element. Example on Data sources page.\r\n * https://photoswipe.com/filters/#domitemdata\r\n *\r\n * @prop {(clickedIndex: number, e: MouseEvent, instance: PhotoSwipeLightbox) => number} clickedIndex\r\n * Modify clicked gallery item index.\r\n * https://photoswipe.com/filters/#clickedindex\r\n *\r\n * @prop {(placeholderSrc: string | false, content: Content) => string | false} placeholderSrc\r\n * Modify placeholder image source.\r\n * https://photoswipe.com/filters/#placeholdersrc\r\n *\r\n * @prop {(isContentLoading: boolean, content: Content) => boolean} isContentLoading\r\n * Modify if the content is currently loading.\r\n * https://photoswipe.com/filters/#iscontentloading\r\n *\r\n * @prop {(isContentZoomable: boolean, content: Content) => boolean} isContentZoomable\r\n * Modify if the content can be zoomed.\r\n * https://photoswipe.com/filters/#iscontentzoomable\r\n *\r\n * @prop {(useContentPlaceholder: boolean, content: Content) => boolean} useContentPlaceholder\r\n * Modify if the placeholder should be used for the content.\r\n * https://photoswipe.com/filters/#usecontentplaceholder\r\n *\r\n * @prop {(isKeepingPlaceholder: boolean, content: Content) => boolean} isKeepingPlaceholder\r\n * Modify if the placeholder should be kept after the content is loaded.\r\n * https://photoswipe.com/filters/#iskeepingplaceholder\r\n *\r\n *\r\n * @prop {(contentErrorElement: HTMLElement, content: Content) => HTMLElement} contentErrorElement\r\n * Modify an element when the content has error state (for example, if image cannot be loaded).\r\n * https://photoswipe.com/filters/#contenterrorelement\r\n *\r\n * @prop {(element: HTMLElement, data: UIElementData) => HTMLElement} uiElement\r\n * Modify a UI element that's being created.\r\n * https://photoswipe.com/filters/#uielement\r\n *\r\n * @prop {(thumbnail: HTMLElement | null | undefined, itemData: SlideData, index: number) => HTMLElement} thumbEl\r\n * Modify the thumbnail element from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbel\r\n *\r\n * @prop {(thumbBounds: Bounds | undefined, itemData: SlideData, index: number) => Bounds} thumbBounds\r\n * Modify the thumbnail bounds from which opening zoom animation starts or ends.\r\n * https://photoswipe.com/filters/#thumbbounds\r\n *\r\n * @prop {(srcsetSizesWidth: number, content: Content) => number} srcsetSizesWidth\r\n *\r\n * @prop {(preventPointerEvent: boolean, event: PointerEvent, pointerType: string) => boolean} preventPointerEvent\r\n *\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeFiltersMap} T\r\n * @typedef {{ fn: PhotoSwipeFiltersMap[T], priority: number }} Filter\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {PhotoSwipeEventsMap[T] extends undefined ? PhotoSwipeEvent<T> : PhotoSwipeEvent<T> & PhotoSwipeEventsMap[T]} AugmentedEvent\r\n */\r\n\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {(event: AugmentedEvent<T>) => void} EventCallback\r\n */\r\n\r\n/**\r\n * Base PhotoSwipe event object\r\n *\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n */\r\nclass PhotoSwipeEvent {\r\n  /**\r\n   * @param {T} type\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   */\r\n  constructor(type, details) {\r\n    this.type = type;\r\n    this.defaultPrevented = false;\r\n    if (details) {\r\n      Object.assign(this, details);\r\n    }\r\n  }\r\n\r\n  preventDefault() {\r\n    this.defaultPrevented = true;\r\n  }\r\n}\r\n\r\n/**\r\n * PhotoSwipe base class that can listen and dispatch for events.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox, extended by base.js\r\n */\r\nclass Eventable {\r\n  constructor() {\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeEventsMap]?: ((event: AugmentedEvent<T>) => void)[] }}\r\n     */\r\n    this._listeners = {};\r\n\r\n    /**\r\n     * @type {{ [T in keyof PhotoSwipeFiltersMap]?: Filter<T>[] }}\r\n     */\r\n    this._filters = {};\r\n\r\n    /** @type {PhotoSwipe | undefined} */\r\n    this.pswp = undefined;\r\n\r\n    /** @type {PhotoSwipeOptions | undefined} */\r\n    this.options = undefined;\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   * @param {number} priority\r\n   */\r\n  addFilter(name, fn, priority = 100) {\r\n    if (!this._filters[name]) {\r\n      this._filters[name] = [];\r\n    }\r\n\r\n    this._filters[name]?.push({ fn, priority });\r\n    this._filters[name]?.sort((f1, f2) => f1.priority - f2.priority);\r\n\r\n    this.pswp?.addFilter(name, fn, priority);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeFiltersMap[T]} fn\r\n   */\r\n  removeFilter(name, fn) {\r\n    if (this._filters[name]) {\r\n      // @ts-expect-error\r\n      this._filters[name] = this._filters[name].filter(filter => (filter.fn !== fn));\r\n    }\r\n\r\n    if (this.pswp) {\r\n      this.pswp.removeFilter(name, fn);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeFiltersMap} T\r\n   * @param {T} name\r\n   * @param {Parameters<PhotoSwipeFiltersMap[T]>} args\r\n   * @returns {Parameters<PhotoSwipeFiltersMap[T]>[0]}\r\n   */\r\n  applyFilters(name, ...args) {\r\n    this._filters[name]?.forEach((filter) => {\r\n      // @ts-expect-error\r\n      args[0] = filter.fn.apply(this, args);\r\n    });\r\n    return args[0];\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  on(name, fn) {\r\n    if (!this._listeners[name]) {\r\n      this._listeners[name] = [];\r\n    }\r\n    this._listeners[name]?.push(fn);\r\n\r\n    // When binding events to lightbox,\r\n    // also bind events to PhotoSwipe Core,\r\n    // if it's open.\r\n    this.pswp?.on(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {EventCallback<T>} fn\r\n   */\r\n  off(name, fn) {\r\n    if (this._listeners[name]) {\r\n      // @ts-expect-error\r\n      this._listeners[name] = this._listeners[name].filter(listener => (fn !== listener));\r\n    }\r\n\r\n    this.pswp?.off(name, fn);\r\n  }\r\n\r\n  /**\r\n   * @template {keyof PhotoSwipeEventsMap} T\r\n   * @param {T} name\r\n   * @param {PhotoSwipeEventsMap[T]} [details]\r\n   * @returns {AugmentedEvent<T>}\r\n   */\r\n  dispatch(name, details) {\r\n    if (this.pswp) {\r\n      return this.pswp.dispatch(name, details);\r\n    }\r\n\r\n    const event = /** @type {AugmentedEvent<T>} */ (new PhotoSwipeEvent(name, details));\r\n\r\n    this._listeners[name]?.forEach((listener) => {\r\n      listener.call(this, event);\r\n    });\r\n\r\n    return event;\r\n  }\r\n}\r\n\r\nexport default Eventable;\r\n", "import { createElement, setWidthHeight, toTransformString } from '../util/util.js';\r\n\r\nclass Placeholder {\r\n  /**\r\n   * @param {string | false} imageSrc\r\n   * @param {HTMLElement} container\r\n   */\r\n  constructor(imageSrc, container) {\r\n    // Create placeholder\r\n    // (stretched thumbnail or simple div behind the main image)\r\n    /** @type {HTMLImageElement | HTMLDivElement | null} */\r\n    this.element = createElement(\r\n      'pswp__img pswp__img--placeholder',\r\n      imageSrc ? 'img' : 'div',\r\n      container\r\n    );\r\n\r\n    if (imageSrc) {\r\n      const imgEl = /** @type {HTMLImageElement} */ (this.element);\r\n      imgEl.decoding = 'async';\r\n      imgEl.alt = '';\r\n      imgEl.src = imageSrc;\r\n      imgEl.setAttribute('role', 'presentation');\r\n    }\r\n\r\n    this.element.setAttribute('aria-hidden', 'true');\r\n  }\r\n\r\n  /**\r\n   * @param {number} width\r\n   * @param {number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.element.tagName === 'IMG') {\r\n      // Use transform scale() to modify img placeholder size\r\n      // (instead of changing width/height directly).\r\n      // This helps with performance, specifically in iOS15 Safari.\r\n      setWidthHeight(this.element, 250, 'auto');\r\n      this.element.style.transformOrigin = '0 0';\r\n      this.element.style.transform = toTransformString(0, 0, width / 250);\r\n    } else {\r\n      setWidthHeight(this.element, width, height);\r\n    }\r\n  }\r\n\r\n  destroy() {\r\n    if (this.element?.parentNode) {\r\n      this.element.remove();\r\n    }\r\n    this.element = null;\r\n  }\r\n}\r\n\r\nexport default Placeholder;\r\n", "import { createElement, is<PERSON><PERSON><PERSON>, <PERSON>OAD_STATE, setWidthHeight } from '../util/util.js';\r\nimport Placeholder from './placeholder.js';\r\n\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../util/util.js').LoadState} LoadState */\r\n\r\nclass Content {\r\n  /**\r\n   * @param {SlideData} itemData Slide data\r\n   * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n   * @param {number} index\r\n   */\r\n  constructor(itemData, instance, index) {\r\n    this.instance = instance;\r\n    this.data = itemData;\r\n    this.index = index;\r\n\r\n    /** @type {HTMLImageElement | HTMLDivElement | undefined} */\r\n    this.element = undefined;\r\n    /** @type {Placeholder | undefined} */\r\n    this.placeholder = undefined;\r\n    /** @type {Slide | undefined} */\r\n    this.slide = undefined;\r\n\r\n    this.displayedImageWidth = 0;\r\n    this.displayedImageHeight = 0;\r\n\r\n    this.width = Number(this.data.w) || Number(this.data.width) || 0;\r\n    this.height = Number(this.data.h) || Number(this.data.height) || 0;\r\n\r\n    this.isAttached = false;\r\n    this.hasSlide = false;\r\n    this.isDecoding = false;\r\n    /** @type {LoadState} */\r\n    this.state = LOAD_STATE.IDLE;\r\n\r\n    if (this.data.type) {\r\n      this.type = this.data.type;\r\n    } else if (this.data.src) {\r\n      this.type = 'image';\r\n    } else {\r\n      this.type = 'html';\r\n    }\r\n\r\n    this.instance.dispatch('contentInit', { content: this });\r\n  }\r\n\r\n  removePlaceholder() {\r\n    if (this.placeholder && !this.keepPlaceholder()) {\r\n      // With delay, as image might be loaded, but not rendered\r\n      setTimeout(() => {\r\n        if (this.placeholder) {\r\n          this.placeholder.destroy();\r\n          this.placeholder = undefined;\r\n        }\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload content\r\n   *\r\n   * @param {boolean} isLazy\r\n   * @param {boolean} [reload]\r\n   */\r\n  load(isLazy, reload) {\r\n    if (this.slide && this.usePlaceholder()) {\r\n      if (!this.placeholder) {\r\n        const placeholderSrc = this.instance.applyFilters(\r\n          'placeholderSrc',\r\n          // use  image-based placeholder only for the first slide,\r\n          // as rendering (even small stretched thumbnail) is an expensive operation\r\n          (this.data.msrc && this.slide.isFirstSlide) ? this.data.msrc : false,\r\n          this\r\n        );\r\n        this.placeholder = new Placeholder(\r\n          placeholderSrc,\r\n          this.slide.container\r\n        );\r\n      } else {\r\n        const placeholderEl = this.placeholder.element;\r\n        // Add placeholder to DOM if it was already created\r\n        if (placeholderEl && !placeholderEl.parentElement) {\r\n          this.slide.container.prepend(placeholderEl);\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.element && !reload) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentLoad', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent()) {\r\n      this.element = createElement('pswp__img', 'img');\r\n      // Start loading only after width is defined, as sizes might depend on it.\r\n      // Due to Safari feature, we must define sizes before srcset.\r\n      if (this.displayedImageWidth) {\r\n        this.loadImage(isLazy);\r\n      }\r\n    } else {\r\n      this.element = createElement('pswp__content', 'div');\r\n      this.element.innerHTML = this.data.html || '';\r\n    }\r\n\r\n    if (reload && this.slide) {\r\n      this.slide.updateContentSize(true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Preload image\r\n   *\r\n   * @param {boolean} isLazy\r\n   */\r\n  loadImage(isLazy) {\r\n    if (!this.isImageContent()\r\n      || !this.element\r\n      || this.instance.dispatch('contentLoadImage', { content: this, isLazy }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const imageElement = /** @type HTMLImageElement */ (this.element);\r\n\r\n    this.updateSrcsetSizes();\r\n\r\n    if (this.data.srcset) {\r\n      imageElement.srcset = this.data.srcset;\r\n    }\r\n\r\n    imageElement.src = this.data.src ?? '';\r\n    imageElement.alt = this.data.alt ?? '';\r\n\r\n    this.state = LOAD_STATE.LOADING;\r\n\r\n    if (imageElement.complete) {\r\n      this.onLoaded();\r\n    } else {\r\n      imageElement.onload = () => {\r\n        this.onLoaded();\r\n      };\r\n\r\n      imageElement.onerror = () => {\r\n        this.onError();\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assign slide to content\r\n   *\r\n   * @param {Slide} slide\r\n   */\r\n  setSlide(slide) {\r\n    this.slide = slide;\r\n    this.hasSlide = true;\r\n    this.instance = slide.pswp;\r\n\r\n    // todo: do we need to unset slide?\r\n  }\r\n\r\n  /**\r\n   * Content load success handler\r\n   */\r\n  onLoaded() {\r\n    this.state = LOAD_STATE.LOADED;\r\n\r\n    if (this.slide && this.element) {\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, content: this });\r\n\r\n      // if content is reloaded\r\n      if (this.slide.isActive\r\n          && this.slide.heavyAppended\r\n          && !this.element.parentNode) {\r\n        this.append();\r\n        this.slide.updateContentSize(true);\r\n      }\r\n\r\n      if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n        this.removePlaceholder();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Content load error handler\r\n   */\r\n  onError() {\r\n    this.state = LOAD_STATE.ERROR;\r\n\r\n    if (this.slide) {\r\n      this.displayError();\r\n      this.instance.dispatch('loadComplete', { slide: this.slide, isError: true, content: this });\r\n      this.instance.dispatch('loadError', { slide: this.slide, content: this });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is currently loading\r\n   */\r\n  isLoading() {\r\n    return this.instance.applyFilters(\r\n      'isContentLoading',\r\n      this.state === LOAD_STATE.LOADING,\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * @returns {Boolean} If the content is in error state\r\n   */\r\n  isError() {\r\n    return this.state === LOAD_STATE.ERROR;\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content is image\r\n   */\r\n  isImageContent() {\r\n    return this.type === 'image';\r\n  }\r\n\r\n  /**\r\n   * Update content size\r\n   *\r\n   * @param {Number} width\r\n   * @param {Number} height\r\n   */\r\n  setDisplayedSize(width, height) {\r\n    if (!this.element) {\r\n      return;\r\n    }\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.setDisplayedSize(width, height);\r\n    }\r\n\r\n    if (this.instance.dispatch(\r\n      'contentResize',\r\n      { content: this, width, height }).defaultPrevented\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    setWidthHeight(this.element, width, height);\r\n\r\n    if (this.isImageContent() && !this.isError()) {\r\n      const isInitialSizeUpdate = (!this.displayedImageWidth && width);\r\n\r\n      this.displayedImageWidth = width;\r\n      this.displayedImageHeight = height;\r\n\r\n      if (isInitialSizeUpdate) {\r\n        this.loadImage(false);\r\n      } else {\r\n        this.updateSrcsetSizes();\r\n      }\r\n\r\n      if (this.slide) {\r\n        this.instance.dispatch(\r\n          'imageSizeChange',\r\n          { slide: this.slide, width, height, content: this }\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If the content can be zoomed\r\n   */\r\n  isZoomable() {\r\n    return this.instance.applyFilters(\r\n      'isContentZoomable',\r\n      this.isImageContent() && (this.state !== LOAD_STATE.ERROR),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Update image srcset sizes attribute based on width and height\r\n   */\r\n  updateSrcsetSizes() {\r\n    // Handle srcset sizes attribute.\r\n    //\r\n    // Never lower quality, if it was increased previously.\r\n    // Chrome does this automatically, Firefox and Safari do not,\r\n    // so we store largest used size in dataset.\r\n    if (!this.isImageContent() || !this.element || !this.data.srcset) {\r\n      return;\r\n    }\r\n\r\n    const image = /** @type HTMLImageElement */ (this.element);\r\n    const sizesWidth = this.instance.applyFilters(\r\n      'srcsetSizesWidth',\r\n      this.displayedImageWidth,\r\n      this\r\n    );\r\n\r\n    if (\r\n      !image.dataset.largestUsedSize\r\n      || sizesWidth > parseInt(image.dataset.largestUsedSize, 10)\r\n    ) {\r\n      image.sizes = sizesWidth + 'px';\r\n      image.dataset.largestUsedSize = String(sizesWidth);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If content should use a placeholder (from msrc by default)\r\n   */\r\n  usePlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'useContentPlaceholder',\r\n      this.isImageContent(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Preload content with lazy-loading param\r\n   */\r\n  lazyLoad() {\r\n    if (this.instance.dispatch('contentLazyLoad', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.load(true);\r\n  }\r\n\r\n  /**\r\n   * @returns {boolean} If placeholder should be kept after content is loaded\r\n   */\r\n  keepPlaceholder() {\r\n    return this.instance.applyFilters(\r\n      'isKeepingPlaceholder',\r\n      this.isLoading(),\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Destroy the content\r\n   */\r\n  destroy() {\r\n    this.hasSlide = false;\r\n    this.slide = undefined;\r\n\r\n    if (this.instance.dispatch('contentDestroy', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    this.remove();\r\n\r\n    if (this.placeholder) {\r\n      this.placeholder.destroy();\r\n      this.placeholder = undefined;\r\n    }\r\n\r\n    if (this.isImageContent() && this.element) {\r\n      this.element.onload = null;\r\n      this.element.onerror = null;\r\n      this.element = undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Display error message\r\n   */\r\n  displayError() {\r\n    if (this.slide) {\r\n      let errorMsgEl = createElement('pswp__error-msg', 'div');\r\n      errorMsgEl.innerText = this.instance.options?.errorMsg ?? '';\r\n      errorMsgEl = /** @type {HTMLDivElement} */ (this.instance.applyFilters(\r\n        'contentErrorElement',\r\n        errorMsgEl,\r\n        this\r\n      ));\r\n      this.element = createElement('pswp__content pswp__error-msg-container', 'div');\r\n      this.element.appendChild(errorMsgEl);\r\n      this.slide.container.innerText = '';\r\n      this.slide.container.appendChild(this.element);\r\n      this.slide.updateContentSize(true);\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the content\r\n   */\r\n  append() {\r\n    if (this.isAttached || !this.element) {\r\n      return;\r\n    }\r\n\r\n    this.isAttached = true;\r\n\r\n    if (this.state === LOAD_STATE.ERROR) {\r\n      this.displayError();\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppend', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const supportsDecode = ('decode' in this.element);\r\n\r\n    if (this.isImageContent()) {\r\n      // Use decode() on nearby slides\r\n      //\r\n      // Nearby slide images are in DOM and not hidden via display:none.\r\n      // However, they are placed offscreen (to the left and right side).\r\n      //\r\n      // Some browsers do not composite the image until it's actually visible,\r\n      // using decode() helps.\r\n      //\r\n      // You might ask \"why dont you just decode() and then append all images\",\r\n      // that's because I want to show image before it's fully loaded,\r\n      // as browser can render parts of image while it is loading.\r\n      // We do not do this in Safari due to partial loading bug.\r\n      if (supportsDecode && this.slide && (!this.slide.isActive || isSafari())) {\r\n        this.isDecoding = true;\r\n        // purposefully using finally instead of then,\r\n        // as if srcset sizes changes dynamically - it may cause decode error\r\n        /** @type {HTMLImageElement} */\r\n        (this.element).decode().catch(() => {}).finally(() => {\r\n          this.isDecoding = false;\r\n          this.appendImage();\r\n        });\r\n      } else {\r\n        this.appendImage();\r\n      }\r\n    } else if (this.slide && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Activate the slide,\r\n   * active slide is generally the current one,\r\n   * meaning the user can see it.\r\n   */\r\n  activate() {\r\n    if (this.instance.dispatch('contentActivate', { content: this }).defaultPrevented\r\n      || !this.slide) {\r\n      return;\r\n    }\r\n\r\n    if (this.isImageContent() && this.isDecoding && !isSafari()) {\r\n      // add image to slide when it becomes active,\r\n      // even if it's not finished decoding\r\n      this.appendImage();\r\n    } else if (this.isError()) {\r\n      this.load(false, true); // try to reload\r\n    }\r\n\r\n    if (this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'false');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deactivate the content\r\n   */\r\n  deactivate() {\r\n    this.instance.dispatch('contentDeactivate', { content: this });\r\n    if (this.slide && this.slide.holderElement) {\r\n      this.slide.holderElement.setAttribute('aria-hidden', 'true');\r\n    }\r\n  }\r\n\r\n\r\n  /**\r\n   * Remove the content from DOM\r\n   */\r\n  remove() {\r\n    this.isAttached = false;\r\n\r\n    if (this.instance.dispatch('contentRemove', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    if (this.element && this.element.parentNode) {\r\n      this.element.remove();\r\n    }\r\n\r\n    if (this.placeholder && this.placeholder.element) {\r\n      this.placeholder.element.remove();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Append the image content to slide container\r\n   */\r\n  appendImage() {\r\n    if (!this.isAttached) {\r\n      return;\r\n    }\r\n\r\n    if (this.instance.dispatch('contentAppendImage', { content: this }).defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    // ensure that element exists and is not already appended\r\n    if (this.slide && this.element && !this.element.parentNode) {\r\n      this.slide.container.appendChild(this.element);\r\n    }\r\n\r\n    if (this.state === LOAD_STATE.LOADED || this.state === LOAD_STATE.ERROR) {\r\n      this.removePlaceholder();\r\n    }\r\n  }\r\n}\r\n\r\nexport default Content;\r\n", "import { getViewportSize, getPanAreaSize } from '../util/viewport-size.js';\r\nimport ZoomLevel from './zoom-level.js';\r\n\r\n/** @typedef {import('./content.js').default} Content */\r\n/** @typedef {import('./slide.js').default} Slide */\r\n/** @typedef {import('./slide.js').SlideData} SlideData */\r\n/** @typedef {import('../core/base.js').default} PhotoSwipeBase */\r\n/** @typedef {import('../photoswipe.js').default} PhotoSwipe */\r\n\r\nconst MIN_SLIDES_TO_CACHE = 5;\r\n\r\n/**\r\n * Lazy-load an image\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * @param {SlideData} itemData Data about the slide\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox instance\r\n * @param {number} index\r\n * @returns {Content} Image that is being decoded or false.\r\n */\r\nexport function lazyLoadData(itemData, instance, index) {\r\n  const content = instance.createContentFromData(itemData, index);\r\n  /** @type {ZoomLevel | undefined} */\r\n  let zoomLevel;\r\n\r\n  const { options } = instance;\r\n\r\n  // We need to know dimensions of the image to preload it,\r\n  // as it might use srcset, and we need to define sizes\r\n  if (options) {\r\n    zoomLevel = new ZoomLevel(options, itemData, -1);\r\n\r\n    let viewportSize;\r\n    if (instance.pswp) {\r\n      viewportSize = instance.pswp.viewportSize;\r\n    } else {\r\n      viewportSize = getViewportSize(options, instance);\r\n    }\r\n\r\n    const panAreaSize = getPanAreaSize(options, viewportSize, itemData, index);\r\n    zoomLevel.update(content.width, content.height, panAreaSize);\r\n  }\r\n\r\n  content.lazyLoad();\r\n\r\n  if (zoomLevel) {\r\n    content.setDisplayedSize(\r\n      Math.ceil(content.width * zoomLevel.initial),\r\n      Math.ceil(content.height * zoomLevel.initial)\r\n    );\r\n  }\r\n\r\n  return content;\r\n}\r\n\r\n\r\n/**\r\n * Lazy-loads specific slide.\r\n * This function is used both by Lightbox and PhotoSwipe core,\r\n * thus it can be called before dialog is opened.\r\n *\r\n * By default, it loads image based on viewport size and initial zoom level.\r\n *\r\n * @param {number} index Slide index\r\n * @param {PhotoSwipeBase} instance PhotoSwipe or PhotoSwipeLightbox eventable instance\r\n * @returns {Content | undefined}\r\n */\r\nexport function lazyLoadSlide(index, instance) {\r\n  const itemData = instance.getItemData(index);\r\n\r\n  if (instance.dispatch('lazyLoadSlide', { index, itemData }).defaultPrevented) {\r\n    return;\r\n  }\r\n\r\n  return lazyLoadData(itemData, instance, index);\r\n}\r\n\r\nclass ContentLoader {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    // Total amount of cached images\r\n    this.limit = Math.max(\r\n      pswp.options.preload[0] + pswp.options.preload[1] + 1,\r\n      MIN_SLIDES_TO_CACHE\r\n    );\r\n    /** @type {Content[]} */\r\n    this._cachedItems = [];\r\n  }\r\n\r\n  /**\r\n   * Lazy load nearby slides based on `preload` option.\r\n   *\r\n   * @param {number} [diff] Difference between slide indexes that was changed recently, or 0.\r\n   */\r\n  updateLazy(diff) {\r\n    const { pswp } = this;\r\n\r\n    if (pswp.dispatch('lazyLoad').defaultPrevented) {\r\n      return;\r\n    }\r\n\r\n    const { preload } = pswp.options;\r\n    const isForward = diff === undefined ? true : (diff >= 0);\r\n    let i;\r\n\r\n    // preload[1] - num items to preload in forward direction\r\n    for (i = 0; i <= preload[1]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? i : (-i)));\r\n    }\r\n\r\n    // preload[0] - num items to preload in backward direction\r\n    for (i = 1; i <= preload[0]; i++) {\r\n      this.loadSlideByIndex(pswp.currIndex + (isForward ? (-i) : i));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} initialIndex\r\n   */\r\n  loadSlideByIndex(initialIndex) {\r\n    const index = this.pswp.getLoopedIndex(initialIndex);\r\n    // try to get cached content\r\n    let content = this.getContentByIndex(index);\r\n    if (!content) {\r\n      // no cached content, so try to load from scratch:\r\n      content = lazyLoadSlide(index, this.pswp);\r\n      // if content can be loaded, add it to cache:\r\n      if (content) {\r\n        this.addToCache(content);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {Slide} slide\r\n   * @returns {Content}\r\n   */\r\n  getContentBySlide(slide) {\r\n    let content = this.getContentByIndex(slide.index);\r\n    if (!content) {\r\n      // create content if not found in cache\r\n      content = this.pswp.createContentFromData(slide.data, slide.index);\r\n      this.addToCache(content);\r\n    }\r\n\r\n    // assign slide to content\r\n    content.setSlide(slide);\r\n\r\n    return content;\r\n  }\r\n\r\n  /**\r\n   * @param {Content} content\r\n   */\r\n  addToCache(content) {\r\n    // move to the end of array\r\n    this.removeByIndex(content.index);\r\n    this._cachedItems.push(content);\r\n\r\n    if (this._cachedItems.length > this.limit) {\r\n      // Destroy the first content that's not attached\r\n      const indexToRemove = this._cachedItems.findIndex((item) => {\r\n        return !item.isAttached && !item.hasSlide;\r\n      });\r\n      if (indexToRemove !== -1) {\r\n        const removedItem = this._cachedItems.splice(indexToRemove, 1)[0];\r\n        removedItem.destroy();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes an image from cache, does not destroy() it, just removes.\r\n   *\r\n   * @param {number} index\r\n   */\r\n  removeByIndex(index) {\r\n    const indexToRemove = this._cachedItems.findIndex(item => item.index === index);\r\n    if (indexToRemove !== -1) {\r\n      this._cachedItems.splice(indexToRemove, 1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @param {number} index\r\n   * @returns {Content | undefined}\r\n   */\r\n  getContentByIndex(index) {\r\n    return this._cachedItems.find(content => content.index === index);\r\n  }\r\n\r\n  destroy() {\r\n    this._cachedItems.forEach(content => content.destroy());\r\n    this._cachedItems = [];\r\n  }\r\n}\r\n\r\nexport default ContentLoader;\r\n", "import Eventable from './eventable.js';\r\nimport { getElementsFromOption } from '../util/util.js';\r\nimport Content from '../slide/content.js';\r\nimport { lazyLoadData } from '../slide/loader.js';\r\n\r\n/** @typedef {import(\"../photoswipe.js\").default} PhotoSwipe */\r\n/** @typedef {import(\"../slide/slide.js\").SlideData} SlideData */\r\n\r\n/**\r\n * PhotoSwipe base class that can retrieve data about every slide.\r\n * Shared by PhotoSwipe Core and PhotoSwipe Lightbox\r\n */\r\nclass PhotoSwipeBase extends Eventable {\r\n  /**\r\n   * Get total number of slides\r\n   *\r\n   * @returns {number}\r\n   */\r\n  getNumItems() {\r\n    let numItems = 0;\r\n    const dataSource = this.options?.dataSource;\r\n\r\n    if (dataSource && 'length' in dataSource) {\r\n      // may be an array or just object with length property\r\n      numItems = dataSource.length;\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      if (dataSource.items) {\r\n        numItems = dataSource.items.length;\r\n      }\r\n    }\r\n\r\n    // legacy event, before filters were introduced\r\n    const event = this.dispatch('numItems', {\r\n      dataSource,\r\n      numItems\r\n    });\r\n    return this.applyFilters('numItems', event.numItems, dataSource);\r\n  }\r\n\r\n  /**\r\n   * @param {SlideData} slideData\r\n   * @param {number} index\r\n   * @returns {Content}\r\n   */\r\n  createContentFromData(slideData, index) {\r\n    return new Content(slideData, this, index);\r\n  }\r\n\r\n  /**\r\n   * Get item data by index.\r\n   *\r\n   * \"item data\" should contain normalized information that PhotoSwipe needs to generate a slide.\r\n   * For example, it may contain properties like\r\n   * `src`, `srcset`, `w`, `h`, which will be used to generate a slide with image.\r\n   *\r\n   * @param {number} index\r\n   * @returns {SlideData}\r\n   */\r\n  getItemData(index) {\r\n    const dataSource = this.options?.dataSource;\r\n    /** @type {SlideData | HTMLElement} */\r\n    let dataSourceItem = {};\r\n    if (Array.isArray(dataSource)) {\r\n      // Datasource is an array of elements\r\n      dataSourceItem = dataSource[index];\r\n    } else if (dataSource && 'gallery' in dataSource) {\r\n      // dataSource has gallery property,\r\n      // thus it was created by Lightbox, based on\r\n      // gallery and children options\r\n\r\n      // query DOM elements\r\n      if (!dataSource.items) {\r\n        dataSource.items = this._getGalleryDOMElements(dataSource.gallery);\r\n      }\r\n\r\n      dataSourceItem = dataSource.items[index];\r\n    }\r\n\r\n    let itemData = dataSourceItem;\r\n\r\n    if (itemData instanceof Element) {\r\n      itemData = this._domElementToItemData(itemData);\r\n    }\r\n\r\n    // Dispatching the itemData event,\r\n    // it's a legacy verion before filters were introduced\r\n    const event = this.dispatch('itemData', {\r\n      itemData: itemData || {},\r\n      index\r\n    });\r\n\r\n    return this.applyFilters('itemData', event.itemData, index);\r\n  }\r\n\r\n  /**\r\n   * Get array of gallery DOM elements,\r\n   * based on childSelector and gallery element.\r\n   *\r\n   * @param {HTMLElement} galleryElement\r\n   * @returns {HTMLElement[]}\r\n   */\r\n  _getGalleryDOMElements(galleryElement) {\r\n    if (this.options?.children || this.options?.childSelector) {\r\n      return getElementsFromOption(\r\n        this.options.children,\r\n        this.options.childSelector,\r\n        galleryElement\r\n      ) || [];\r\n    }\r\n\r\n    return [galleryElement];\r\n  }\r\n\r\n  /**\r\n   * Converts DOM element to item data object.\r\n   *\r\n   * @param {HTMLElement} element DOM element\r\n   * @returns {SlideData}\r\n   */\r\n  _domElementToItemData(element) {\r\n    /** @type {SlideData} */\r\n    const itemData = {\r\n      element\r\n    };\r\n\r\n    const linkEl = /** @type {HTMLAnchorElement} */ (\r\n      element.tagName === 'A'\r\n        ? element\r\n        : element.querySelector('a')\r\n    );\r\n\r\n    if (linkEl) {\r\n      // src comes from data-pswp-src attribute,\r\n      // if it's empty link href is used\r\n      itemData.src = linkEl.dataset.pswpSrc || linkEl.href;\r\n\r\n      if (linkEl.dataset.pswpSrcset) {\r\n        itemData.srcset = linkEl.dataset.pswpSrcset;\r\n      }\r\n\r\n      itemData.width = linkEl.dataset.pswpWidth ? parseInt(linkEl.dataset.pswpWidth, 10) : 0;\r\n      itemData.height = linkEl.dataset.pswpHeight ? parseInt(linkEl.dataset.pswpHeight, 10) : 0;\r\n\r\n      // support legacy w & h properties\r\n      itemData.w = itemData.width;\r\n      itemData.h = itemData.height;\r\n\r\n      if (linkEl.dataset.pswpType) {\r\n        itemData.type = linkEl.dataset.pswpType;\r\n      }\r\n\r\n      const thumbnailEl = element.querySelector('img');\r\n\r\n      if (thumbnailEl) {\r\n        // msrc is URL to placeholder image that's displayed before large image is loaded\r\n        // by default it's displayed only for the first slide\r\n        itemData.msrc = thumbnailEl.currentSrc || thumbnailEl.src;\r\n        itemData.alt = thumbnailEl.getAttribute('alt') ?? '';\r\n      }\r\n\r\n      if (linkEl.dataset.pswpCropped || linkEl.dataset.cropped) {\r\n        itemData.thumbCropped = true;\r\n      }\r\n    }\r\n\r\n    return this.applyFilters('domItemData', itemData, element, linkEl);\r\n  }\r\n\r\n  /**\r\n   * Lazy-load by slide data\r\n   *\r\n   * @param {SlideData} itemData Data about the slide\r\n   * @param {number} index\r\n   * @returns {Content} Image that is being decoded or false.\r\n   */\r\n  lazyLoadData(itemData, index) {\r\n    return lazyLoadData(itemData, this, index);\r\n  }\r\n}\r\n\r\nexport default PhotoSwipeBase;\r\n", "import {\r\n  setTransform,\r\n  equalizePoints,\r\n  decodeImage,\r\n  toTransformString\r\n} from './util/util.js';\r\n\r\n/** @typedef {import('./photoswipe.js').default} PhotoSwipe */\r\n/** @typedef {import('./slide/get-thumb-bounds.js').Bounds} Bounds */\r\n/** @typedef {import('./util/animations.js').AnimationProps} AnimationProps */\r\n\r\n// some browsers do not paint\r\n// elements which opacity is set to 0,\r\n// since we need to pre-render elements for the animation -\r\n// we set it to the minimum amount\r\nconst MIN_OPACITY = 0.003;\r\n\r\n/**\r\n * Manages opening and closing transitions of the PhotoSwipe.\r\n *\r\n * It can perform zoom, fade or no transition.\r\n */\r\nclass Opener {\r\n  /**\r\n   * @param {PhotoSwipe} pswp\r\n   */\r\n  constructor(pswp) {\r\n    this.pswp = pswp;\r\n    this.isClosed = true;\r\n    this.isOpen = false;\r\n    this.isClosing = false;\r\n    this.isOpening = false;\r\n    /**\r\n     * @private\r\n     * @type {number | false | undefined}\r\n     */\r\n    this._duration = undefined;\r\n    /** @private */\r\n    this._useAnimation = false;\r\n    /** @private */\r\n    this._croppedZoom = false;\r\n    /** @private */\r\n    this._animateRootOpacity = false;\r\n    /** @private */\r\n    this._animateBgOpacity = false;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | HTMLImageElement | null | undefined }\r\n     */\r\n    this._placeholder = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\r\n    this._opacityElement = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLDivElement | undefined }\r\n     */\r\n    this._cropContainer1 = undefined;\r\n    /**\r\n     * @private\r\n     * @type { HTMLElement | null | undefined }\r\n     */\r\n    this._cropContainer2 = undefined;\r\n\r\n    /**\r\n     * @private\r\n     * @type {Bounds | undefined}\r\n     */\r\n    this._thumbBounds = undefined;\r\n\r\n\r\n    this._prepareOpen = this._prepareOpen.bind(this);\r\n\r\n    // Override initial zoom and pan position\r\n    pswp.on('firstZoomPan', this._prepareOpen);\r\n  }\r\n\r\n  open() {\r\n    this._prepareOpen();\r\n    this._start();\r\n  }\r\n\r\n  close() {\r\n    if (this.isClosed || this.isClosing || this.isOpening) {\r\n      // if we close during opening animation\r\n      // for now do nothing,\r\n      // browsers aren't good at changing the direction of the CSS transition\r\n      return;\r\n    }\r\n\r\n    const slide = this.pswp.currSlide;\r\n\r\n    this.isOpen = false;\r\n    this.isOpening = false;\r\n    this.isClosing = true;\r\n    this._duration = this.pswp.options.hideAnimationDuration;\r\n\r\n    if (slide && slide.currZoomLevel * slide.width >= this.pswp.options.maxWidthToAnimate) {\r\n      this._duration = 0;\r\n    }\r\n\r\n    this._applyStartProps();\r\n    setTimeout(() => {\r\n      this._start();\r\n    }, this._croppedZoom ? 30 : 0);\r\n  }\r\n\r\n  /** @private */\r\n  _prepareOpen() {\r\n    this.pswp.off('firstZoomPan', this._prepareOpen);\r\n    if (!this.isOpening) {\r\n      const slide = this.pswp.currSlide;\r\n      this.isOpening = true;\r\n      this.isClosing = false;\r\n      this._duration = this.pswp.options.showAnimationDuration;\r\n      if (slide && slide.zoomLevels.initial * slide.width >= this.pswp.options.maxWidthToAnimate) {\r\n        this._duration = 0;\r\n      }\r\n      this._applyStartProps();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _applyStartProps() {\r\n    const { pswp } = this;\r\n    const slide = this.pswp.currSlide;\r\n    const { options } = pswp;\r\n\r\n    if (options.showHideAnimationType === 'fade') {\r\n      options.showHideOpacity = true;\r\n      this._thumbBounds = undefined;\r\n    } else if (options.showHideAnimationType === 'none') {\r\n      options.showHideOpacity = false;\r\n      this._duration = 0;\r\n      this._thumbBounds = undefined;\r\n    } else if (this.isOpening && pswp._initialThumbBounds) {\r\n      // Use initial bounds if defined\r\n      this._thumbBounds = pswp._initialThumbBounds;\r\n    } else {\r\n      this._thumbBounds = this.pswp.getThumbBounds();\r\n    }\r\n\r\n    this._placeholder = slide?.getPlaceholderElement();\r\n\r\n    pswp.animations.stopAll();\r\n\r\n    // Discard animations when duration is less than 50ms\r\n    this._useAnimation = Boolean(this._duration && this._duration > 50);\r\n    this._animateZoom = Boolean(this._thumbBounds)\r\n                        && slide?.content.usePlaceholder()\r\n                        && (!this.isClosing || !pswp.mainScroll.isShifted());\r\n    if (!this._animateZoom) {\r\n      this._animateRootOpacity = true;\r\n\r\n      if (this.isOpening && slide) {\r\n        slide.zoomAndPanToInitial();\r\n        slide.applyCurrentZoomPan();\r\n      }\r\n    } else {\r\n      this._animateRootOpacity = options.showHideOpacity ?? false;\r\n    }\r\n    this._animateBgOpacity = !this._animateRootOpacity && this.pswp.options.bgOpacity > MIN_OPACITY;\r\n    this._opacityElement = this._animateRootOpacity ? pswp.element : pswp.bg;\r\n\r\n    if (!this._useAnimation) {\r\n      this._duration = 0;\r\n      this._animateZoom = false;\r\n      this._animateBgOpacity = false;\r\n      this._animateRootOpacity = true;\r\n      if (this.isOpening) {\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        pswp.applyBgOpacity(1);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this._animateZoom && this._thumbBounds && this._thumbBounds.innerRect) {\r\n      // Properties are used when animation from cropped thumbnail\r\n      this._croppedZoom = true;\r\n      this._cropContainer1 = this.pswp.container;\r\n      this._cropContainer2 = this.pswp.currSlide?.holderElement;\r\n\r\n      if (pswp.container) {\r\n        pswp.container.style.overflow = 'hidden';\r\n        pswp.container.style.width = pswp.viewportSize.x + 'px';\r\n      }\r\n    } else {\r\n      this._croppedZoom = false;\r\n    }\r\n\r\n    if (this.isOpening) {\r\n      // Apply styles before opening transition\r\n      if (this._animateRootOpacity) {\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        pswp.applyBgOpacity(1);\r\n      } else {\r\n        if (this._animateBgOpacity && pswp.bg) {\r\n          pswp.bg.style.opacity = String(MIN_OPACITY);\r\n        }\r\n        if (pswp.element) {\r\n          pswp.element.style.opacity = '1';\r\n        }\r\n      }\r\n\r\n      if (this._animateZoom) {\r\n        this._setClosedStateZoomPan();\r\n        if (this._placeholder) {\r\n          // tell browser that we plan to animate the placeholder\r\n          this._placeholder.style.willChange = 'transform';\r\n\r\n          // hide placeholder to allow hiding of\r\n          // elements that overlap it (such as icons over the thumbnail)\r\n          this._placeholder.style.opacity = String(MIN_OPACITY);\r\n        }\r\n      }\r\n    } else if (this.isClosing) {\r\n      // hide nearby slides to make sure that\r\n      // they are not painted during the transition\r\n      if (pswp.mainScroll.itemHolders[0]) {\r\n        pswp.mainScroll.itemHolders[0].el.style.display = 'none';\r\n      }\r\n      if (pswp.mainScroll.itemHolders[2]) {\r\n        pswp.mainScroll.itemHolders[2].el.style.display = 'none';\r\n      }\r\n\r\n      if (this._croppedZoom) {\r\n        if (pswp.mainScroll.x !== 0) {\r\n          // shift the main scroller to zero position\r\n          pswp.mainScroll.resetPosition();\r\n          pswp.mainScroll.resize();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _start() {\r\n    if (this.isOpening\r\n        && this._useAnimation\r\n        && this._placeholder\r\n        && this._placeholder.tagName === 'IMG') {\r\n      // To ensure smooth animation\r\n      // we wait till the current slide image placeholder is decoded,\r\n      // but no longer than 250ms,\r\n      // and no shorter than 50ms\r\n      // (just using requestanimationframe is not enough in Firefox,\r\n      // for some reason)\r\n      new Promise((resolve) => {\r\n        let decoded = false;\r\n        let isDelaying = true;\r\n        decodeImage(/** @type {HTMLImageElement} */ (this._placeholder)).finally(() => {\r\n          decoded = true;\r\n          if (!isDelaying) {\r\n            resolve(true);\r\n          }\r\n        });\r\n        setTimeout(() => {\r\n          isDelaying = false;\r\n          if (decoded) {\r\n            resolve(true);\r\n          }\r\n        }, 50);\r\n        setTimeout(resolve, 250);\r\n      }).finally(() => this._initiate());\r\n    } else {\r\n      this._initiate();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _initiate() {\r\n    this.pswp.element?.style.setProperty('--pswp-transition-duration', this._duration + 'ms');\r\n\r\n    this.pswp.dispatch(\r\n      this.isOpening ? 'openingAnimationStart' : 'closingAnimationStart'\r\n    );\r\n\r\n    // legacy event\r\n    this.pswp.dispatch(\r\n      /** @type {'initialZoomIn' | 'initialZoomOut'} */\r\n      ('initialZoom' + (this.isOpening ? 'In' : 'Out'))\r\n    );\r\n\r\n    this.pswp.element?.classList.toggle('pswp--ui-visible', this.isOpening);\r\n\r\n    if (this.isOpening) {\r\n      if (this._placeholder) {\r\n        // unhide the placeholder\r\n        this._placeholder.style.opacity = '1';\r\n      }\r\n      this._animateToOpenState();\r\n    } else if (this.isClosing) {\r\n      this._animateToClosedState();\r\n    }\r\n\r\n    if (!this._useAnimation) {\r\n      this._onAnimationComplete();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _onAnimationComplete() {\r\n    const { pswp } = this;\r\n    this.isOpen = this.isOpening;\r\n    this.isClosed = this.isClosing;\r\n    this.isOpening = false;\r\n    this.isClosing = false;\r\n\r\n    pswp.dispatch(\r\n      this.isOpen ? 'openingAnimationEnd' : 'closingAnimationEnd'\r\n    );\r\n\r\n    // legacy event\r\n    pswp.dispatch(\r\n      /** @type {'initialZoomInEnd' | 'initialZoomOutEnd'} */\r\n      ('initialZoom' + (this.isOpen ? 'InEnd' : 'OutEnd'))\r\n    );\r\n\r\n    if (this.isClosed) {\r\n      pswp.destroy();\r\n    } else if (this.isOpen) {\r\n      if (this._animateZoom && pswp.container) {\r\n        pswp.container.style.overflow = 'visible';\r\n        pswp.container.style.width = '100%';\r\n      }\r\n      pswp.currSlide?.applyCurrentZoomPan();\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _animateToOpenState() {\r\n    const { pswp } = this;\r\n    if (this._animateZoom) {\r\n      if (this._croppedZoom && this._cropContainer1 && this._cropContainer2) {\r\n        this._animateTo(this._cropContainer1, 'transform', 'translate3d(0,0,0)');\r\n        this._animateTo(this._cropContainer2, 'transform', 'none');\r\n      }\r\n\r\n      if (pswp.currSlide) {\r\n        pswp.currSlide.zoomAndPanToInitial();\r\n        this._animateTo(\r\n          pswp.currSlide.container,\r\n          'transform',\r\n          pswp.currSlide.getCurrentTransform()\r\n        );\r\n      }\r\n    }\r\n\r\n    if (this._animateBgOpacity && pswp.bg) {\r\n      this._animateTo(pswp.bg, 'opacity', String(pswp.options.bgOpacity));\r\n    }\r\n\r\n    if (this._animateRootOpacity && pswp.element) {\r\n      this._animateTo(pswp.element, 'opacity', '1');\r\n    }\r\n  }\r\n\r\n  /** @private */\r\n  _animateToClosedState() {\r\n    const { pswp } = this;\r\n\r\n    if (this._animateZoom) {\r\n      this._setClosedStateZoomPan(true);\r\n    }\r\n\r\n    // do not animate opacity if it's already at 0\r\n    if (this._animateBgOpacity && pswp.bgOpacity > 0.01 && pswp.bg) {\r\n      this._animateTo(pswp.bg, 'opacity', '0');\r\n    }\r\n\r\n    if (this._animateRootOpacity && pswp.element) {\r\n      this._animateTo(pswp.element, 'opacity', '0');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {boolean} [animate]\r\n   */\r\n  _setClosedStateZoomPan(animate) {\r\n    if (!this._thumbBounds) return;\r\n\r\n    const { pswp } = this;\r\n    const { innerRect } = this._thumbBounds;\r\n    const { currSlide, viewportSize } = pswp;\r\n\r\n    if (this._croppedZoom && innerRect && this._cropContainer1 && this._cropContainer2) {\r\n      const containerOnePanX = -viewportSize.x + (this._thumbBounds.x - innerRect.x) + innerRect.w;\r\n      const containerOnePanY = -viewportSize.y + (this._thumbBounds.y - innerRect.y) + innerRect.h;\r\n      const containerTwoPanX = viewportSize.x - innerRect.w;\r\n      const containerTwoPanY = viewportSize.y - innerRect.h;\r\n\r\n\r\n      if (animate) {\r\n        this._animateTo(\r\n          this._cropContainer1,\r\n          'transform',\r\n          toTransformString(containerOnePanX, containerOnePanY)\r\n        );\r\n\r\n        this._animateTo(\r\n          this._cropContainer2,\r\n          'transform',\r\n          toTransformString(containerTwoPanX, containerTwoPanY)\r\n        );\r\n      } else {\r\n        setTransform(this._cropContainer1, containerOnePanX, containerOnePanY);\r\n        setTransform(this._cropContainer2, containerTwoPanX, containerTwoPanY);\r\n      }\r\n    }\r\n\r\n    if (currSlide) {\r\n      equalizePoints(currSlide.pan, innerRect || this._thumbBounds);\r\n      currSlide.currZoomLevel = this._thumbBounds.w / currSlide.width;\r\n      if (animate) {\r\n        this._animateTo(currSlide.container, 'transform', currSlide.getCurrentTransform());\r\n      } else {\r\n        currSlide.applyCurrentZoomPan();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {HTMLElement} target\r\n   * @param {'transform' | 'opacity'} prop\r\n   * @param {string} propValue\r\n   */\r\n  _animateTo(target, prop, propValue) {\r\n    if (!this._duration) {\r\n      target.style[prop] = propValue;\r\n      return;\r\n    }\r\n\r\n    const { animations } = this.pswp;\r\n    /** @type {AnimationProps} */\r\n    const animProps = {\r\n      duration: this._duration,\r\n      easing: this.pswp.options.easing,\r\n      onComplete: () => {\r\n        if (!animations.activeAnimations.length) {\r\n          this._onAnimationComplete();\r\n        }\r\n      },\r\n      target,\r\n    };\r\n    animProps[prop] = propValue;\r\n    animations.startTransition(animProps);\r\n  }\r\n}\r\n\r\nexport default Opener;\r\n", "import {\r\n  createElement,\r\n  equalizePoints,\r\n  pointsEqual,\r\n  clamp,\r\n} from './util/util.js';\r\n\r\nimport DOMEvents from './util/dom-events.js';\r\nimport Slide from './slide/slide.js';\r\nimport Gestures from './gestures/gestures.js';\r\nimport MainScroll from './main-scroll.js';\r\n\r\nimport Keyboard from './keyboard.js';\r\nimport Animations from './util/animations.js';\r\nimport ScrollWheel from './scroll-wheel.js';\r\nimport UI from './ui/ui.js';\r\nimport { getViewportSize } from './util/viewport-size.js';\r\nimport { getThumbBounds } from './slide/get-thumb-bounds.js';\r\nimport PhotoSwipeBase from './core/base.js';\r\nimport Opener from './opener.js';\r\nimport ContentLoader from './slide/loader.js';\r\n\r\n/**\r\n * @template T\r\n * @typedef {import('./types.js').Type<T>} Type<T>\r\n */\r\n\r\n/** @typedef {import('./slide/slide.js').SlideData} SlideData */\r\n/** @typedef {import('./slide/zoom-level.js').ZoomLevelOption} ZoomLevelOption */\r\n/** @typedef {import('./ui/ui-element.js').UIElementData} UIElementData */\r\n/** @typedef {import('./main-scroll.js').ItemHolder} ItemHolder */\r\n/** @typedef {import('./core/eventable.js').PhotoSwipeEventsMap} PhotoSwipeEventsMap */\r\n/** @typedef {import('./core/eventable.js').PhotoSwipeFiltersMap} PhotoSwipeFiltersMap */\r\n/** @typedef {import('./slide/get-thumb-bounds').Bounds} Bounds */\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').EventCallback<T>} EventCallback<T>\r\n */\r\n/**\r\n * @template {keyof PhotoSwipeEventsMap} T\r\n * @typedef {import('./core/eventable.js').AugmentedEvent<T>} AugmentedEvent<T>\r\n */\r\n\r\n/** @typedef {{ x: number; y: number; id?: string | number }} Point */\r\n/** @typedef {{ top: number; bottom: number; left: number; right: number }} Padding */\r\n/** @typedef {SlideData[]} DataSourceArray */\r\n/** @typedef {{ gallery: HTMLElement; items?: HTMLElement[] }} DataSourceObject */\r\n/** @typedef {DataSourceArray | DataSourceObject} DataSource */\r\n/** @typedef {(point: Point, originalEvent: PointerEvent) => void} ActionFn */\r\n/** @typedef {'close' | 'next' | 'zoom' | 'zoom-or-close' | 'toggle-controls'} ActionType */\r\n/** @typedef {Type<PhotoSwipe> | { default: Type<PhotoSwipe> }} PhotoSwipeModule */\r\n/** @typedef {PhotoSwipeModule | Promise<PhotoSwipeModule> | (() => Promise<PhotoSwipeModule>)} PhotoSwipeModuleOption */\r\n\r\n/**\r\n * @typedef {string | NodeListOf<HTMLElement> | HTMLElement[] | HTMLElement} ElementProvider\r\n */\r\n\r\n/** @typedef {Partial<PreparedPhotoSwipeOptions>} PhotoSwipeOptions https://photoswipe.com/options/ */\r\n/**\r\n * @typedef {Object} PreparedPhotoSwipeOptions\r\n *\r\n * @prop {DataSource} [dataSource]\r\n * Pass an array of any items via dataSource option. Its length will determine amount of slides\r\n * (which may be modified further from numItems event).\r\n *\r\n * Each item should contain data that you need to generate slide\r\n * (for image slide it would be src (image URL), width (image width), height, srcset, alt).\r\n *\r\n * If these properties are not present in your initial array, you may \"pre-parse\" each item from itemData filter.\r\n *\r\n * @prop {number} bgOpacity\r\n * Background backdrop opacity, always define it via this option and not via CSS rgba color.\r\n *\r\n * @prop {number} spacing\r\n * Spacing between slides. Defined as ratio relative to the viewport width (0.1 = 10% of viewport).\r\n *\r\n * @prop {boolean} allowPanToNext\r\n * Allow swipe navigation to the next slide when the current slide is zoomed. Does not apply to mouse events.\r\n *\r\n * @prop {boolean} loop\r\n * If set to true you'll be able to swipe from the last to the first image.\r\n * Option is always false when there are less than 3 slides.\r\n *\r\n * @prop {boolean} [wheelToZoom]\r\n * By default PhotoSwipe zooms image with ctrl-wheel, if you enable this option - image will zoom just via wheel.\r\n *\r\n * @prop {boolean} pinchToClose\r\n * Pinch touch gesture to close the gallery.\r\n *\r\n * @prop {boolean} closeOnVerticalDrag\r\n * Vertical drag gesture to close the PhotoSwipe.\r\n *\r\n * @prop {Padding} [padding]\r\n * Slide area padding (in pixels).\r\n *\r\n * @prop {(viewportSize: Point, itemData: SlideData, index: number) => Padding} [paddingFn]\r\n * The option is checked frequently, so make sure it's performant. Overrides padding option if defined. For example:\r\n *\r\n * @prop {number | false} hideAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} showAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {number | false} zoomAnimationDuration\r\n * Transition duration in milliseconds, can be 0.\r\n *\r\n * @prop {string} easing\r\n * String, 'cubic-bezier(.4,0,.22,1)'. CSS easing function for open/close/zoom transitions.\r\n *\r\n * @prop {boolean} escKey\r\n * Esc key to close.\r\n *\r\n * @prop {boolean} arrowKeys\r\n * Left/right arrow keys for navigation.\r\n *\r\n * @prop {boolean} trapFocus\r\n * Trap focus within PhotoSwipe element while it's open.\r\n *\r\n * @prop {boolean} returnFocus\r\n * Restore focus the last active element after PhotoSwipe is closed.\r\n *\r\n * @prop {boolean} clickToCloseNonZoomable\r\n * If image is not zoomable (for example, smaller than viewport) it can be closed by clicking on it.\r\n *\r\n * @prop {ActionType | ActionFn | false} imageClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} bgClickAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} tapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {ActionType | ActionFn | false} doubleTapAction\r\n * Refer to click and tap actions page.\r\n *\r\n * @prop {number} preloaderDelay\r\n * Delay before the loading indicator will be displayed,\r\n * if image is loaded during it - the indicator will not be displayed at all. Can be zero.\r\n *\r\n * @prop {string} indexIndicatorSep\r\n * Used for slide count indicator (\"1 of 10 \").\r\n *\r\n * @prop {(options: PhotoSwipeOptions, pswp: PhotoSwipeBase) => Point} [getViewportSizeFn]\r\n * A function that should return slide viewport width and height, in format {x: 100, y: 100}.\r\n *\r\n * @prop {string} errorMsg\r\n * Message to display when the image wasn't able to load. If you need to display HTML - use contentErrorElement filter.\r\n *\r\n * @prop {[number, number]} preload\r\n * Lazy loading of nearby slides based on direction of movement. Should be an array with two integers,\r\n * first one - number of items to preload before the current image, second one - after the current image.\r\n * Two nearby images are always loaded.\r\n *\r\n * @prop {string} [mainClass]\r\n * Class that will be added to the root element of PhotoSwipe, may contain multiple separated by space.\r\n * Example on Styling page.\r\n *\r\n * @prop {HTMLElement} [appendToEl]\r\n * Element to which PhotoSwipe dialog will be appended when it opens.\r\n *\r\n * @prop {number} maxWidthToAnimate\r\n * Maximum width of image to animate, if initial rendered image width\r\n * is larger than this value - the opening/closing transition will be automatically disabled.\r\n *\r\n * @prop {string} [closeTitle]\r\n * Translating\r\n *\r\n * @prop {string} [zoomTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * Translating\r\n *\r\n * @prop {string} [arrowNextTitle]\r\n * Translating\r\n *\r\n * @prop {'zoom' | 'fade' | 'none'} [showHideAnimationType]\r\n * To adjust opening or closing transition type use lightbox option `showHideAnimationType` (`String`).\r\n * It supports three values - `zoom` (default), `fade` (default if there is no thumbnail) and `none`.\r\n *\r\n * Animations are automatically disabled if user `(prefers-reduced-motion: reduce)`.\r\n *\r\n * @prop {number} index\r\n * Defines start slide index.\r\n *\r\n * @prop {(e: MouseEvent) => number} [getClickedIndexFn]\r\n *\r\n * @prop {boolean} [arrowPrev]\r\n * @prop {boolean} [arrowNext]\r\n * @prop {boolean} [zoom]\r\n * @prop {boolean} [close]\r\n * @prop {boolean} [counter]\r\n *\r\n * @prop {string} [arrowPrevSVG]\r\n * @prop {string} [arrowNextSVG]\r\n * @prop {string} [zoomSVG]\r\n * @prop {string} [closeSVG]\r\n * @prop {string} [counterSVG]\r\n *\r\n * @prop {string} [arrowPrevTitle]\r\n * @prop {string} [arrowNextTitle]\r\n * @prop {string} [zoomTitle]\r\n * @prop {string} [closeTitle]\r\n * @prop {string} [counterTitle]\r\n *\r\n * @prop {ZoomLevelOption} [initialZoomLevel]\r\n * @prop {ZoomLevelOption} [secondaryZoomLevel]\r\n * @prop {ZoomLevelOption} [maxZoomLevel]\r\n *\r\n * @prop {boolean} [mouseMovePan]\r\n * @prop {Point | null} [initialPointerPos]\r\n * @prop {boolean} [showHideOpacity]\r\n *\r\n * @prop {PhotoSwipeModuleOption} [pswpModule]\r\n * @prop {() => Promise<any>} [openPromise]\r\n * @prop {boolean} [preloadFirstSlide]\r\n * @prop {ElementProvider} [gallery]\r\n * @prop {string} [gallerySelector]\r\n * @prop {ElementProvider} [children]\r\n * @prop {string} [childSelector]\r\n * @prop {string | false} [thumbSelector]\r\n */\r\n\r\n/** @type {PreparedPhotoSwipeOptions} */\r\nconst defaultOptions = {\r\n  allowPanToNext: true,\r\n  spacing: 0.1,\r\n  loop: true,\r\n  pinchToClose: true,\r\n  closeOnVerticalDrag: true,\r\n  hideAnimationDuration: 333,\r\n  showAnimationDuration: 333,\r\n  zoomAnimationDuration: 333,\r\n  escKey: true,\r\n  arrowKeys: true,\r\n  trapFocus: true,\r\n  returnFocus: true,\r\n  maxWidthToAnimate: 4000,\r\n  clickToCloseNonZoomable: true,\r\n  imageClickAction: 'zoom-or-close',\r\n  bgClickAction: 'close',\r\n  tapAction: 'toggle-controls',\r\n  doubleTapAction: 'zoom',\r\n  indexIndicatorSep: ' / ',\r\n  preloaderDelay: 2000,\r\n  bgOpacity: 0.8,\r\n\r\n  index: 0,\r\n  errorMsg: 'The image cannot be loaded',\r\n  preload: [1, 2],\r\n  easing: 'cubic-bezier(.4,0,.22,1)'\r\n};\r\n\r\n/**\r\n * PhotoSwipe Core\r\n */\r\nclass PhotoSwipe extends PhotoSwipeBase {\r\n  /**\r\n   * @param {PhotoSwipeOptions} [options]\r\n   */\r\n  constructor(options) {\r\n    super();\r\n\r\n    this.options = this._prepareOptions(options || {});\r\n\r\n    /**\r\n     * offset of viewport relative to document\r\n     *\r\n     * @type {Point}\r\n     */\r\n    this.offset = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * @type {Point}\r\n     * @private\r\n     */\r\n    this._prevViewportSize = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * Size of scrollable PhotoSwipe viewport\r\n     *\r\n     * @type {Point}\r\n     */\r\n    this.viewportSize = { x: 0, y: 0 };\r\n\r\n    /**\r\n     * background (backdrop) opacity\r\n     */\r\n    this.bgOpacity = 1;\r\n    this.currIndex = 0;\r\n    this.potentialIndex = 0;\r\n    this.isOpen = false;\r\n    this.isDestroying = false;\r\n    this.hasMouse = false;\r\n\r\n    /**\r\n     * @private\r\n     * @type {SlideData}\r\n     */\r\n    this._initialItemData = {};\r\n    /** @type {Bounds | undefined} */\r\n    this._initialThumbBounds = undefined;\r\n\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.topBar = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.element = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.template = undefined;\r\n    /** @type {HTMLDivElement | undefined} */\r\n    this.container = undefined;\r\n    /** @type {HTMLElement | undefined} */\r\n    this.scrollWrap = undefined;\r\n    /** @type {Slide | undefined} */\r\n    this.currSlide = undefined;\r\n\r\n    this.events = new DOMEvents();\r\n    this.animations = new Animations();\r\n    this.mainScroll = new MainScroll(this);\r\n    this.gestures = new Gestures(this);\r\n    this.opener = new Opener(this);\r\n    this.keyboard = new Keyboard(this);\r\n    this.contentLoader = new ContentLoader(this);\r\n  }\r\n\r\n  /** @returns {boolean} */\r\n  init() {\r\n    if (this.isOpen || this.isDestroying) {\r\n      return false;\r\n    }\r\n\r\n    this.isOpen = true;\r\n    this.dispatch('init'); // legacy\r\n    this.dispatch('beforeOpen');\r\n\r\n    this._createMainStructure();\r\n\r\n    // add classes to the root element of PhotoSwipe\r\n    let rootClasses = 'pswp--open';\r\n    if (this.gestures.supportsTouch) {\r\n      rootClasses += ' pswp--touch';\r\n    }\r\n    if (this.options.mainClass) {\r\n      rootClasses += ' ' + this.options.mainClass;\r\n    }\r\n    if (this.element) {\r\n      this.element.className += ' ' + rootClasses;\r\n    }\r\n\r\n    this.currIndex = this.options.index || 0;\r\n    this.potentialIndex = this.currIndex;\r\n    this.dispatch('firstUpdate'); // starting index can be modified here\r\n\r\n    // initialize scroll wheel handler to block the scroll\r\n    this.scrollWheel = new ScrollWheel(this);\r\n\r\n    // sanitize index\r\n    if (Number.isNaN(this.currIndex)\r\n        || this.currIndex < 0\r\n        || this.currIndex >= this.getNumItems()) {\r\n      this.currIndex = 0;\r\n    }\r\n\r\n    if (!this.gestures.supportsTouch) {\r\n      // enable mouse features if no touch support detected\r\n      this.mouseDetected();\r\n    }\r\n\r\n    // causes forced synchronous layout\r\n    this.updateSize();\r\n\r\n    this.offset.y = window.pageYOffset;\r\n\r\n    this._initialItemData = this.getItemData(this.currIndex);\r\n    this.dispatch('gettingData', {\r\n      index: this.currIndex,\r\n      data: this._initialItemData,\r\n      slide: undefined\r\n    });\r\n\r\n    // *Layout* - calculate size and position of elements here\r\n    this._initialThumbBounds = this.getThumbBounds();\r\n    this.dispatch('initialLayout');\r\n\r\n    this.on('openingAnimationEnd', () => {\r\n      const { itemHolders } = this.mainScroll;\r\n\r\n      // Add content to the previous and next slide\r\n      if (itemHolders[0]) {\r\n        itemHolders[0].el.style.display = 'block';\r\n        this.setContent(itemHolders[0], this.currIndex - 1);\r\n      }\r\n      if (itemHolders[2]) {\r\n        itemHolders[2].el.style.display = 'block';\r\n        this.setContent(itemHolders[2], this.currIndex + 1);\r\n      }\r\n\r\n      this.appendHeavy();\r\n\r\n      this.contentLoader.updateLazy();\r\n\r\n      this.events.add(window, 'resize', this._handlePageResize.bind(this));\r\n      this.events.add(window, 'scroll', this._updatePageScrollOffset.bind(this));\r\n      this.dispatch('bindEvents');\r\n    });\r\n\r\n    // set content for center slide (first time)\r\n    if (this.mainScroll.itemHolders[1]) {\r\n      this.setContent(this.mainScroll.itemHolders[1], this.currIndex);\r\n    }\r\n    this.dispatch('change');\r\n\r\n    this.opener.open();\r\n\r\n    this.dispatch('afterInit');\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Get looped slide index\r\n   * (for example, -1 will return the last slide)\r\n   *\r\n   * @param {number} index\r\n   * @returns {number}\r\n   */\r\n  getLoopedIndex(index) {\r\n    const numSlides = this.getNumItems();\r\n\r\n    if (this.options.loop) {\r\n      if (index > numSlides - 1) {\r\n        index -= numSlides;\r\n      }\r\n\r\n      if (index < 0) {\r\n        index += numSlides;\r\n      }\r\n    }\r\n\r\n    return clamp(index, 0, numSlides - 1);\r\n  }\r\n\r\n  appendHeavy() {\r\n    this.mainScroll.itemHolders.forEach((itemHolder) => {\r\n      itemHolder.slide?.appendHeavy();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Change the slide\r\n   * @param {number} index New index\r\n   */\r\n  goTo(index) {\r\n    this.mainScroll.moveIndexBy(\r\n      this.getLoopedIndex(index) - this.potentialIndex\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Go to the next slide.\r\n   */\r\n  next() {\r\n    this.goTo(this.potentialIndex + 1);\r\n  }\r\n\r\n  /**\r\n   * Go to the previous slide.\r\n   */\r\n  prev() {\r\n    this.goTo(this.potentialIndex - 1);\r\n  }\r\n\r\n  /**\r\n   * @see slide/slide.js zoomTo\r\n   *\r\n   * @param {Parameters<Slide['zoomTo']>} args\r\n   */\r\n  zoomTo(...args) {\r\n    this.currSlide?.zoomTo(...args);\r\n  }\r\n\r\n  /**\r\n   * @see slide/slide.js toggleZoom\r\n   */\r\n  toggleZoom() {\r\n    this.currSlide?.toggleZoom();\r\n  }\r\n\r\n  /**\r\n   * Close the gallery.\r\n   * After closing transition ends - destroy it\r\n   */\r\n  close() {\r\n    if (!this.opener.isOpen || this.isDestroying) {\r\n      return;\r\n    }\r\n\r\n    this.isDestroying = true;\r\n\r\n    this.dispatch('close');\r\n\r\n    this.events.removeAll();\r\n    this.opener.close();\r\n  }\r\n\r\n  /**\r\n   * Destroys the gallery:\r\n   * - instantly closes the gallery\r\n   * - unbinds events,\r\n   * - cleans intervals and timeouts\r\n   * - removes elements from DOM\r\n   */\r\n  destroy() {\r\n    if (!this.isDestroying) {\r\n      this.options.showHideAnimationType = 'none';\r\n      this.close();\r\n      return;\r\n    }\r\n\r\n    this.dispatch('destroy');\r\n\r\n    this._listeners = {};\r\n\r\n    if (this.scrollWrap) {\r\n      this.scrollWrap.ontouchmove = null;\r\n      this.scrollWrap.ontouchend = null;\r\n    }\r\n\r\n    this.element?.remove();\r\n\r\n    this.mainScroll.itemHolders.forEach((itemHolder) => {\r\n      itemHolder.slide?.destroy();\r\n    });\r\n\r\n    this.contentLoader.destroy();\r\n    this.events.removeAll();\r\n  }\r\n\r\n  /**\r\n   * Refresh/reload content of a slide by its index\r\n   *\r\n   * @param {number} slideIndex\r\n   */\r\n  refreshSlideContent(slideIndex) {\r\n    this.contentLoader.removeByIndex(slideIndex);\r\n    this.mainScroll.itemHolders.forEach((itemHolder, i) => {\r\n      let potentialHolderIndex = (this.currSlide?.index ?? 0) - 1 + i;\r\n      if (this.canLoop()) {\r\n        potentialHolderIndex = this.getLoopedIndex(potentialHolderIndex);\r\n      }\r\n      if (potentialHolderIndex === slideIndex) {\r\n        // set the new slide content\r\n        this.setContent(itemHolder, slideIndex, true);\r\n\r\n        // activate the new slide if it's current\r\n        if (i === 1) {\r\n          this.currSlide = itemHolder.slide;\r\n          itemHolder.slide?.setIsActive(true);\r\n        }\r\n      }\r\n    });\r\n\r\n    this.dispatch('change');\r\n  }\r\n\r\n\r\n  /**\r\n   * Set slide content\r\n   *\r\n   * @param {ItemHolder} holder mainScroll.itemHolders array item\r\n   * @param {number} index Slide index\r\n   * @param {boolean} [force] If content should be set even if index wasn't changed\r\n   */\r\n  setContent(holder, index, force) {\r\n    if (this.canLoop()) {\r\n      index = this.getLoopedIndex(index);\r\n    }\r\n\r\n    if (holder.slide) {\r\n      if (holder.slide.index === index && !force) {\r\n        // exit if holder already contains this slide\r\n        // this could be common when just three slides are used\r\n        return;\r\n      }\r\n\r\n      // destroy previous slide\r\n      holder.slide.destroy();\r\n      holder.slide = undefined;\r\n    }\r\n\r\n    // exit if no loop and index is out of bounds\r\n    if (!this.canLoop() && (index < 0 || index >= this.getNumItems())) {\r\n      return;\r\n    }\r\n\r\n    const itemData = this.getItemData(index);\r\n    holder.slide = new Slide(itemData, index, this);\r\n\r\n    // set current slide\r\n    if (index === this.currIndex) {\r\n      this.currSlide = holder.slide;\r\n    }\r\n\r\n    holder.slide.append(holder.el);\r\n  }\r\n\r\n  /** @returns {Point} */\r\n  getViewportCenterPoint() {\r\n    return {\r\n      x: this.viewportSize.x / 2,\r\n      y: this.viewportSize.y / 2\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Update size of all elements.\r\n   * Executed on init and on page resize.\r\n   *\r\n   * @param {boolean} [force] Update size even if size of viewport was not changed.\r\n   */\r\n  updateSize(force) {\r\n    // let item;\r\n    // let itemIndex;\r\n\r\n    if (this.isDestroying) {\r\n      // exit if PhotoSwipe is closed or closing\r\n      // (to avoid errors, as resize event might be delayed)\r\n      return;\r\n    }\r\n\r\n    //const newWidth = this.scrollWrap.clientWidth;\r\n    //const newHeight = this.scrollWrap.clientHeight;\r\n\r\n    const newViewportSize = getViewportSize(this.options, this);\r\n\r\n    if (!force && pointsEqual(newViewportSize, this._prevViewportSize)) {\r\n      // Exit if dimensions were not changed\r\n      return;\r\n    }\r\n\r\n    //this._prevViewportSize.x = newWidth;\r\n    //this._prevViewportSize.y = newHeight;\r\n    equalizePoints(this._prevViewportSize, newViewportSize);\r\n\r\n    this.dispatch('beforeResize');\r\n\r\n    equalizePoints(this.viewportSize, this._prevViewportSize);\r\n\r\n    this._updatePageScrollOffset();\r\n\r\n    this.dispatch('viewportSize');\r\n\r\n    // Resize slides only after opener animation is finished\r\n    // and don't re-calculate size on inital size update\r\n    this.mainScroll.resize(this.opener.isOpen);\r\n\r\n    if (!this.hasMouse && window.matchMedia('(any-hover: hover)').matches) {\r\n      this.mouseDetected();\r\n    }\r\n\r\n    this.dispatch('resize');\r\n  }\r\n\r\n  /**\r\n   * @param {number} opacity\r\n   */\r\n  applyBgOpacity(opacity) {\r\n    this.bgOpacity = Math.max(opacity, 0);\r\n    if (this.bg) {\r\n      this.bg.style.opacity = String(this.bgOpacity * this.options.bgOpacity);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Whether mouse is detected\r\n   */\r\n  mouseDetected() {\r\n    if (!this.hasMouse) {\r\n      this.hasMouse = true;\r\n      this.element?.classList.add('pswp--has_mouse');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Page resize event handler\r\n   *\r\n   * @private\r\n   */\r\n  _handlePageResize() {\r\n    this.updateSize();\r\n\r\n    // In iOS webview, if element size depends on document size,\r\n    // it'll be measured incorrectly in resize event\r\n    //\r\n    // https://bugs.webkit.org/show_bug.cgi?id=170595\r\n    // https://hackernoon.com/onresize-event-broken-in-mobile-safari-d8469027bf4d\r\n    if (/iPhone|iPad|iPod/i.test(window.navigator.userAgent)) {\r\n      setTimeout(() => {\r\n        this.updateSize();\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Page scroll offset is used\r\n   * to get correct coordinates\r\n   * relative to PhotoSwipe viewport.\r\n   *\r\n   * @private\r\n   */\r\n  _updatePageScrollOffset() {\r\n    this.setScrollOffset(0, window.pageYOffset);\r\n  }\r\n\r\n  /**\r\n   * @param {number} x\r\n   * @param {number} y\r\n   */\r\n  setScrollOffset(x, y) {\r\n    this.offset.x = x;\r\n    this.offset.y = y;\r\n    this.dispatch('updateScrollOffset');\r\n  }\r\n\r\n  /**\r\n   * Create main HTML structure of PhotoSwipe,\r\n   * and add it to DOM\r\n   *\r\n   * @private\r\n   */\r\n  _createMainStructure() {\r\n    // root DOM element of PhotoSwipe (.pswp)\r\n    this.element = createElement('pswp', 'div');\r\n    this.element.setAttribute('tabindex', '-1');\r\n    this.element.setAttribute('role', 'dialog');\r\n\r\n    // template is legacy prop\r\n    this.template = this.element;\r\n\r\n    // Background is added as a separate element,\r\n    // as animating opacity is faster than animating rgba()\r\n    this.bg = createElement('pswp__bg', 'div', this.element);\r\n    this.scrollWrap = createElement('pswp__scroll-wrap', 'section', this.element);\r\n    this.container = createElement('pswp__container', 'div', this.scrollWrap);\r\n\r\n    // aria pattern: carousel\r\n    this.scrollWrap.setAttribute('aria-roledescription', 'carousel');\r\n    this.container.setAttribute('aria-live', 'off');\r\n    this.container.setAttribute('id', 'pswp__items');\r\n\r\n    this.mainScroll.appendHolders();\r\n\r\n    this.ui = new UI(this);\r\n    this.ui.init();\r\n\r\n    // append to DOM\r\n    (this.options.appendToEl || document.body).appendChild(this.element);\r\n  }\r\n\r\n\r\n  /**\r\n   * Get position and dimensions of small thumbnail\r\n   *   {x:,y:,w:}\r\n   *\r\n   * Height is optional (calculated based on the large image)\r\n   *\r\n   * @returns {Bounds | undefined}\r\n   */\r\n  getThumbBounds() {\r\n    return getThumbBounds(\r\n      this.currIndex,\r\n      this.currSlide ? this.currSlide.data : this._initialItemData,\r\n      this\r\n    );\r\n  }\r\n\r\n  /**\r\n   * If the PhotoSwipe can have continuous loop\r\n   * @returns Boolean\r\n   */\r\n  canLoop() {\r\n    return (this.options.loop && this.getNumItems() > 2);\r\n  }\r\n\r\n  /**\r\n   * @private\r\n   * @param {PhotoSwipeOptions} options\r\n   * @returns {PreparedPhotoSwipeOptions}\r\n   */\r\n  _prepareOptions(options) {\r\n    if (window.matchMedia('(prefers-reduced-motion), (update: slow)').matches) {\r\n      options.showHideAnimationType = 'none';\r\n      options.zoomAnimationDuration = 0;\r\n    }\r\n\r\n    /** @type {PreparedPhotoSwipeOptions} */\r\n    return {\r\n      ...defaultOptions,\r\n      ...options\r\n    };\r\n  }\r\n}\r\n\r\nexport default PhotoSwipe;\r\n"], "names": ["createElement", "className", "tagName", "appendToEl", "el", "document", "append<PERSON><PERSON><PERSON>", "equalizePoints", "p1", "p2", "x", "y", "id", "undefined", "roundPoint", "p", "Math", "round", "getDistanceBetween", "abs", "sqrt", "pointsEqual", "clamp", "val", "min", "max", "toTransformString", "scale", "propValue", "setTransform", "style", "transform", "defaultCSSEasing", "setTransitionStyle", "prop", "duration", "ease", "transition", "setWidthHeight", "w", "h", "width", "height", "removeTransitionStyle", "decodeImage", "img", "decode", "catch", "complete", "Promise", "resolve", "reject", "onload", "onerror", "LOAD_STATE", "IDLE", "LOADING", "LOADED", "ERROR", "specialKeyUsed", "e", "button", "ctrl<PERSON>ey", "metaKey", "altKey", "shift<PERSON>ey", "getElementsFromOption", "option", "legacySelector", "parent", "elements", "Element", "NodeList", "Array", "isArray", "from", "selector", "querySelectorAll", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "vendor", "match", "supportsPassive", "window", "addEventListener", "Object", "defineProperty", "get", "DOMEvents", "constructor", "_pool", "add", "target", "type", "listener", "passive", "_toggleListener", "remove", "removeAll", "for<PERSON>ach", "poolItem", "unbind", "skip<PERSON><PERSON>", "methodName", "types", "split", "eType", "filter", "push", "eventOptions", "getViewportSize", "options", "pswp", "getViewportSizeFn", "newViewportSize", "documentElement", "clientWidth", "innerHeight", "parsePaddingOption", "viewportSize", "itemData", "index", "paddingValue", "paddingFn", "padding", "legacyPropName", "toUpperCase", "slice", "Number", "getPanAreaSize", "PanBounds", "slide", "currZoomLevel", "center", "update", "reset", "_updateAxis", "dispatch", "axis", "elSize", "paddingProp", "data", "panAreaSize", "correctPan", "panOffset", "MAX_IMAGE_WIDTH", "ZoomLevel", "elementSize", "fit", "fill", "vFill", "initial", "secondary", "max<PERSON><PERSON><PERSON>", "maxHeight", "hRatio", "vRatio", "_getInitial", "_getSecondary", "_getMax", "zoomLevels", "slideData", "_parseZoomLevelOption", "optionPrefix", "optionName", "optionValue", "Slide", "isActive", "currIndex", "currentResolution", "pan", "isFirstSlide", "opener", "isOpen", "content", "contentLoader", "getContentBySlide", "container", "holderElement", "heavyAppended", "bounds", "prevDis<PERSON><PERSON><PERSON><PERSON>", "prevDisplayedHeight", "setIsActive", "activate", "deactivate", "append", "transform<PERSON><PERSON>in", "calculateSize", "load", "updateContentSize", "appendHeavy", "zoomAndPanToInitial", "applyCurrentZoomPan", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mainScroll", "isShifted", "defaultPrevented", "destroy", "hasSlide", "resize", "panTo", "force", "scaleMultiplier", "sizeChanged", "setDisplayedSize", "getPlaceholderElement", "placeholder", "element", "zoomTo", "destZoomLevel", "centerPoint", "transitionDuration", "ignoreBounds", "isZoomable", "animations", "stopAllPan", "prevZoomLevel", "setZoomLevel", "calculateZoomToPanOffset", "finishTransition", "_setResolution", "startTransition", "isPan", "name", "getCurrentTransform", "onComplete", "easing", "toggleZoom", "zoomAnimationDuration", "point", "totalPanDistance", "getViewportCenterPoint", "zoomFactor", "panX", "panY", "isPannable", "Boolean", "_applyZoomTransform", "currSlide", "zoom", "newResolution", "PAN_END_FRICTION", "VERTICAL_DRAG_FRICTION", "MIN_RATIO_TO_CLOSE", "MIN_NEXT_SLIDE_SPEED", "project", "initialVelocity", "decelerationRate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gestures", "startPan", "start", "stopAll", "change", "prevP1", "dragAxis", "closeOnVerticalDrag", "isMultitouch", "_setPanWithFriction", "bgOpacity", "_getVerticalDragRatio", "applyBgOpacity", "mainScrollChanged", "_panOrMoveMainScroll", "end", "velocity", "indexDiff", "mainScrollShiftDiff", "getCurrSlideX", "currentSlideVisibilityRatio", "moveIndexBy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_finishPanGestureForAxis", "panPos", "restoreBgOpacity", "projectedPosition", "vDragRatio", "projectedVDragRatio", "close", "correctedPanPosition", "dampingRatio", "initialBgOpacity", "totalPanDist", "startSpring", "onUpdate", "pos", "animationProgressRatio", "floor", "delta", "newMainScrollX", "moveTo", "newPan", "allowPanToNext", "currSlideMainScrollX", "isLeftToRight", "isRightToLeft", "wasAtMinPanPosition", "wasAtMaxPanPosition", "potentialPan", "customFriction", "corrected<PERSON>an", "UPPER_ZOOM_FRICTION", "LOWER_ZOOM_FRICTION", "getZoomPointsCenter", "Zoom<PERSON><PERSON><PERSON>", "_startPan", "_startZoomPoint", "_zoomPoint", "_wasOverFitZoomLevel", "_startZoomLevel", "startP1", "startP2", "minZoomLevel", "maxZoomLevel", "pinchToClose", "_calculatePanForZoomLevel", "ignoreGesture", "destinationZoomLevel", "currZoomLevelNeedsChange", "initialPan", "destinationPan", "panNeedsChange", "naturalFrequency", "now", "newZoomLevel", "didTapOnMainContent", "event", "closest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "originalEvent", "targetClassList", "classList", "isImageClick", "contains", "isBackgroundClick", "_doClickOrTapAction", "tap", "doubleTap", "actionName", "actionFullName", "call", "clickToCloseNonZoomable", "toggle", "AXIS_SWIPE_HYSTERISIS", "DOUBLE_TAP_DELAY", "MIN_TAP_DISTANCE", "Gestures", "prevP2", "_lastStartP1", "_intervalP1", "_numActivePoints", "_ongoingPointers", "_touchEventEnabled", "_pointerEventEnabled", "PointerEvent", "supportsTouch", "maxTouchPoints", "_intervalTime", "_velocityCalculated", "isDragging", "isZooming", "raf", "_tapTimer", "drag", "<PERSON><PERSON><PERSON><PERSON>", "on", "events", "scrollWrap", "_onClick", "bind", "_bindEvents", "ontouchmove", "ontouchend", "pref", "down", "up", "cancel", "cancelEvent", "onPointerDown", "onPointerMove", "onPointerUp", "isMousePointer", "pointerType", "preventDefault", "mouseDetected", "_preventPointerEventBehaviour", "_updatePoints", "_clearTapTimer", "_calculateDragDirection", "_updateStartPoints", "Date", "_rafStopLoop", "_rafRender<PERSON>oop", "_finishDrag", "_updateVelocity", "_finishTap", "_updatePrevPoints", "requestAnimationFrame", "time", "_getVelocity", "indexOf", "tap<PERSON>elay", "doubleTapAction", "setTimeout", "clearTimeout", "displacement", "cancelAnimationFrame", "preventPointerEvent", "applyFilters", "pointerEvent", "pointerIndex", "findIndex", "ongoingPointer", "pointerId", "splice", "_convertEventPosToPoint", "length", "touchEvent", "touches", "diff", "axisToCheck", "pageX", "offset", "pageY", "identifier", "stopPropagation", "MAIN_SCROLL_END_FRICTION", "MainScroll", "slideWidth", "_currPositionIndex", "_prevPositionIndex", "_containerShiftIndex", "itemHolders", "resizeSlides", "newSlideWidth", "spacing", "slideWidthChanged", "itemHolder", "resetPosition", "appendHolders", "i", "setAttribute", "display", "canBeSwiped", "getNumItems", "animate", "velocityX", "newIndex", "potentialIndex", "numSlides", "canLoop", "getLoopedIndex", "distance", "stopMainScroll", "destinationX", "updateCurrItem", "isMainScroll", "currDiff", "currDistance", "positionDifference", "diffAbs", "tempHolder", "shift", "<PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "updateLazy", "dragging", "newSlideIndexOffset", "KeyboardKeyCodesMap", "Escape", "z", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Tab", "getKeyboardEventKey", "key", "isKeySupported", "Keyboard", "_wasFocused", "trapFocus", "initialPointerPos", "_focusRoot", "_onFocusIn", "_onKeyDown", "lastActiveElement", "activeElement", "returnFocus", "focus", "keydownAction", "isForward", "keyCode", "escKey", "arrowKeys", "template", "DEFAULT_EASING", "CSSAnimation", "props", "onFinish", "_target", "_onComplete", "_finished", "_onTransitionEnd", "_helperTimeout", "_finalizeAnimation", "removeEventListener", "DEFAULT_NATURAL_FREQUENCY", "DEFAULT_DAMPING_RATIO", "SpringEaser", "_dampingRatio", "_naturalFrequency", "_dampedFrequency", "easeFrame", "deltaPosition", "deltaTime", "coeff", "naturalDumpingPow", "E", "dumpedFCos", "cos", "dumpedFSin", "sin", "SpringAnimation", "_raf", "easer", "prevTime", "animationLoop", "Animations", "activeAnimations", "_start", "isSpring", "animation", "stop", "isPanR<PERSON>ning", "some", "ScrollWheel", "_onWheel", "deltaX", "deltaY", "wheelToZoom", "deltaMode", "clientX", "clientY", "addElementHTML", "htmlData", "isCustomSVG", "svgData", "out", "join", "size", "outlineID", "inner", "UIElement", "elementHTML", "html", "isButton", "toLowerCase", "title", "aria<PERSON><PERSON><PERSON>", "ariaText", "innerHTML", "onInit", "onClick", "onclick", "appendTo", "topBar", "initArrowButton", "isNextButton", "loop", "disabled", "arrowPrev", "order", "arrowNext", "closeButton", "zoomButton", "loadingIndicator", "indicatorElement", "isVisible", "delayTimeout", "toggleIndicatorClass", "setIndicatorVisibility", "visible", "updatePreloaderVisibility", "isLoading", "preloader<PERSON>elay", "ui", "counterIndicator", "counterElement", "innerText", "indexIndicatorSep", "setZoomedIn", "isZoomedIn", "UI", "isRegistered", "uiElementsData", "items", "_lastUpdatedZoomLevel", "init", "sort", "a", "b", "uiElementData", "registerElement", "_onZoomPanUpdate", "elementData", "isClosing", "currZoomLevelDiff", "potentialZoomLevel", "imageClickAction", "getBoundsByElement", "thumbAreaRect", "getBoundingClientRect", "left", "top", "getCroppedBoundsByElement", "imageWidth", "imageHeight", "fillZoomLevel", "offsetX", "offsetY", "innerRect", "getThumbBounds", "instance", "thumbBounds", "thumbnail", "thumbSelector", "matches", "querySelector", "thumbCropped", "PhotoSwipeEvent", "details", "assign", "Eventable", "_listeners", "_filters", "addFilter", "fn", "priority", "f1", "f2", "removeFilter", "args", "apply", "off", "Placeholder", "imageSrc", "imgEl", "decoding", "alt", "src", "parentNode", "Content", "displayedImageWidth", "displayedImageHeight", "isAttached", "isDecoding", "state", "removePlaceholder", "keepPlaceholder", "isLazy", "reload", "usePlaceholder", "placeholderSrc", "msrc", "placeholder<PERSON><PERSON>", "parentElement", "prepend", "isImageContent", "loadImage", "imageElement", "updateSrcsetSizes", "srcset", "onLoaded", "onError", "setSlide", "displayError", "isError", "isInitialSizeUpdate", "image", "sizesWidth", "dataset", "largestUsedSize", "parseInt", "sizes", "String", "lazyLoad", "errorMsgEl", "errorMsg", "supportsDecode", "finally", "appendImage", "MIN_SLIDES_TO_CACHE", "lazyLoadData", "createContentFromData", "zoomLevel", "ceil", "lazyLoadSlide", "getItemData", "ContentLoader", "limit", "preload", "_cachedItems", "loadSlideByIndex", "initialIndex", "getContentByIndex", "addToCache", "removeByIndex", "indexToRemove", "item", "removedItem", "find", "PhotoSwipeBase", "numItems", "dataSource", "_getGalleryDOMElements", "gallery", "dataSourceItem", "_domElementToItemData", "galleryElement", "children", "childSelector", "linkEl", "pswpSrc", "href", "pswpSrcset", "pswpWidth", "pswpHeight", "pswpType", "thumbnailEl", "currentSrc", "getAttribute", "pswpCropped", "cropped", "MIN_OPACITY", "Opener", "isClosed", "isOpening", "_duration", "_useAnimation", "_croppedZoom", "_animateRootOpacity", "_animateBgOpacity", "_placeholder", "_opacityElement", "_cropContainer1", "_cropContainer2", "_thumbBounds", "_prepareOpen", "open", "hideAnimationDuration", "maxWidthToAnimate", "_applyStartProps", "showAnimationDuration", "showHideAnimationType", "showHideOpacity", "_initialThumbBounds", "_animateZoom", "bg", "opacity", "overflow", "_setClosedStateZoomPan", "<PERSON><PERSON><PERSON><PERSON>", "decoded", "is<PERSON><PERSON>ying", "_initiate", "setProperty", "_animateToOpenState", "_animateToClosedState", "_onAnimationComplete", "_animateTo", "containerOnePanX", "containerOnePanY", "containerTwoPanX", "containerTwoPanY", "animProps", "defaultOptions", "bgClickAction", "tapAction", "PhotoSwipe", "_prepareOptions", "_prevViewportSize", "isDestroying", "hasMouse", "_initialItemData", "keyboard", "_createMainStructure", "rootClasses", "mainClass", "scrollWheel", "isNaN", "updateSize", "pageYOffset", "_handlePageResize", "_updatePageScrollOffset", "goTo", "next", "prev", "refreshSlideContent", "slideIndex", "potentialHolderIndex", "holder", "matchMedia", "test", "userAgent", "setScrollOffset", "body"], "mappings": ";;;;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,aAAT,CAAuBC,SAAvB,EAAkCC,OAAlC,EAA2CC,UAA3C,EAAuD;AAC5D,EAAA,MAAMC,EAAE,GAAGC,QAAQ,CAACL,aAAT,CAAuBE,OAAvB,CAAX,CAAA;;AACA,EAAA,IAAID,SAAJ,EAAe;IACbG,EAAE,CAACH,SAAH,GAAeA,SAAf,CAAA;AACD,GAAA;;AACD,EAAA,IAAIE,UAAJ,EAAgB;IACdA,UAAU,CAACG,WAAX,CAAuBF,EAAvB,CAAA,CAAA;AACD,GAAA;;AACD,EAAA,OAAOA,EAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;;AACO,SAASG,cAAT,CAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;AACrCD,EAAAA,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAAV,CAAA;AACAF,EAAAA,EAAE,CAACG,CAAH,GAAOF,EAAE,CAACE,CAAV,CAAA;;AACA,EAAA,IAAIF,EAAE,CAACG,EAAH,KAAUC,SAAd,EAAyB;AACvBL,IAAAA,EAAE,CAACI,EAAH,GAAQH,EAAE,CAACG,EAAX,CAAA;AACD,GAAA;;AACD,EAAA,OAAOJ,EAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;;AACO,SAASM,UAAT,CAAoBC,CAApB,EAAuB;EAC5BA,CAAC,CAACL,CAAF,GAAMM,IAAI,CAACC,KAAL,CAAWF,CAAC,CAACL,CAAb,CAAN,CAAA;EACAK,CAAC,CAACJ,CAAF,GAAMK,IAAI,CAACC,KAAL,CAAWF,CAAC,CAACJ,CAAb,CAAN,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASO,kBAAT,CAA4BV,EAA5B,EAAgCC,EAAhC,EAAoC;AACzC,EAAA,MAAMC,CAAC,GAAGM,IAAI,CAACG,GAAL,CAASX,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAAnB,CAAV,CAAA;AACA,EAAA,MAAMC,CAAC,GAAGK,IAAI,CAACG,GAAL,CAASX,EAAE,CAACG,CAAH,GAAOF,EAAE,CAACE,CAAnB,CAAV,CAAA;EACA,OAAOK,IAAI,CAACI,IAAL,CAAWV,CAAC,GAAGA,CAAL,GAAWC,CAAC,GAAGA,CAAzB,CAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASU,WAAT,CAAqBb,EAArB,EAAyBC,EAAzB,EAA6B;AAClC,EAAA,OAAOD,EAAE,CAACE,CAAH,KAASD,EAAE,CAACC,CAAZ,IAAiBF,EAAE,CAACG,CAAH,KAASF,EAAE,CAACE,CAApC,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASW,KAAT,CAAeC,GAAf,EAAoBC,GAApB,EAAyBC,GAAzB,EAA8B;AACnC,EAAA,OAAOT,IAAI,CAACQ,GAAL,CAASR,IAAI,CAACS,GAAL,CAASF,GAAT,EAAcC,GAAd,CAAT,EAA6BC,GAA7B,CAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,iBAAT,CAA2BhB,CAA3B,EAA8BC,CAA9B,EAAiCgB,KAAjC,EAAwC;EAC7C,IAAIC,SAAS,GAAI,CAAclB,YAAAA,EAAAA,CAAE,MAAKC,CAAC,IAAI,CAAE,CAA7C,KAAA,CAAA,CAAA;;EAEA,IAAIgB,KAAK,KAAKd,SAAd,EAAyB;AACvBe,IAAAA,SAAS,IAAK,CAAA,SAAA,EAAWD,KAAM,CAAA,CAAA,EAAGA,KAAM,CAAxC,GAAA,CAAA,CAAA;AACD,GAAA;;AAED,EAAA,OAAOC,SAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,YAAT,CAAsBzB,EAAtB,EAA0BM,CAA1B,EAA6BC,CAA7B,EAAgCgB,KAAhC,EAAuC;AAC5CvB,EAAAA,EAAE,CAAC0B,KAAH,CAASC,SAAT,GAAqBL,iBAAiB,CAAChB,CAAD,EAAIC,CAAJ,EAAOgB,KAAP,CAAtC,CAAA;AACD,CAAA;AAED,MAAMK,gBAAgB,GAAG,0BAAzB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,kBAAT,CAA4B7B,EAA5B,EAAgC8B,IAAhC,EAAsCC,QAAtC,EAAgDC,IAAhD,EAAsD;AAC3D;AACA;AACA;AACAhC,EAAAA,EAAE,CAAC0B,KAAH,CAASO,UAAT,GAAsBH,IAAI,GACrB,CAAA,EAAEA,IAAK,CAAA,CAAA,EAAGC,QAAS,CAAKC,GAAAA,EAAAA,IAAI,IAAIJ,gBAAiB,CAAA,CAD5B,GAEtB,MAFJ,CAAA;AAGD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASM,cAAT,CAAwBlC,EAAxB,EAA4BmC,CAA5B,EAA+BC,CAA/B,EAAkC;AACvCpC,EAAAA,EAAE,CAAC0B,KAAH,CAASW,KAAT,GAAkB,OAAOF,CAAP,KAAa,QAAd,GAA2B,CAAA,EAAEA,CAAE,CAAA,EAAA,CAA/B,GAAqCA,CAAtD,CAAA;AACAnC,EAAAA,EAAE,CAAC0B,KAAH,CAASY,MAAT,GAAmB,OAAOF,CAAP,KAAa,QAAd,GAA2B,CAAA,EAAEA,CAAE,CAAA,EAAA,CAA/B,GAAqCA,CAAvD,CAAA;AACD,CAAA;AAED;AACA;AACA;;AACO,SAASG,qBAAT,CAA+BvC,EAA/B,EAAmC;EACxC6B,kBAAkB,CAAC7B,EAAD,CAAlB,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;;AACO,SAASwC,WAAT,CAAqBC,GAArB,EAA0B;EAC/B,IAAI,QAAA,IAAYA,GAAhB,EAAqB;IACnB,OAAOA,GAAG,CAACC,MAAJ,EAAA,CAAaC,KAAb,CAAmB,MAAM,EAAzB,CAAP,CAAA;AACD,GAAA;;EAED,IAAIF,GAAG,CAACG,QAAR,EAAkB;AAChB,IAAA,OAAOC,OAAO,CAACC,OAAR,CAAgBL,GAAhB,CAAP,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAII,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACtCN,IAAAA,GAAG,CAACO,MAAJ,GAAa,MAAMF,OAAO,CAACL,GAAD,CAA1B,CAAA;;IACAA,GAAG,CAACQ,OAAJ,GAAcF,MAAd,CAAA;AACD,GAHM,CAAP,CAAA;AAID,CAAA;AAED;;AACA;;AACO,MAAMG,UAAU,GAAG;AACxBC,EAAAA,IAAI,EAAE,MADkB;AAExBC,EAAAA,OAAO,EAAE,SAFe;AAGxBC,EAAAA,MAAM,EAAE,QAHgB;AAIxBC,EAAAA,KAAK,EAAE,OAAA;AAJiB,CAAnB,CAAA;AAQP;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,cAAT,CAAwBC,CAAxB,EAA2B;EAChC,OAAQ,QAAA,IAAYA,CAAZ,IAAiBA,CAAC,CAACC,MAAF,KAAa,CAA/B,IAAqCD,CAAC,CAACE,OAAvC,IAAkDF,CAAC,CAACG,OAApD,IAA+DH,CAAC,CAACI,MAAjE,IAA2EJ,CAAC,CAACK,QAApF,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,qBAAT,CAA+BC,MAA/B,EAAuCC,cAAvC,EAAuDC,MAAM,GAAGhE,QAAhE,EAA0E;AAC/E;EACA,IAAIiE,QAAQ,GAAG,EAAf,CAAA;;EAEA,IAAIH,MAAM,YAAYI,OAAtB,EAA+B;IAC7BD,QAAQ,GAAG,CAACH,MAAD,CAAX,CAAA;AACD,GAFD,MAEO,IAAIA,MAAM,YAAYK,QAAlB,IAA8BC,KAAK,CAACC,OAAN,CAAcP,MAAd,CAAlC,EAAyD;AAC9DG,IAAAA,QAAQ,GAAGG,KAAK,CAACE,IAAN,CAAWR,MAAX,CAAX,CAAA;AACD,GAFM,MAEA;IACL,MAAMS,QAAQ,GAAG,OAAOT,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCC,cAAvD,CAAA;;AACA,IAAA,IAAIQ,QAAJ,EAAc;MACZN,QAAQ,GAAGG,KAAK,CAACE,IAAN,CAAWN,MAAM,CAACQ,gBAAP,CAAwBD,QAAxB,CAAX,CAAX,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAON,QAAP,CAAA;AACD,CAAA;AAcD;AACA;AACA;AACA;AACA;;AACO,SAASQ,QAAT,GAAoB;AACzB,EAAA,OAAO,CAAC,EAAEC,SAAS,CAACC,MAAV,IAAoBD,SAAS,CAACC,MAAV,CAAiBC,KAAjB,CAAuB,QAAvB,CAAtB,CAAR,CAAA;AACD;;ACvOD;AACA,IAAIC,eAAe,GAAG,KAAtB,CAAA;AACA;;AACA,IAAI;AACF;AACAC,EAAAA,MAAM,CAACC,gBAAP,CAAwB,MAAxB,EAAgC,IAAhC,EAAsCC,MAAM,CAACC,cAAP,CAAsB,EAAtB,EAA0B,SAA1B,EAAqC;AACzEC,IAAAA,GAAG,EAAE,MAAM;AACTL,MAAAA,eAAe,GAAG,IAAlB,CAAA;AACD,KAAA;AAHwE,GAArC,CAAtC,CAAA,CAAA;AAKD,CAPD,CAOE,OAAOtB,CAAP,EAAU,EAAE;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,MAAM4B,SAAN,CAAgB;AACdC,EAAAA,WAAW,GAAG;AACZ;AACJ;AACA;AACA;IACI,IAAKC,CAAAA,KAAL,GAAa,EAAb,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEC,GAAG,CAACC,MAAD,EAASC,IAAT,EAAeC,QAAf,EAAyBC,OAAzB,EAAkC;IACnC,IAAKC,CAAAA,eAAL,CAAqBJ,MAArB,EAA6BC,IAA7B,EAAmCC,QAAnC,EAA6CC,OAA7C,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEE,MAAM,CAACL,MAAD,EAASC,IAAT,EAAeC,QAAf,EAAyBC,OAAzB,EAAkC;IACtC,IAAKC,CAAAA,eAAL,CAAqBJ,MAArB,EAA6BC,IAA7B,EAAmCC,QAAnC,EAA6CC,OAA7C,EAAsD,IAAtD,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEG,EAAAA,SAAS,GAAG;AACV,IAAA,IAAA,CAAKR,KAAL,CAAWS,OAAX,CAAoBC,QAAD,IAAc;MAC/B,IAAKJ,CAAAA,eAAL,CACEI,QAAQ,CAACR,MADX,EAEEQ,QAAQ,CAACP,IAFX,EAGEO,QAAQ,CAACN,QAHX,EAIEM,QAAQ,CAACL,OAJX,EAKE,IALF,EAME,IANF,CAAA,CAAA;KADF,CAAA,CAAA;;IAUA,IAAKL,CAAAA,KAAL,GAAa,EAAb,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEM,EAAAA,eAAe,CAACJ,MAAD,EAASC,IAAT,EAAeC,QAAf,EAAyBC,OAAzB,EAAkCM,MAAlC,EAA0CC,QAA1C,EAAoD;IACjE,IAAI,CAACV,MAAL,EAAa;AACX,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMW,UAAU,GAAGF,MAAM,GAAG,qBAAH,GAA2B,kBAApD,CAAA;AACA,IAAA,MAAMG,KAAK,GAAGX,IAAI,CAACY,KAAL,CAAW,GAAX,CAAd,CAAA;AACAD,IAAAA,KAAK,CAACL,OAAN,CAAeO,KAAD,IAAW;AACvB,MAAA,IAAIA,KAAJ,EAAW;AACT;AACA;QACA,IAAI,CAACJ,QAAL,EAAe;AACb,UAAA,IAAID,MAAJ,EAAY;AACV;YACA,IAAKX,CAAAA,KAAL,GAAa,IAAKA,CAAAA,KAAL,CAAWiB,MAAX,CAAmBP,QAAD,IAAc;AAC3C,cAAA,OAAOA,QAAQ,CAACP,IAAT,KAAkBa,KAAlB,IACFN,QAAQ,CAACN,QAAT,KAAsBA,QADpB,IAEFM,QAAQ,CAACR,MAAT,KAAoBA,MAFzB,CAAA;AAGD,aAJY,CAAb,CAAA;AAKD,WAPD,MAOO;AACL;YACA,IAAKF,CAAAA,KAAL,CAAWkB,IAAX,CAAgB;cACdhB,MADc;AAEdC,cAAAA,IAAI,EAAEa,KAFQ;cAGdZ,QAHc;AAIdC,cAAAA,OAAAA;aAJF,CAAA,CAAA;AAMD,WAAA;AACF,SApBQ;AAuBT;;;QACA,MAAMc,YAAY,GAAG3B,eAAe,GAAG;UAAEa,OAAO,EAAGA,OAAO,IAAI,KAAA;AAAvB,SAAH,GAAqC,KAAzE,CAAA;QAEAH,MAAM,CAACW,UAAD,CAAN,CACEG,KADF,EAEEZ,QAFF,EAGEe,YAHF,CAAA,CAAA;AAKD,OAAA;KAhCH,CAAA,CAAA;AAkCD,GAAA;;AAtGa;;ACrBhB;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,eAAT,CAAyBC,OAAzB,EAAkCC,IAAlC,EAAwC;EAC7C,IAAID,OAAO,CAACE,iBAAZ,EAA+B;IAC7B,MAAMC,eAAe,GAAGH,OAAO,CAACE,iBAAR,CAA0BF,OAA1B,EAAmCC,IAAnC,CAAxB,CAAA;;AACA,IAAA,IAAIE,eAAJ,EAAqB;AACnB,MAAA,OAAOA,eAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,OAAO;AACLxG,IAAAA,CAAC,EAAEL,QAAQ,CAAC8G,eAAT,CAAyBC,WADvB;AAGL;AACA;AACA;AACA;IACAzG,CAAC,EAAEwE,MAAM,CAACkC,WAAAA;GAPZ,CAAA;AASD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,kBAAT,CAA4BpF,IAA5B,EAAkC6E,OAAlC,EAA2CQ,YAA3C,EAAyDC,QAAzD,EAAmEC,KAAnE,EAA0E;EAC/E,IAAIC,YAAY,GAAG,CAAnB,CAAA;;EAEA,IAAIX,OAAO,CAACY,SAAZ,EAAuB;AACrBD,IAAAA,YAAY,GAAGX,OAAO,CAACY,SAAR,CAAkBJ,YAAlB,EAAgCC,QAAhC,EAA0CC,KAA1C,CAAiDvF,CAAAA,IAAjD,CAAf,CAAA;AACD,GAFD,MAEO,IAAI6E,OAAO,CAACa,OAAZ,EAAqB;AAC1BF,IAAAA,YAAY,GAAGX,OAAO,CAACa,OAAR,CAAgB1F,IAAhB,CAAf,CAAA;AACD,GAFM,MAEA;AACL,IAAA,MAAM2F,cAAc,GAAG,SAAA,GAAY3F,IAAI,CAAC,CAAD,CAAJ,CAAQ4F,WAAR,EAAZ,GAAoC5F,IAAI,CAAC6F,KAAL,CAAW,CAAX,CAA3D,CADK;;AAGL,IAAA,IAAIhB,OAAO,CAACc,cAAD,CAAX,EAA6B;AAC3B;AACAH,MAAAA,YAAY,GAAGX,OAAO,CAACc,cAAD,CAAtB,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAOG,MAAM,CAACN,YAAD,CAAN,IAAwB,CAA/B,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASO,cAAT,CAAwBlB,OAAxB,EAAiCQ,YAAjC,EAA+CC,QAA/C,EAAyDC,KAAzD,EAAgE;EACrE,OAAO;AACL/G,IAAAA,CAAC,EAAE6G,YAAY,CAAC7G,CAAb,GACC4G,kBAAkB,CAAC,MAAD,EAASP,OAAT,EAAkBQ,YAAlB,EAAgCC,QAAhC,EAA0CC,KAA1C,CADnB,GAECH,kBAAkB,CAAC,OAAD,EAAUP,OAAV,EAAmBQ,YAAnB,EAAiCC,QAAjC,EAA2CC,KAA3C,CAHjB;AAIL9G,IAAAA,CAAC,EAAE4G,YAAY,CAAC5G,CAAb,GACC2G,kBAAkB,CAAC,KAAD,EAAQP,OAAR,EAAiBQ,YAAjB,EAA+BC,QAA/B,EAAyCC,KAAzC,CADnB,GAECH,kBAAkB,CAAC,QAAD,EAAWP,OAAX,EAAoBQ,YAApB,EAAkCC,QAAlC,EAA4CC,KAA5C,CAAA;GANxB,CAAA;AAQD;;AChGD;;AACA;;AACA;;AAEA;AACA;AACA;;AACA,MAAMS,SAAN,CAAgB;AACd;AACF;AACA;EACEzC,WAAW,CAAC0C,KAAD,EAAQ;IACjB,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAKC,CAAAA,aAAL,GAAqB,CAArB,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL;AAAc;AAAqB,IAAA;AAAE3H,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA9C,CAAA;AACA,IAAA,IAAA,CAAKc,GAAL;AAAW;AAAqB,IAAA;AAAEf,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA3C,CAAA;AACA,IAAA,IAAA,CAAKa,GAAL;AAAW;AAAqB,IAAA;AAAEd,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA3C,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACE2H,MAAM,CAACF,aAAD,EAAgB;IACpB,IAAKA,CAAAA,aAAL,GAAqBA,aAArB,CAAA;;AAEA,IAAA,IAAI,CAAC,IAAA,CAAKD,KAAL,CAAW1F,KAAhB,EAAuB;AACrB,MAAA,IAAA,CAAK8F,KAAL,EAAA,CAAA;AACD,KAFD,MAEO;MACL,IAAKC,CAAAA,WAAL,CAAiB,GAAjB,CAAA,CAAA;;MACA,IAAKA,CAAAA,WAAL,CAAiB,GAAjB,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKL,KAAL,CAAWnB,IAAX,CAAgByB,QAAhB,CAAyB,YAAzB,EAAuC;AAAEN,QAAAA,KAAK,EAAE,IAAKA,CAAAA,KAAAA;OAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEK,WAAW,CAACE,IAAD,EAAO;IAChB,MAAM;AAAE1B,MAAAA,IAAAA;AAAF,KAAA,GAAW,KAAKmB,KAAtB,CAAA;AACA,IAAA,MAAMQ,MAAM,GAAG,IAAKR,CAAAA,KAAL,CAAWO,IAAI,KAAK,GAAT,GAAe,OAAf,GAAyB,QAApC,CAAA,GAAgD,KAAKN,aAApE,CAAA;IACA,MAAMQ,WAAW,GAAGF,IAAI,KAAK,GAAT,GAAe,MAAf,GAAwB,KAA5C,CAAA;IACA,MAAMd,OAAO,GAAGN,kBAAkB,CAChCsB,WADgC,EAEhC5B,IAAI,CAACD,OAF2B,EAGhCC,IAAI,CAACO,YAH2B,EAIhC,IAAKY,CAAAA,KAAL,CAAWU,IAJqB,EAKhC,IAAKV,CAAAA,KAAL,CAAWV,KALqB,CAAlC,CAAA;IAQA,MAAMqB,WAAW,GAAG,IAAA,CAAKX,KAAL,CAAWW,WAAX,CAAuBJ,IAAvB,CAApB,CAZgB;AAehB;;AACA,IAAA,IAAA,CAAKL,MAAL,CAAYK,IAAZ,CAAoB1H,GAAAA,IAAI,CAACC,KAAL,CAAW,CAAC6H,WAAW,GAAGH,MAAf,IAAyB,CAApC,CAAyCf,GAAAA,OAA7D,CAhBgB;;IAmBhB,IAAKnG,CAAAA,GAAL,CAASiH,IAAT,CAAkBC,GAAAA,MAAM,GAAGG,WAAV,GACb9H,IAAI,CAACC,KAAL,CAAW6H,WAAW,GAAGH,MAAzB,CAAmCf,GAAAA,OADtB,GAEb,IAAA,CAAKS,MAAL,CAAYK,IAAZ,CAFJ,CAnBgB;;AAwBhB,IAAA,IAAA,CAAKlH,GAAL,CAASkH,IAAT,CAAA,GAAkBC,MAAM,GAAGG,WAAV,GACblB,OADa,GAEb,IAAA,CAAKS,MAAL,CAAYK,IAAZ,CAFJ,CAAA;AAGD,GA7Da;;;AAgEdH,EAAAA,KAAK,GAAG;AACN,IAAA,IAAA,CAAKF,MAAL,CAAY3H,CAAZ,GAAgB,CAAhB,CAAA;AACA,IAAA,IAAA,CAAK2H,MAAL,CAAY1H,CAAZ,GAAgB,CAAhB,CAAA;AACA,IAAA,IAAA,CAAKc,GAAL,CAASf,CAAT,GAAa,CAAb,CAAA;AACA,IAAA,IAAA,CAAKe,GAAL,CAASd,CAAT,GAAa,CAAb,CAAA;AACA,IAAA,IAAA,CAAKa,GAAL,CAASd,CAAT,GAAa,CAAb,CAAA;AACA,IAAA,IAAA,CAAKc,GAAL,CAASb,CAAT,GAAa,CAAb,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEoI,EAAAA,UAAU,CAACL,IAAD,EAAOM,SAAP,EAAkB;AAAE;AAC5B,IAAA,OAAO1H,KAAK,CAAC0H,SAAD,EAAY,KAAKvH,GAAL,CAASiH,IAAT,CAAZ,EAA4B,IAAKlH,CAAAA,GAAL,CAASkH,IAAT,CAA5B,CAAZ,CAAA;AACD,GAAA;;AAlFa;;ACVhB,MAAMO,eAAe,GAAG,IAAxB,CAAA;AAEA;;AACA;;AACA;;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AACA,MAAMC,SAAN,CAAgB;AACd;AACF;AACA;AACA;AACA;AACA;EACEzD,WAAW,CAACsB,OAAD,EAAUS,QAAV,EAAoBC,KAApB,EAA2BT,IAA3B,EAAiC;IAC1C,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKD,CAAAA,OAAL,GAAeA,OAAf,CAAA;IACA,IAAKS,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;IACA,IAAKC,CAAAA,KAAL,GAAaA,KAAb,CAAA;AACA;;IACA,IAAKqB,CAAAA,WAAL,GAAmB,IAAnB,CAAA;AACA;;IACA,IAAKK,CAAAA,WAAL,GAAmB,IAAnB,CAAA;IACA,IAAKC,CAAAA,GAAL,GAAW,CAAX,CAAA;IACA,IAAKC,CAAAA,IAAL,GAAY,CAAZ,CAAA;IACA,IAAKC,CAAAA,KAAL,GAAa,CAAb,CAAA;IACA,IAAKC,CAAAA,OAAL,GAAe,CAAf,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,CAAjB,CAAA;IACA,IAAK/H,CAAAA,GAAL,GAAW,CAAX,CAAA;IACA,IAAKD,CAAAA,GAAL,GAAW,CAAX,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE8G,EAAAA,MAAM,CAACmB,QAAD,EAAWC,SAAX,EAAsBZ,WAAtB,EAAmC;AACvC;AACA,IAAA,MAAMK,WAAW,GAAG;AAAEzI,MAAAA,CAAC,EAAE+I,QAAL;AAAe9I,MAAAA,CAAC,EAAE+I,SAAAA;KAAtC,CAAA;IACA,IAAKP,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;IACA,IAAKL,CAAAA,WAAL,GAAmBA,WAAnB,CAAA;IAEA,MAAMa,MAAM,GAAGb,WAAW,CAACpI,CAAZ,GAAgByI,WAAW,CAACzI,CAA3C,CAAA;IACA,MAAMkJ,MAAM,GAAGd,WAAW,CAACnI,CAAZ,GAAgBwI,WAAW,CAACxI,CAA3C,CAAA;AAEA,IAAA,IAAA,CAAKyI,GAAL,GAAWpI,IAAI,CAACQ,GAAL,CAAS,CAAT,EAAYmI,MAAM,GAAGC,MAAT,GAAkBD,MAAlB,GAA2BC,MAAvC,CAAX,CAAA;AACA,IAAA,IAAA,CAAKP,IAAL,GAAYrI,IAAI,CAACQ,GAAL,CAAS,CAAT,EAAYmI,MAAM,GAAGC,MAAT,GAAkBD,MAAlB,GAA2BC,MAAvC,CAAZ,CAVuC;AAavC;;IACA,IAAKN,CAAAA,KAAL,GAAatI,IAAI,CAACQ,GAAL,CAAS,CAAT,EAAYoI,MAAZ,CAAb,CAAA;AAEA,IAAA,IAAA,CAAKL,OAAL,GAAe,IAAKM,CAAAA,WAAL,EAAf,CAAA;AACA,IAAA,IAAA,CAAKL,SAAL,GAAiB,IAAKM,CAAAA,aAAL,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKrI,GAAL,GAAWT,IAAI,CAACS,GAAL,CACT,IAAA,CAAK8H,OADI,EAET,KAAKC,SAFI,EAGT,IAAKO,CAAAA,OAAL,EAHS,CAAX,CAAA;AAMA,IAAA,IAAA,CAAKvI,GAAL,GAAWR,IAAI,CAACQ,GAAL,CACT,IAAA,CAAK4H,GADI,EAET,IAAKG,CAAAA,OAFI,EAGT,IAAA,CAAKC,SAHI,CAAX,CAAA;;IAMA,IAAI,IAAA,CAAKxC,IAAT,EAAe;AACb,MAAA,IAAA,CAAKA,IAAL,CAAUyB,QAAV,CAAmB,kBAAnB,EAAuC;AAAEuB,QAAAA,UAAU,EAAE,IAAd;AAAoBC,QAAAA,SAAS,EAAE,IAAKzC,CAAAA,QAAAA;OAA3E,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE0C,qBAAqB,CAACC,YAAD,EAAe;AAClC,IAAA,MAAMC,UAAU;AAAG;AACjBD,IAAAA,YAAY,GAAG,WADjB,CAAA;AAGA,IAAA,MAAME,WAAW,GAAG,IAAA,CAAKtD,OAAL,CAAaqD,UAAb,CAApB,CAAA;;IAEA,IAAI,CAACC,WAAL,EAAkB;AAChB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOA,WAAP,KAAuB,UAA3B,EAAuC;MACrC,OAAOA,WAAW,CAAC,IAAD,CAAlB,CAAA;AACD,KAAA;;IAED,IAAIA,WAAW,KAAK,MAApB,EAA4B;AAC1B,MAAA,OAAO,KAAKhB,IAAZ,CAAA;AACD,KAAA;;IAED,IAAIgB,WAAW,KAAK,KAApB,EAA2B;AACzB,MAAA,OAAO,KAAKjB,GAAZ,CAAA;AACD,KAAA;;IAED,OAAOpB,MAAM,CAACqC,WAAD,CAAb,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEP,EAAAA,aAAa,GAAG;AACd,IAAA,IAAI1B,aAAa,GAAG,IAAA,CAAK8B,qBAAL,CAA2B,WAA3B,CAApB,CAAA;;AAEA,IAAA,IAAI9B,aAAJ,EAAmB;AACjB,MAAA,OAAOA,aAAP,CAAA;AACD,KALa;;;IAQdA,aAAa,GAAGpH,IAAI,CAACQ,GAAL,CAAS,CAAT,EAAY,IAAK4H,CAAAA,GAAL,GAAW,CAAvB,CAAhB,CAAA;;IAEA,IAAI,IAAA,CAAKD,WAAL,IAAoBf,aAAa,GAAG,IAAKe,CAAAA,WAAL,CAAiBzI,CAAjC,GAAqCuI,eAA7D,EAA8E;AAC5Eb,MAAAA,aAAa,GAAGa,eAAe,GAAG,IAAKE,CAAAA,WAAL,CAAiBzI,CAAnD,CAAA;AACD,KAAA;;AAED,IAAA,OAAO0H,aAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEyB,EAAAA,WAAW,GAAG;AACZ,IAAA,OAAO,KAAKK,qBAAL,CAA2B,SAA3B,CAAA,IAAyC,KAAKd,GAArD,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEW,EAAAA,OAAO,GAAG;AACR;AACA;AACA,IAAA,OAAO,IAAKG,CAAAA,qBAAL,CAA2B,KAA3B,KAAqClJ,IAAI,CAACS,GAAL,CAAS,CAAT,EAAY,IAAA,CAAK2H,GAAL,GAAW,CAAvB,CAA5C,CAAA;AACD,GAAA;;AArJa;;ACbhB;AAiCA;AACA;AACA;;AACA,MAAMkB,KAAN,CAAY;AACV;AACF;AACA;AACA;AACA;AACE7E,EAAAA,WAAW,CAACoD,IAAD,EAAOpB,KAAP,EAAcT,IAAd,EAAoB;IAC7B,IAAK6B,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKpB,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAKT,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACA,IAAA,IAAA,CAAKuD,QAAL,GAAiB9C,KAAK,KAAKT,IAAI,CAACwD,SAAhC,CAAA;IACA,IAAKC,CAAAA,iBAAL,GAAyB,CAAzB,CAAA;AACA;;AACA,IAAA,IAAA,CAAK3B,WAAL,GAAmB;AAAEpI,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA9B,CAAA;AACA;;AACA,IAAA,IAAA,CAAK+J,GAAL,GAAW;AAAEhK,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAtB,CAAA;IAEA,IAAKgK,CAAAA,YAAL,GAAqB,IAAA,CAAKJ,QAAL,IAAiB,CAACvD,IAAI,CAAC4D,MAAL,CAAYC,MAAnD,CAAA;AAEA,IAAA,IAAA,CAAKb,UAAL,GAAkB,IAAId,SAAJ,CAAclC,IAAI,CAACD,OAAnB,EAA4B8B,IAA5B,EAAkCpB,KAAlC,EAAyCT,IAAzC,CAAlB,CAAA;AAEA,IAAA,IAAA,CAAKA,IAAL,CAAUyB,QAAV,CAAmB,aAAnB,EAAkC;AAChCN,MAAAA,KAAK,EAAE,IADyB;MAEhCU,IAAI,EAAE,KAAKA,IAFqB;AAGhCpB,MAAAA,KAAAA;KAHF,CAAA,CAAA;IAMA,IAAKqD,CAAAA,OAAL,GAAe,IAAA,CAAK9D,IAAL,CAAU+D,aAAV,CAAwBC,iBAAxB,CAA0C,IAA1C,CAAf,CAAA;AACA,IAAA,IAAA,CAAKC,SAAL,GAAiBjL,aAAa,CAAC,iBAAD,EAAoB,KAApB,CAA9B,CAAA;AACA;;IACA,IAAKkL,CAAAA,aAAL,GAAqB,IAArB,CAAA;IAEA,IAAK9C,CAAAA,aAAL,GAAqB,CAArB,CAAA;AACA;;AACA,IAAA,IAAA,CAAK3F,KAAL,GAAa,IAAKqI,CAAAA,OAAL,CAAarI,KAA1B,CAAA;AACA;;AACA,IAAA,IAAA,CAAKC,MAAL,GAAc,IAAKoI,CAAAA,OAAL,CAAapI,MAA3B,CAAA;IACA,IAAKyI,CAAAA,aAAL,GAAqB,KAArB,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAc,IAAIlD,SAAJ,CAAc,IAAd,CAAd,CAAA;IAEA,IAAKmD,CAAAA,kBAAL,GAA0B,CAAC,CAA3B,CAAA;IACA,IAAKC,CAAAA,mBAAL,GAA2B,CAAC,CAA5B,CAAA;AAEA,IAAA,IAAA,CAAKtE,IAAL,CAAUyB,QAAV,CAAmB,WAAnB,EAAgC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAAzC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEoD,WAAW,CAAChB,QAAD,EAAW;AACpB,IAAA,IAAIA,QAAQ,IAAI,CAAC,IAAA,CAAKA,QAAtB,EAAgC;AAC9B;AACA,MAAA,IAAA,CAAKiB,QAAL,EAAA,CAAA;AACD,KAHD,MAGO,IAAI,CAACjB,QAAD,IAAa,IAAA,CAAKA,QAAtB,EAAgC;AACrC;AACA,MAAA,IAAA,CAAKkB,UAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEC,MAAM,CAACR,aAAD,EAAgB;IACpB,IAAKA,CAAAA,aAAL,GAAqBA,aAArB,CAAA;IAEA,IAAKD,CAAAA,SAAL,CAAenJ,KAAf,CAAqB6J,eAArB,GAAuC,KAAvC,CAHoB;;IAMpB,IAAI,CAAC,IAAK9C,CAAAA,IAAV,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK+C,aAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKC,iBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKb,aAAL,CAAmB5K,WAAnB,CAA+B,KAAK2K,SAApC,CAAA,CAAA;AAEA,IAAA,IAAA,CAAKe,mBAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKhF,IAAL,CAAUyB,QAAV,CAAmB,cAAnB,EAAmC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA5C,CAAA,CAAA;AAEA,IAAA,IAAA,CAAK8D,mBAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKjF,IAAL,CAAUyB,QAAV,CAAmB,iBAAnB,EAAsC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA/C,CAAA,CAAA;;IAEA,IAAI,IAAA,CAAKoC,QAAT,EAAmB;AACjB,MAAA,IAAA,CAAKiB,QAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDK,EAAAA,IAAI,GAAG;AACL,IAAA,IAAA,CAAKf,OAAL,CAAae,IAAb,CAAkB,KAAlB,CAAA,CAAA;AACA,IAAA,IAAA,CAAK7E,IAAL,CAAUyB,QAAV,CAAmB,WAAnB,EAAgC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAAzC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE4D,EAAAA,WAAW,GAAG;IACZ,MAAM;AAAE/E,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;AACA,IAAA,MAAMkF,iBAAiB,GAAG,IAA1B,CAFY;AAIZ;;IACA,IAAI,IAAA,CAAKf,aAAL,IACG,CAACnE,IAAI,CAAC4D,MAAL,CAAYC,MADhB,IAEG7D,IAAI,CAACmF,UAAL,CAAgBC,SAAhB,EAFH,IAGI,CAAC,KAAK7B,QAAN,IAAkB,CAAC2B,iBAH3B,EAG+C;AAC7C,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKlF,IAAL,CAAUyB,QAAV,CAAmB,aAAnB,EAAkC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA3C,CAAA,CAAmDkE,gBAAvD,EAAyE;AACvE,MAAA,OAAA;AACD,KAAA;;IAED,IAAKlB,CAAAA,aAAL,GAAqB,IAArB,CAAA;IAEA,IAAKL,CAAAA,OAAL,CAAaY,MAAb,EAAA,CAAA;AAEA,IAAA,IAAA,CAAK1E,IAAL,CAAUyB,QAAV,CAAmB,oBAAnB,EAAyC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAAlD,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEqD,EAAAA,QAAQ,GAAG;IACT,IAAKjB,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACA,IAAA,IAAA,CAAKwB,WAAL,EAAA,CAAA;IACA,IAAKjB,CAAAA,OAAL,CAAaU,QAAb,EAAA,CAAA;AACA,IAAA,IAAA,CAAKxE,IAAL,CAAUyB,QAAV,CAAmB,eAAnB,EAAoC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA7C,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEsD,EAAAA,UAAU,GAAG;IACX,IAAKlB,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKO,CAAAA,OAAL,CAAaW,UAAb,EAAA,CAAA;;AAEA,IAAA,IAAI,KAAKrD,aAAL,KAAuB,KAAK4B,UAAL,CAAgBT,OAA3C,EAAoD;AAClD;AACA,MAAA,IAAA,CAAKqC,aAAL,EAAA,CAAA;AACD,KAPU;;;IAUX,IAAKnB,CAAAA,iBAAL,GAAyB,CAAzB,CAAA;AACA,IAAA,IAAA,CAAKuB,mBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKC,mBAAL,EAAA,CAAA;AACA,IAAA,IAAA,CAAKH,iBAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAK9E,IAAL,CAAUyB,QAAV,CAAmB,iBAAnB,EAAsC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA/C,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEmE,EAAAA,OAAO,GAAG;AACR,IAAA,IAAA,CAAKxB,OAAL,CAAayB,QAAb,GAAwB,KAAxB,CAAA;IACA,IAAKzB,CAAAA,OAAL,CAAa7E,MAAb,EAAA,CAAA;IACA,IAAKgF,CAAAA,SAAL,CAAehF,MAAf,EAAA,CAAA;AACA,IAAA,IAAA,CAAKe,IAAL,CAAUyB,QAAV,CAAmB,cAAnB,EAAmC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA5C,CAAA,CAAA;AACD,GAAA;;AAEDqE,EAAAA,MAAM,GAAG;IACP,IAAI,IAAA,CAAKpE,aAAL,KAAuB,IAAK4B,CAAAA,UAAL,CAAgBT,OAAvC,IAAkD,CAAC,IAAKgB,CAAAA,QAA5D,EAAsE;AACpE;AACA;AAEA;AACA,MAAA,IAAA,CAAKqB,aAAL,EAAA,CAAA;MACA,IAAKnB,CAAAA,iBAAL,GAAyB,CAAzB,CAAA;AACA,MAAA,IAAA,CAAKuB,mBAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAKC,mBAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAKH,iBAAL,EAAA,CAAA;AACD,KAVD,MAUO;AACL;AACA,MAAA,IAAA,CAAKF,aAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAKR,MAAL,CAAY9C,MAAZ,CAAmB,KAAKF,aAAxB,CAAA,CAAA;MACA,IAAKqE,CAAAA,KAAL,CAAW,IAAA,CAAK/B,GAAL,CAAShK,CAApB,EAAuB,IAAA,CAAKgK,GAAL,CAAS/J,CAAhC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAGD;AACF;AACA;AACA;AACA;AACA;;;EACEmL,iBAAiB,CAACY,KAAD,EAAQ;AACvB;AACA;IACA,MAAMC,eAAe,GAAG,IAAKlC,CAAAA,iBAAL,IAA0B,IAAKT,CAAAA,UAAL,CAAgBT,OAAlE,CAAA;;IAEA,IAAI,CAACoD,eAAL,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMlK,KAAK,GAAGzB,IAAI,CAACC,KAAL,CAAW,IAAKwB,CAAAA,KAAL,GAAakK,eAAxB,KAA4C,IAAK3F,CAAAA,IAAL,CAAUO,YAAV,CAAuB7G,CAAjF,CAAA;AACA,IAAA,MAAMgC,MAAM,GAAG1B,IAAI,CAACC,KAAL,CAAW,IAAKyB,CAAAA,MAAL,GAAciK,eAAzB,KAA6C,IAAK3F,CAAAA,IAAL,CAAUO,YAAV,CAAuB5G,CAAnF,CAAA;;IAEA,IAAI,CAAC,IAAKiM,CAAAA,WAAL,CAAiBnK,KAAjB,EAAwBC,MAAxB,CAAD,IAAoC,CAACgK,KAAzC,EAAgD;AAC9C,MAAA,OAAA;AACD,KAAA;;AACD,IAAA,IAAA,CAAK5B,OAAL,CAAa+B,gBAAb,CAA8BpK,KAA9B,EAAqCC,MAArC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEkK,EAAAA,WAAW,CAACnK,KAAD,EAAQC,MAAR,EAAgB;IACzB,IAAID,KAAK,KAAK,IAAK4I,CAAAA,kBAAf,IACG3I,MAAM,KAAK,IAAK4I,CAAAA,mBADvB,EAC4C;MAC1C,IAAKD,CAAAA,kBAAL,GAA0B5I,KAA1B,CAAA;MACA,IAAK6I,CAAAA,mBAAL,GAA2B5I,MAA3B,CAAA;AACA,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;AAED;;;AACAoK,EAAAA,qBAAqB,GAAG;AAAA,IAAA,IAAA,qBAAA,CAAA;;AACtB,IAAA,OAAA,CAAA,qBAAA,GAAO,KAAKhC,OAAL,CAAaiC,WAApB,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAO,sBAA0BC,OAAjC,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEC,MAAM,CAACC,aAAD,EAAgBC,WAAhB,EAA6BC,kBAA7B,EAAiDC,YAAjD,EAA+D;IACnE,MAAM;AAAErG,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;;IACA,IAAI,CAAC,IAAKsG,CAAAA,UAAL,EAAD,IACGtG,IAAI,CAACmF,UAAL,CAAgBC,SAAhB,EADP,EACoC;AAClC,MAAA,OAAA;AACD,KAAA;;AAEDpF,IAAAA,IAAI,CAACyB,QAAL,CAAc,cAAd,EAA8B;MAC5ByE,aAD4B;MACbC,WADa;AACAC,MAAAA,kBAAAA;AADA,KAA9B,EAPmE;;AAYnEpG,IAAAA,IAAI,CAACuG,UAAL,CAAgBC,UAAhB,GAZmE;AAenE;AACA;;IAEA,MAAMC,aAAa,GAAG,IAAA,CAAKrF,aAA3B,CAAA;;IAEA,IAAI,CAACiF,YAAL,EAAmB;AACjBH,MAAAA,aAAa,GAAG5L,KAAK,CAAC4L,aAAD,EAAgB,IAAKlD,CAAAA,UAAL,CAAgBxI,GAAhC,EAAqC,IAAA,CAAKwI,UAAL,CAAgBvI,GAArD,CAArB,CAAA;AACD,KAtBkE;AAyBnE;AACA;;;IAEA,IAAKiM,CAAAA,YAAL,CAAkBR,aAAlB,CAAA,CAAA;AACA,IAAA,IAAA,CAAKxC,GAAL,CAAShK,CAAT,GAAa,IAAKiN,CAAAA,wBAAL,CAA8B,GAA9B,EAAmCR,WAAnC,EAAgDM,aAAhD,CAAb,CAAA;AACA,IAAA,IAAA,CAAK/C,GAAL,CAAS/J,CAAT,GAAa,IAAKgN,CAAAA,wBAAL,CAA8B,GAA9B,EAAmCR,WAAnC,EAAgDM,aAAhD,CAAb,CAAA;IACA3M,UAAU,CAAC,IAAK4J,CAAAA,GAAN,CAAV,CAAA;;IAEA,MAAMkD,gBAAgB,GAAG,MAAM;MAC7B,IAAKC,CAAAA,cAAL,CAAoBX,aAApB,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKjB,mBAAL,EAAA,CAAA;KAFF,CAAA;;IAKA,IAAI,CAACmB,kBAAL,EAAyB;MACvBQ,gBAAgB,EAAA,CAAA;AACjB,KAFD,MAEO;AACL5G,MAAAA,IAAI,CAACuG,UAAL,CAAgBO,eAAhB,CAAgC;AAC9BC,QAAAA,KAAK,EAAE,IADuB;AAE9BC,QAAAA,IAAI,EAAE,QAFwB;QAG9BpI,MAAM,EAAE,KAAKqF,SAHiB;QAI9BlJ,SAAS,EAAE,IAAKkM,CAAAA,mBAAL,EAJmB;AAK9BC,QAAAA,UAAU,EAAEN,gBALkB;AAM9BzL,QAAAA,QAAQ,EAAEiL,kBANoB;AAO9Be,QAAAA,MAAM,EAAEnH,IAAI,CAACD,OAAL,CAAaoH,MAAAA;OAPvB,CAAA,CAAA;AASD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;EACEC,UAAU,CAACjB,WAAD,EAAc;IACtB,IAAKF,CAAAA,MAAL,CACE,IAAA,CAAK7E,aAAL,KAAuB,IAAK4B,CAAAA,UAAL,CAAgBT,OAAvC,GACI,IAAA,CAAKS,UAAL,CAAgBR,SADpB,GACgC,IAAKQ,CAAAA,UAAL,CAAgBT,OAFlD,EAGE4D,WAHF,EAIE,IAAA,CAAKnG,IAAL,CAAUD,OAAV,CAAkBsH,qBAJpB,CAAA,CAAA;AAMD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEX,YAAY,CAACtF,aAAD,EAAgB;IAC1B,IAAKA,CAAAA,aAAL,GAAqBA,aAArB,CAAA;AACA,IAAA,IAAA,CAAKgD,MAAL,CAAY9C,MAAZ,CAAmB,KAAKF,aAAxB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEuF,EAAAA,wBAAwB,CAACjF,IAAD,EAAO4F,KAAP,EAAcb,aAAd,EAA6B;AACnD,IAAA,MAAMc,gBAAgB,GAAG,IAAKnD,CAAAA,MAAL,CAAY3J,GAAZ,CAAgBiH,IAAhB,CAAA,GAAwB,KAAK0C,MAAL,CAAY5J,GAAZ,CAAgBkH,IAAhB,CAAjD,CAAA;;IACA,IAAI6F,gBAAgB,KAAK,CAAzB,EAA4B;AAC1B,MAAA,OAAO,KAAKnD,MAAL,CAAY/C,MAAZ,CAAmBK,IAAnB,CAAP,CAAA;AACD,KAAA;;IAED,IAAI,CAAC4F,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,IAAA,CAAKtH,IAAL,CAAUwH,sBAAV,EAAR,CAAA;AACD,KAAA;;IAED,IAAI,CAACf,aAAL,EAAoB;AAClBA,MAAAA,aAAa,GAAG,IAAA,CAAKzD,UAAL,CAAgBT,OAAhC,CAAA;AACD,KAAA;;AAED,IAAA,MAAMkF,UAAU,GAAG,IAAKrG,CAAAA,aAAL,GAAqBqF,aAAxC,CAAA;IACA,OAAO,IAAA,CAAKrC,MAAL,CAAYrC,UAAZ,CACLL,IADK,EAEL,CAAC,IAAKgC,CAAAA,GAAL,CAAShC,IAAT,IAAiB4F,KAAK,CAAC5F,IAAD,CAAvB,IAAiC+F,UAAjC,GAA8CH,KAAK,CAAC5F,IAAD,CAF9C,CAAP,CAAA;AAID,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE+D,EAAAA,KAAK,CAACiC,IAAD,EAAOC,IAAP,EAAa;AAChB,IAAA,IAAA,CAAKjE,GAAL,CAAShK,CAAT,GAAa,IAAK0K,CAAAA,MAAL,CAAYrC,UAAZ,CAAuB,GAAvB,EAA4B2F,IAA5B,CAAb,CAAA;AACA,IAAA,IAAA,CAAKhE,GAAL,CAAS/J,CAAT,GAAa,IAAKyK,CAAAA,MAAL,CAAYrC,UAAZ,CAAuB,GAAvB,EAA4B4F,IAA5B,CAAb,CAAA;AACA,IAAA,IAAA,CAAK1C,mBAAL,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACE2C,EAAAA,UAAU,GAAG;AACX,IAAA,OAAOC,OAAO,CAAC,IAAKpM,CAAAA,KAAN,CAAP,IAAwB,IAAK2F,CAAAA,aAAL,GAAqB,IAAA,CAAK4B,UAAL,CAAgBZ,GAApE,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEkE,EAAAA,UAAU,GAAG;IACX,OAAOuB,OAAO,CAAC,IAAA,CAAKpM,KAAN,CAAP,IAAuB,IAAKqI,CAAAA,OAAL,CAAawC,UAAb,EAA9B,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACErB,EAAAA,mBAAmB,GAAG;AACpB,IAAA,IAAA,CAAK6C,mBAAL,CAAyB,IAAKpE,CAAAA,GAAL,CAAShK,CAAlC,EAAqC,IAAA,CAAKgK,GAAL,CAAS/J,CAA9C,EAAiD,KAAKyH,aAAtD,CAAA,CAAA;;AACA,IAAA,IAAI,IAAS,KAAA,IAAA,CAAKpB,IAAL,CAAU+H,SAAvB,EAAkC;AAChC,MAAA,IAAA,CAAK/H,IAAL,CAAUyB,QAAV,CAAmB,eAAnB,EAAoC;AAAEN,QAAAA,KAAK,EAAE,IAAA;OAA7C,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAED6D,EAAAA,mBAAmB,GAAG;AACpB,IAAA,IAAA,CAAK5D,aAAL,GAAqB,IAAA,CAAK4B,UAAL,CAAgBT,OAArC,CADoB;;AAIpB,IAAA,IAAA,CAAK6B,MAAL,CAAY9C,MAAZ,CAAmB,KAAKF,aAAxB,CAAA,CAAA;IACA7H,cAAc,CAAC,KAAKmK,GAAN,EAAW,KAAKU,MAAL,CAAY/C,MAAvB,CAAd,CAAA;AACA,IAAA,IAAA,CAAKrB,IAAL,CAAUyB,QAAV,CAAmB,gBAAnB,EAAqC;AAAEN,MAAAA,KAAK,EAAE,IAAA;KAA9C,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE2G,EAAAA,mBAAmB,CAACpO,CAAD,EAAIC,CAAJ,EAAOqO,IAAP,EAAa;AAC9BA,IAAAA,IAAI,IAAI,IAAKvE,CAAAA,iBAAL,IAA0B,IAAKT,CAAAA,UAAL,CAAgBT,OAAlD,CAAA;IACA1H,YAAY,CAAC,KAAKoJ,SAAN,EAAiBvK,CAAjB,EAAoBC,CAApB,EAAuBqO,IAAvB,CAAZ,CAAA;AACD,GAAA;;AAEDpD,EAAAA,aAAa,GAAG;IACd,MAAM;AAAE5E,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IAEAzG,cAAc,CACZ,KAAKuI,WADO,EAEZb,cAAc,CAACjB,IAAI,CAACD,OAAN,EAAeC,IAAI,CAACO,YAApB,EAAkC,IAAKsB,CAAAA,IAAvC,EAA6C,IAAKpB,CAAAA,KAAlD,CAFF,CAAd,CAAA;IAKA,IAAKuC,CAAAA,UAAL,CAAgB1B,MAAhB,CAAuB,IAAA,CAAK7F,KAA5B,EAAmC,IAAKC,CAAAA,MAAxC,EAAgD,IAAA,CAAKoG,WAArD,CAAA,CAAA;AAEA9B,IAAAA,IAAI,CAACyB,QAAL,CAAc,eAAd,EAA+B;AAC7BN,MAAAA,KAAK,EAAE,IAAA;KADT,CAAA,CAAA;AAGD,GAAA;AAED;;;AACA8F,EAAAA,mBAAmB,GAAG;AACpB,IAAA,MAAMtM,KAAK,GAAG,IAAKyG,CAAAA,aAAL,IAAsB,IAAA,CAAKqC,iBAAL,IAA0B,IAAKT,CAAAA,UAAL,CAAgBT,OAAhE,CAAd,CAAA;AACA,IAAA,OAAO7H,iBAAiB,CAAC,IAAKgJ,CAAAA,GAAL,CAAShK,CAAV,EAAa,IAAA,CAAKgK,GAAL,CAAS/J,CAAtB,EAAyBgB,KAAzB,CAAxB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEkM,cAAc,CAACoB,aAAD,EAAgB;AAC5B,IAAA,IAAIA,aAAa,KAAK,IAAKxE,CAAAA,iBAA3B,EAA8C;AAC5C,MAAA,OAAA;AACD,KAAA;;IAED,IAAKA,CAAAA,iBAAL,GAAyBwE,aAAzB,CAAA;AACA,IAAA,IAAA,CAAKnD,iBAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAK9E,IAAL,CAAUyB,QAAV,CAAmB,mBAAnB,CAAA,CAAA;AACD,GAAA;;AAndS;;AChCZ;;AACA;;AAEA,MAAMyG,gBAAgB,GAAG,IAAzB,CAAA;AACA,MAAMC,sBAAsB,GAAG,GAA/B;;AAGA,MAAMC,kBAAkB,GAAG,GAA3B;AAGA;;AACA,MAAMC,oBAAoB,GAAG,GAA7B,CAAA;AAEA;AACA;AACA;AACA;AACA;;AACA,SAASC,OAAT,CAAiBC,eAAjB,EAAkCC,gBAAlC,EAAoD;AAClD,EAAA,OAAOD,eAAe,GAAGC,gBAAlB,IAAsC,CAAA,GAAIA,gBAA1C,CAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AAChB;AACF;AACA;EACEhK,WAAW,CAACiK,QAAD,EAAW;IACpB,IAAKA,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;AACA,IAAA,IAAA,CAAK1I,IAAL,GAAY0I,QAAQ,CAAC1I,IAArB,CAAA;AACA;;AACA,IAAA,IAAA,CAAK2I,QAAL,GAAgB;AAAEjP,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA3B,CAAA;AACD,GAAA;;AAEDiP,EAAAA,KAAK,GAAG;AACN,IAAA,IAAI,IAAK5I,CAAAA,IAAL,CAAU+H,SAAd,EAAyB;MACvBxO,cAAc,CAAC,IAAKoP,CAAAA,QAAN,EAAgB,IAAA,CAAK3I,IAAL,CAAU+H,SAAV,CAAoBrE,GAApC,CAAd,CAAA;AACD,KAAA;;AACD,IAAA,IAAA,CAAK1D,IAAL,CAAUuG,UAAV,CAAqBsC,OAArB,EAAA,CAAA;AACD,GAAA;;AAEDC,EAAAA,MAAM,GAAG;IACP,MAAM;MAAEtP,EAAF;MAAMuP,MAAN;AAAcC,MAAAA,QAAAA;AAAd,KAAA,GAA2B,KAAKN,QAAtC,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK/H,IAA3B,CAAA;;AAEA,IAAA,IAAIgJ,QAAQ,KAAK,GAAb,IACG,IAAKhJ,CAAAA,IAAL,CAAUD,OAAV,CAAkBkJ,mBADrB,IAEIlB,SAAS,IAAIA,SAAS,CAAC3G,aAAV,IAA2B2G,SAAS,CAAC/E,UAAV,CAAqBZ,GAFjE,IAGG,CAAC,IAAKsG,CAAAA,QAAL,CAAcQ,YAHtB,EAGoC;AAClC;AACA,MAAA,MAAMvB,IAAI,GAAGI,SAAS,CAACrE,GAAV,CAAc/J,CAAd,IAAmBH,EAAE,CAACG,CAAH,GAAOoP,MAAM,CAACpP,CAAjC,CAAb,CAAA;;AACA,MAAA,IAAI,CAAC,IAAKqG,CAAAA,IAAL,CAAUyB,QAAV,CAAmB,cAAnB,EAAmC;AAAEkG,QAAAA,IAAAA;OAArC,CAAA,CAA6CtC,gBAAlD,EAAoE;AAClE,QAAA,IAAA,CAAK8D,mBAAL,CAAyB,GAAzB,EAA8BxB,IAA9B,EAAoCQ,sBAApC,CAAA,CAAA;;AACA,QAAA,MAAMiB,SAAS,GAAG,CAAA,GAAIpP,IAAI,CAACG,GAAL,CAAS,IAAA,CAAKkP,qBAAL,CAA2BtB,SAAS,CAACrE,GAAV,CAAc/J,CAAzC,CAAT,CAAtB,CAAA;AACA,QAAA,IAAA,CAAKqG,IAAL,CAAUsJ,cAAV,CAAyBF,SAAzB,CAAA,CAAA;AACArB,QAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,OAAA;AACF,KAZD,MAYO;AACL,MAAA,MAAMsE,iBAAiB,GAAG,IAAA,CAAKC,oBAAL,CAA0B,GAA1B,CAA1B,CAAA;;MACA,IAAI,CAACD,iBAAL,EAAwB;QACtB,IAAKC,CAAAA,oBAAL,CAA0B,GAA1B,CAAA,CAAA;;AAEA,QAAA,IAAIzB,SAAJ,EAAe;AACbjO,UAAAA,UAAU,CAACiO,SAAS,CAACrE,GAAX,CAAV,CAAA;AACAqE,UAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;;AAEDwE,EAAAA,GAAG,GAAG;IACJ,MAAM;AAAEC,MAAAA,QAAAA;AAAF,KAAA,GAAe,KAAKhB,QAA1B,CAAA;IACA,MAAM;MAAEvD,UAAF;AAAc4C,MAAAA,SAAAA;AAAd,KAAA,GAA4B,KAAK/H,IAAvC,CAAA;IACA,IAAI2J,SAAS,GAAG,CAAhB,CAAA;AAEA,IAAA,IAAA,CAAK3J,IAAL,CAAUuG,UAAV,CAAqBsC,OAArB,GALI;;AAQJ,IAAA,IAAI1D,UAAU,CAACC,SAAX,EAAJ,EAA4B;AAC1B;MACA,MAAMwE,mBAAmB,GAAGzE,UAAU,CAACzL,CAAX,GAAeyL,UAAU,CAAC0E,aAAX,EAA3C,CAF0B;AAK1B;AACA;AACA;;MACA,MAAMC,2BAA2B,GAAIF,mBAAmB,GAAG,IAAA,CAAK5J,IAAL,CAAUO,YAAV,CAAuB7G,CAAlF,CAR0B;AAW1B;AACA;AACA;AACA;AACA;AACA;AACA;;MACA,IAAKgQ,QAAQ,CAAChQ,CAAT,GAAa,CAAC2O,oBAAd,IAAsCyB,2BAA2B,GAAG,CAArE,IACIJ,QAAQ,CAAChQ,CAAT,GAAa,GAAb,IAAoBoQ,2BAA2B,GAAG,CAAC,GAD3D,EACiE;AAC/D;AACAH,QAAAA,SAAS,GAAG,CAAZ,CAAA;AACAD,QAAAA,QAAQ,CAAChQ,CAAT,GAAaM,IAAI,CAACQ,GAAL,CAASkP,QAAQ,CAAChQ,CAAlB,EAAqB,CAArB,CAAb,CAAA;OAJF,MAKO,IAAKgQ,QAAQ,CAAChQ,CAAT,GAAa2O,oBAAb,IAAqCyB,2BAA2B,GAAG,CAApE,IACHJ,QAAQ,CAAChQ,CAAT,GAAa,CAAC,GAAd,IAAqBoQ,2BAA2B,GAAG,GADpD,EAC0D;AAC/D;QACAH,SAAS,GAAG,CAAC,CAAb,CAAA;AACAD,QAAAA,QAAQ,CAAChQ,CAAT,GAAaM,IAAI,CAACS,GAAL,CAASiP,QAAQ,CAAChQ,CAAlB,EAAqB,CAArB,CAAb,CAAA;AACD,OAAA;;MAEDyL,UAAU,CAAC4E,WAAX,CAAuBJ,SAAvB,EAAkC,IAAlC,EAAwCD,QAAQ,CAAChQ,CAAjD,CAAA,CAAA;AACD,KAvCG;;;AA0CJ,IAAA,IAAKqO,SAAS,IAAIA,SAAS,CAAC3G,aAAV,GAA0B2G,SAAS,CAAC/E,UAAV,CAAqBvI,GAA7D,IACG,IAAA,CAAKiO,QAAL,CAAcQ,YADrB,EACmC;AACjC,MAAA,IAAA,CAAKR,QAAL,CAAc1F,UAAd,CAAyBgH,cAAzB,CAAwC,IAAxC,CAAA,CAAA;AACD,KAHD,MAGO;AACL;AACA;AACA;AACA;MACA,IAAKC,CAAAA,wBAAL,CAA8B,GAA9B,CAAA,CAAA;;MACA,IAAKA,CAAAA,wBAAL,CAA8B,GAA9B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;EACEA,wBAAwB,CAACvI,IAAD,EAAO;IAC7B,MAAM;AAAEgI,MAAAA,QAAAA;AAAF,KAAA,GAAe,KAAKhB,QAA1B,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK/H,IAA3B,CAAA;;IAEA,IAAI,CAAC+H,SAAL,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;IAED,MAAM;MAAErE,GAAF;AAAOU,MAAAA,MAAAA;AAAP,KAAA,GAAkB2D,SAAxB,CAAA;AACA,IAAA,MAAMmC,MAAM,GAAGxG,GAAG,CAAChC,IAAD,CAAlB,CAAA;AACA,IAAA,MAAMyI,gBAAgB,GAAI,IAAKnK,CAAAA,IAAL,CAAUoJ,SAAV,GAAsB,CAAtB,IAA2B1H,IAAI,KAAK,GAA9D,CAV6B;AAa7B;;AACA,IAAA,MAAM8G,gBAAgB,GAAG,KAAzB,CAd6B;AAgB7B;;AACA,IAAA,MAAM4B,iBAAiB,GAAGF,MAAM,GAAG5B,OAAO,CAACoB,QAAQ,CAAChI,IAAD,CAAT,EAAiB8G,gBAAjB,CAA1C,CAAA;;AAEA,IAAA,IAAI2B,gBAAJ,EAAsB;AACpB,MAAA,MAAME,UAAU,GAAG,IAAA,CAAKhB,qBAAL,CAA2Ba,MAA3B,CAAnB,CAAA;;MACA,MAAMI,mBAAmB,GAAG,IAAKjB,CAAAA,qBAAL,CAA2Be,iBAA3B,CAA5B,CAFoB;AAKpB;;;AACA,MAAA,IAAKC,UAAU,GAAG,CAAb,IAAkBC,mBAAmB,GAAG,CAAClC,kBAA1C,IACIiC,UAAU,GAAG,CAAb,IAAkBC,mBAAmB,GAAGlC,kBADhD,EACqE;QACnE,IAAKpI,CAAAA,IAAL,CAAUuK,KAAV,EAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;AACF,KA9B4B;;;IAiC7B,MAAMC,oBAAoB,GAAGpG,MAAM,CAACrC,UAAP,CAAkBL,IAAlB,EAAwB0I,iBAAxB,CAA7B,CAjC6B;AAoC7B;;IACA,IAAIF,MAAM,KAAKM,oBAAf,EAAqC;AACnC,MAAA,OAAA;AACD,KAvC4B;;;IA0C7B,MAAMC,YAAY,GAAID,oBAAoB,KAAKJ,iBAA1B,GAA+C,CAA/C,GAAmD,IAAxE,CAAA;AAEA,IAAA,MAAMM,gBAAgB,GAAG,IAAK1K,CAAAA,IAAL,CAAUoJ,SAAnC,CAAA;AACA,IAAA,MAAMuB,YAAY,GAAGH,oBAAoB,GAAGN,MAA5C,CAAA;AAEA,IAAA,IAAA,CAAKlK,IAAL,CAAUuG,UAAV,CAAqBqE,WAArB,CAAiC;MAC/B5D,IAAI,EAAE,eAAetF,IADU;AAE/BqF,MAAAA,KAAK,EAAE,IAFwB;AAG/B6B,MAAAA,KAAK,EAAEsB,MAHwB;AAI/BT,MAAAA,GAAG,EAAEe,oBAJ0B;AAK/Bd,MAAAA,QAAQ,EAAEA,QAAQ,CAAChI,IAAD,CALa;MAM/B+I,YAN+B;MAO/BI,QAAQ,EAAGC,GAAD,IAAS;AACjB;QACA,IAAIX,gBAAgB,IAAI,IAAKnK,CAAAA,IAAL,CAAUoJ,SAAV,GAAsB,CAA9C,EAAiD;AAC/C;UACA,MAAM2B,sBAAsB,GAAG,CAAA,GAAI,CAACP,oBAAoB,GAAGM,GAAxB,IAA+BH,YAAlE,CAF+C;AAK/C;AACA;;AACA,UAAA,IAAA,CAAK3K,IAAL,CAAUsJ,cAAV,CAAyBhP,KAAK,CAC5BoQ,gBAAgB,GAAG,CAAC,CAAA,GAAIA,gBAAL,IAAyBK,sBADhB,EAE5B,CAF4B,EAG5B,CAH4B,CAA9B,CAAA,CAAA;AAKD,SAAA;;QAEDrH,GAAG,CAAChC,IAAD,CAAH,GAAY1H,IAAI,CAACgR,KAAL,CAAWF,GAAX,CAAZ,CAAA;AACA/C,QAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,OAAA;KAzBH,CAAA,CAAA;AA2BD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEuE,oBAAoB,CAAC9H,IAAD,EAAO;IACzB,MAAM;MAAElI,EAAF;MAAMwP,QAAN;MAAgBD,MAAhB;AAAwBG,MAAAA,YAAAA;AAAxB,KAAA,GAAyC,KAAKR,QAApD,CAAA;IACA,MAAM;MAAEX,SAAF;AAAa5C,MAAAA,UAAAA;AAAb,KAAA,GAA4B,KAAKnF,IAAvC,CAAA;IACA,MAAMiL,KAAK,GAAIzR,EAAE,CAACkI,IAAD,CAAF,GAAWqH,MAAM,CAACrH,IAAD,CAAhC,CAAA;AACA,IAAA,MAAMwJ,cAAc,GAAG/F,UAAU,CAACzL,CAAX,GAAeuR,KAAtC,CAAA;;AAEA,IAAA,IAAI,CAACA,KAAD,IAAU,CAAClD,SAAf,EAA0B;AACxB,MAAA,OAAO,KAAP,CAAA;AACD,KARwB;;;AAWzB,IAAA,IAAIrG,IAAI,KAAK,GAAT,IAAgB,CAACqG,SAAS,CAACH,UAAV,EAAjB,IAA2C,CAACsB,YAAhD,EAA8D;AAC5D/D,MAAAA,UAAU,CAACgG,MAAX,CAAkBD,cAAlB,EAAkC,IAAlC,CAAA,CAAA;MACA,OAAO,IAAP,CAF4D;AAG7D,KAAA;;IAED,MAAM;AAAE9G,MAAAA,MAAAA;AAAF,KAAA,GAAa2D,SAAnB,CAAA;IACA,MAAMqD,MAAM,GAAGrD,SAAS,CAACrE,GAAV,CAAchC,IAAd,IAAsBuJ,KAArC,CAAA;;AAEA,IAAA,IAAI,KAAKjL,IAAL,CAAUD,OAAV,CAAkBsL,cAAlB,IACGrC,QAAQ,KAAK,GADhB,IAEGtH,IAAI,KAAK,GAFZ,IAGG,CAACwH,YAHR,EAGsB;AACpB,MAAA,MAAMoC,oBAAoB,GAAGnG,UAAU,CAAC0E,aAAX,EAA7B,CADoB;;AAIpB,MAAA,MAAMD,mBAAmB,GAAGzE,UAAU,CAACzL,CAAX,GAAe4R,oBAA3C,CAAA;AAEA,MAAA,MAAMC,aAAa,GAAGN,KAAK,GAAG,CAA9B,CAAA;MACA,MAAMO,aAAa,GAAG,CAACD,aAAvB,CAAA;;MAEA,IAAIH,MAAM,GAAGhH,MAAM,CAAC5J,GAAP,CAAWkH,IAAX,CAAT,IAA6B6J,aAAjC,EAAgD;AAC9C;AAEA;AACA;AACA;AACA,QAAA,MAAME,mBAAmB,GAAIrH,MAAM,CAAC5J,GAAP,CAAWkH,IAAX,CAAA,IAAoB,IAAKiH,CAAAA,QAAL,CAAcjH,IAAd,CAAjD,CAAA;;AAEA,QAAA,IAAI+J,mBAAJ,EAAyB;AACvBtG,UAAAA,UAAU,CAACgG,MAAX,CAAkBD,cAAlB,EAAkC,IAAlC,CAAA,CAAA;AACA,UAAA,OAAO,IAAP,CAAA;AACD,SAHD,MAGO;AACL,UAAA,IAAA,CAAK/B,mBAAL,CAAyBzH,IAAzB,EAA+B0J,MAA/B,EADK;;AAGN,SAAA;AACF,OAfD,MAeO,IAAIA,MAAM,GAAGhH,MAAM,CAAC3J,GAAP,CAAWiH,IAAX,CAAT,IAA6B8J,aAAjC,EAAgD;AACrD;AAEA;AACA,QAAA,MAAME,mBAAmB,GAAI,IAAK/C,CAAAA,QAAL,CAAcjH,IAAd,CAAuB0C,IAAAA,MAAM,CAAC3J,GAAP,CAAWiH,IAAX,CAApD,CAAA;;AAEA,QAAA,IAAIgK,mBAAJ,EAAyB;AACvBvG,UAAAA,UAAU,CAACgG,MAAX,CAAkBD,cAAlB,EAAkC,IAAlC,CAAA,CAAA;AACA,UAAA,OAAO,IAAP,CAAA;AACD,SAHD,MAGO;AACL,UAAA,IAAA,CAAK/B,mBAAL,CAAyBzH,IAAzB,EAA+B0J,MAA/B,EADK;;AAGN,SAAA;AACF,OAbM,MAaA;AACL;QACA,IAAIxB,mBAAmB,KAAK,CAA5B,EAA+B;AAC7B;AACA,UAAA,IAAIA,mBAAmB,GAAG,CAAA;AAAE;YAAsB;AAChDzE,YAAAA,UAAU,CAACgG,MAAX,CAAkBnR,IAAI,CAACS,GAAL,CAASyQ,cAAT,EAAyBI,oBAAzB,CAAlB,EAAkE,IAAlE,CAAA,CAAA;AACA,YAAA,OAAO,IAAP,CAAA;WAFF,MAGO,IAAI1B,mBAAmB,GAAG,CAAA;AAAE;YAAsB;AACvD;AACAzE,YAAAA,UAAU,CAACgG,MAAX,CAAkBnR,IAAI,CAACQ,GAAL,CAAS0Q,cAAT,EAAyBI,oBAAzB,CAAlB,EAAkE,IAAlE,CAAA,CAAA;AACA,YAAA,OAAO,IAAP,CAAA;AACD,WAAA;AACF,SAVD,MAUO;AACL;AACA,UAAA,IAAA,CAAKnC,mBAAL,CAAyBzH,IAAzB,EAA+B0J,MAA/B,CAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAzDD,MAyDO;MACL,IAAI1J,IAAI,KAAK,GAAb,EAAkB;AAChB;AACA,QAAA,IAAI,CAACyD,UAAU,CAACC,SAAX,EAAD,IAA2BhB,MAAM,CAAC5J,GAAP,CAAWb,CAAX,KAAiByK,MAAM,CAAC3J,GAAP,CAAWd,CAA3D,EAA8D;AAC5D,UAAA,IAAA,CAAKwP,mBAAL,CAAyBzH,IAAzB,EAA+B0J,MAA/B,CAAA,CAAA;AACD,SAAA;AACF,OALD,MAKO;AACL,QAAA,IAAA,CAAKjC,mBAAL,CAAyBzH,IAAzB,EAA+B0J,MAA/B,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,OAAO,KAAP,CAAA;AACD,GAxRe;AA2RhB;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACE/B,qBAAqB,CAAC1B,IAAD,EAAO;AAAA,IAAA,IAAA,qBAAA,EAAA,oBAAA,CAAA;;IAC1B,OAAO,CAACA,IAAI,IAAA,CAAA,qBAAA,GAAA,CAAA,oBAAA,GAAI,IAAK3H,CAAAA,IAAL,CAAU+H,SAAd,MAAI,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,oBAAA,CAAqB3D,MAArB,CAA4B/C,MAA5B,CAAmC1H,CAAvC,MAA4C,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAAA,CAA5C,CAAL,KAAwD,IAAKqG,CAAAA,IAAL,CAAUO,YAAV,CAAuB5G,CAAvB,GAA2B,CAAnF,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEwP,EAAAA,mBAAmB,CAACzH,IAAD,EAAOiK,YAAP,EAAqBC,cAArB,EAAqC;IACtD,MAAM;AAAE7D,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK/H,IAA3B,CAAA;;IAEA,IAAI,CAAC+H,SAAL,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;IAED,MAAM;MAAErE,GAAF;AAAOU,MAAAA,MAAAA;AAAP,KAAA,GAAkB2D,SAAxB,CAAA;IACA,MAAM8D,YAAY,GAAGzH,MAAM,CAACrC,UAAP,CAAkBL,IAAlB,EAAwBiK,YAAxB,CAArB,CARsD;;AAUtD,IAAA,IAAIE,YAAY,KAAKF,YAAjB,IAAiCC,cAArC,EAAqD;AACnD,MAAA,MAAMX,KAAK,GAAGjR,IAAI,CAACC,KAAL,CAAW0R,YAAY,GAAGjI,GAAG,CAAChC,IAAD,CAA7B,CAAd,CAAA;MACAgC,GAAG,CAAChC,IAAD,CAAH,IAAauJ,KAAK,IAAIW,cAAc,IAAI1D,gBAAtB,CAAlB,CAAA;AACD,KAHD,MAGO;AACLxE,MAAAA,GAAG,CAAChC,IAAD,CAAH,GAAYiK,YAAZ,CAAA;AACD,KAAA;AACF,GAAA;;AAtUe;;ACzBlB;;AACA;;AAEA,MAAMG,mBAAmB,GAAG,IAA5B,CAAA;AACA,MAAMC,mBAAmB,GAAG,IAA5B,CAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,mBAAT,CAA6BjS,CAA7B,EAAgCP,EAAhC,EAAoCC,EAApC,EAAwC;AACtCM,EAAAA,CAAC,CAACL,CAAF,GAAM,CAACF,EAAE,CAACE,CAAH,GAAOD,EAAE,CAACC,CAAX,IAAgB,CAAtB,CAAA;AACAK,EAAAA,CAAC,CAACJ,CAAF,GAAM,CAACH,EAAE,CAACG,CAAH,GAAOF,EAAE,CAACE,CAAX,IAAgB,CAAtB,CAAA;AACA,EAAA,OAAOI,CAAP,CAAA;AACD,CAAA;;AAED,MAAMkS,WAAN,CAAkB;AAChB;AACF;AACA;EACExN,WAAW,CAACiK,QAAD,EAAW;IACpB,IAAKA,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;AACA;AACJ;AACA;AACA;;AACI,IAAA,IAAA,CAAKwD,SAAL,GAAiB;AAAExS,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA5B,CAAA;AACA;AACJ;AACA;AACA;;AACI,IAAA,IAAA,CAAKwS,eAAL,GAAuB;AAAEzS,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAlC,CAAA;AACA;AACJ;AACA;AACA;;AACI,IAAA,IAAA,CAAKyS,UAAL,GAAkB;AAAE1S,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA7B,CAAA;AACA;;IACA,IAAK0S,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;AACA;;IACA,IAAKC,CAAAA,eAAL,GAAuB,CAAvB,CAAA;AACD,GAAA;;AAED1D,EAAAA,KAAK,GAAG;IACN,MAAM;AAAEb,MAAAA,SAAAA;KAAc,GAAA,IAAA,CAAKW,QAAL,CAAc1I,IAApC,CAAA;;AACA,IAAA,IAAI+H,SAAJ,EAAe;AACb,MAAA,IAAA,CAAKuE,eAAL,GAAuBvE,SAAS,CAAC3G,aAAjC,CAAA;AACA7H,MAAAA,cAAc,CAAC,IAAK2S,CAAAA,SAAN,EAAiBnE,SAAS,CAACrE,GAA3B,CAAd,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKgF,QAAL,CAAc1I,IAAd,CAAmBuG,UAAnB,CAA8BC,UAA9B,EAAA,CAAA;IACA,IAAK6F,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;AACD,GAAA;;AAEDvD,EAAAA,MAAM,GAAG;IACP,MAAM;MAAEtP,EAAF;MAAM+S,OAAN;MAAe9S,EAAf;MAAmB+S,OAAnB;AAA4BxM,MAAAA,IAAAA;AAA5B,KAAA,GAAqC,KAAK0I,QAAhD,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB/H,IAAtB,CAAA;;IAEA,IAAI,CAAC+H,SAAL,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM0E,YAAY,GAAG1E,SAAS,CAAC/E,UAAV,CAAqBxI,GAA1C,CAAA;AACA,IAAA,MAAMkS,YAAY,GAAG3E,SAAS,CAAC/E,UAAV,CAAqBvI,GAA1C,CAAA;;AAEA,IAAA,IAAI,CAACsN,SAAS,CAACzB,UAAV,EAAD,IAA2BtG,IAAI,CAACmF,UAAL,CAAgBC,SAAhB,EAA/B,EAA4D;AAC1D,MAAA,OAAA;AACD,KAAA;;AAED4G,IAAAA,mBAAmB,CAAC,IAAKG,CAAAA,eAAN,EAAuBI,OAAvB,EAAgCC,OAAhC,CAAnB,CAAA;AACAR,IAAAA,mBAAmB,CAAC,IAAKI,CAAAA,UAAN,EAAkB5S,EAAlB,EAAsBC,EAAtB,CAAnB,CAAA;;IAEA,IAAI2H,aAAa,GAAI,CAAIlH,GAAAA,kBAAkB,CAACqS,OAAD,EAAUC,OAAV,CAAvB,GACAtS,kBAAkB,CAACV,EAAD,EAAKC,EAAL,CADlB,GAEA,IAAK6S,CAAAA,eAFzB,CAlBO;;;AAuBP,IAAA,IAAIlL,aAAa,GAAG2G,SAAS,CAAC/E,UAAV,CAAqBT,OAArB,GAAgCwF,SAAS,CAAC/E,UAAV,CAAqBT,OAArB,GAA+B,EAAnF,EAAwF;MACtF,IAAK8J,CAAAA,oBAAL,GAA4B,IAA5B,CAAA;AACD,KAAA;;IAED,IAAIjL,aAAa,GAAGqL,YAApB,EAAkC;AAChC,MAAA,IAAIzM,IAAI,CAACD,OAAL,CAAa4M,YAAb,IACG,CAAC,IAAKN,CAAAA,oBADT,IAEG,IAAA,CAAKC,eAAL,IAAwBvE,SAAS,CAAC/E,UAAV,CAAqBT,OAFpD,EAE6D;AAC3D;QACA,MAAM6G,SAAS,GAAG,CAAA,GAAK,CAACqD,YAAY,GAAGrL,aAAhB,KAAkCqL,YAAY,GAAG,GAAjD,CAAvB,CAAA;;AACA,QAAA,IAAI,CAACzM,IAAI,CAACyB,QAAL,CAAc,YAAd,EAA4B;AAAE2H,UAAAA,SAAAA;SAA9B,CAAA,CAA2C/D,gBAAhD,EAAkE;UAChErF,IAAI,CAACsJ,cAAL,CAAoBF,SAApB,CAAA,CAAA;AACD,SAAA;AACF,OARD,MAQO;AACL;QACAhI,aAAa,GAAGqL,YAAY,GAAG,CAACA,YAAY,GAAGrL,aAAhB,IAAiC2K,mBAAhE,CAAA;AACD,OAAA;AACF,KAbD,MAaO,IAAI3K,aAAa,GAAGsL,YAApB,EAAkC;AACvC;MACAtL,aAAa,GAAGsL,YAAY,GAAG,CAACtL,aAAa,GAAGsL,YAAjB,IAAiCZ,mBAAhE,CAAA;AACD,KAAA;;IAED/D,SAAS,CAACrE,GAAV,CAAchK,CAAd,GAAkB,IAAKkT,CAAAA,yBAAL,CAA+B,GAA/B,EAAoCxL,aAApC,CAAlB,CAAA;IACA2G,SAAS,CAACrE,GAAV,CAAc/J,CAAd,GAAkB,IAAKiT,CAAAA,yBAAL,CAA+B,GAA/B,EAAoCxL,aAApC,CAAlB,CAAA;IAEA2G,SAAS,CAACrB,YAAV,CAAuBtF,aAAvB,CAAA,CAAA;AACA2G,IAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,GAAA;;AAEDwE,EAAAA,GAAG,GAAG;IACJ,MAAM;AAAEzJ,MAAAA,IAAAA;AAAF,KAAA,GAAW,KAAK0I,QAAtB,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB/H,IAAtB,CAAA;;IACA,IAAI,CAAC,CAAC+H,SAAD,IAAcA,SAAS,CAAC3G,aAAV,GAA0B2G,SAAS,CAAC/E,UAAV,CAAqBT,OAA9D,KACG,CAAC,IAAA,CAAK8J,oBADT,IAEGrM,IAAI,CAACD,OAAL,CAAa4M,YAFpB,EAEkC;AAChC3M,MAAAA,IAAI,CAACuK,KAAL,EAAA,CAAA;AACD,KAJD,MAIO;AACL,MAAA,IAAA,CAAKP,cAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE4C,EAAAA,yBAAyB,CAAClL,IAAD,EAAON,aAAP,EAAsB;AAC7C,IAAA,MAAMqG,UAAU,GAAGrG,aAAa,GAAG,KAAKkL,eAAxC,CAAA;AACA,IAAA,OAAO,KAAKF,UAAL,CAAgB1K,IAAhB,CAAA,GACI,CAAC,IAAKyK,CAAAA,eAAL,CAAqBzK,IAArB,IAA6B,IAAKwK,CAAAA,SAAL,CAAexK,IAAf,CAA9B,IAAsD+F,UADjE,CAAA;AAED,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEuC,cAAc,CAAC6C,aAAD,EAAgB;IAC5B,MAAM;AAAE7M,MAAAA,IAAAA;AAAF,KAAA,GAAW,KAAK0I,QAAtB,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB/H,IAAtB,CAAA;;IAEA,IAAI,EAAC+H,SAAD,KAAA,IAAA,IAACA,SAAD,KAAA,KAAA,CAAA,IAACA,SAAS,CAAEzB,UAAX,EAAD,CAAJ,EAA8B;AAC5B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAK8F,UAAL,CAAgB1S,CAAhB,KAAsB,CAA1B,EAA6B;AAC3BmT,MAAAA,aAAa,GAAG,IAAhB,CAAA;AACD,KAAA;;AAED,IAAA,MAAMpG,aAAa,GAAGsB,SAAS,CAAC3G,aAAhC,CAAA;AAEA;;AACA,IAAA,IAAI0L,oBAAJ,CAAA;IACA,IAAIC,wBAAwB,GAAG,IAA/B,CAAA;;AAEA,IAAA,IAAItG,aAAa,GAAGsB,SAAS,CAAC/E,UAAV,CAAqBT,OAAzC,EAAkD;AAChDuK,MAAAA,oBAAoB,GAAG/E,SAAS,CAAC/E,UAAV,CAAqBT,OAA5C,CADgD;KAAlD,MAGO,IAAIkE,aAAa,GAAGsB,SAAS,CAAC/E,UAAV,CAAqBvI,GAAzC,EAA8C;AACnDqS,MAAAA,oBAAoB,GAAG/E,SAAS,CAAC/E,UAAV,CAAqBvI,GAA5C,CADmD;AAGpD,KAHM,MAGA;AACLsS,MAAAA,wBAAwB,GAAG,KAA3B,CAAA;AACAD,MAAAA,oBAAoB,GAAGrG,aAAvB,CAAA;AACD,KAAA;;AAED,IAAA,MAAMiE,gBAAgB,GAAG1K,IAAI,CAACoJ,SAA9B,CAAA;AACA,IAAA,MAAMe,gBAAgB,GAAGnK,IAAI,CAACoJ,SAAL,GAAiB,CAA1C,CAAA;IAEA,MAAM4D,UAAU,GAAGzT,cAAc,CAAC;AAAEG,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;AAAX,KAAD,EAAiBoO,SAAS,CAACrE,GAA3B,CAAjC,CAAA;IACA,IAAIuJ,cAAc,GAAG1T,cAAc,CAAC;AAAEG,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAZ,EAAiBqT,UAAjB,CAAnC,CAAA;;AAEA,IAAA,IAAIH,aAAJ,EAAmB;AACjB,MAAA,IAAA,CAAKT,UAAL,CAAgB1S,CAAhB,GAAoB,CAApB,CAAA;AACA,MAAA,IAAA,CAAK0S,UAAL,CAAgBzS,CAAhB,GAAoB,CAApB,CAAA;AACA,MAAA,IAAA,CAAKwS,eAAL,CAAqBzS,CAArB,GAAyB,CAAzB,CAAA;AACA,MAAA,IAAA,CAAKyS,eAAL,CAAqBxS,CAArB,GAAyB,CAAzB,CAAA;MACA,IAAK2S,CAAAA,eAAL,GAAuB7F,aAAvB,CAAA;AACAlN,MAAAA,cAAc,CAAC,IAAA,CAAK2S,SAAN,EAAiBc,UAAjB,CAAd,CAAA;AACD,KAAA;;AAED,IAAA,IAAID,wBAAJ,EAA8B;AAC5BE,MAAAA,cAAc,GAAG;AACfvT,QAAAA,CAAC,EAAE,IAAKkT,CAAAA,yBAAL,CAA+B,GAA/B,EAAoCE,oBAApC,CADY;AAEfnT,QAAAA,CAAC,EAAE,IAAKiT,CAAAA,yBAAL,CAA+B,GAA/B,EAAoCE,oBAApC,CAAA;OAFL,CAAA;AAID,KAjD2B;;;IAoD5B/E,SAAS,CAACrB,YAAV,CAAuBoG,oBAAvB,CAAA,CAAA;AAEAG,IAAAA,cAAc,GAAG;AACfvT,MAAAA,CAAC,EAAEqO,SAAS,CAAC3D,MAAV,CAAiBrC,UAAjB,CAA4B,GAA5B,EAAiCkL,cAAc,CAACvT,CAAhD,CADY;MAEfC,CAAC,EAAEoO,SAAS,CAAC3D,MAAV,CAAiBrC,UAAjB,CAA4B,GAA5B,EAAiCkL,cAAc,CAACtT,CAAhD,CAAA;AAFY,KAAjB,CAtD4B;;IA4D5BoO,SAAS,CAACrB,YAAV,CAAuBD,aAAvB,CAAA,CAAA;IAEA,MAAMyG,cAAc,GAAG,CAAC7S,WAAW,CAAC4S,cAAD,EAAiBD,UAAjB,CAAnC,CAAA;;IAEA,IAAI,CAACE,cAAD,IAAmB,CAACH,wBAApB,IAAgD,CAAC5C,gBAArD,EAAuE;AACrE;MACApC,SAAS,CAAClB,cAAV,CAAyBiG,oBAAzB,CAAA,CAAA;;MACA/E,SAAS,CAAC9C,mBAAV,EAAA,CAHqE;;AAMrE,MAAA,OAAA;AACD,KAAA;;IAEDjF,IAAI,CAACuG,UAAL,CAAgBC,UAAhB,EAAA,CAAA;AAEAxG,IAAAA,IAAI,CAACuG,UAAL,CAAgBqE,WAAhB,CAA4B;AAC1B7D,MAAAA,KAAK,EAAE,IADmB;AAE1B6B,MAAAA,KAAK,EAAE,CAFmB;AAG1Ba,MAAAA,GAAG,EAAE,IAHqB;AAI1BC,MAAAA,QAAQ,EAAE,CAJgB;AAK1Be,MAAAA,YAAY,EAAE,CALY;AAM1B0C,MAAAA,gBAAgB,EAAE,EANQ;MAO1BtC,QAAQ,EAAGuC,GAAD,IAAS;QACjBA,GAAG,IAAI,IAAP,CADiB;;QAGjB,IAAIF,cAAc,IAAIH,wBAAtB,EAAgD;AAC9C,UAAA,IAAIG,cAAJ,EAAoB;AAClBnF,YAAAA,SAAS,CAACrE,GAAV,CAAchK,CAAd,GAAkBsT,UAAU,CAACtT,CAAX,GAAe,CAACuT,cAAc,CAACvT,CAAf,GAAmBsT,UAAU,CAACtT,CAA/B,IAAoC0T,GAArE,CAAA;AACArF,YAAAA,SAAS,CAACrE,GAAV,CAAc/J,CAAd,GAAkBqT,UAAU,CAACrT,CAAX,GAAe,CAACsT,cAAc,CAACtT,CAAf,GAAmBqT,UAAU,CAACrT,CAA/B,IAAoCyT,GAArE,CAAA;AACD,WAAA;;AAED,UAAA,IAAIL,wBAAJ,EAA8B;YAC5B,MAAMM,YAAY,GAAG5G,aAAa,GACpB,CAACqG,oBAAoB,GAAGrG,aAAxB,IAAyC2G,GADvD,CAAA;YAEArF,SAAS,CAACrB,YAAV,CAAuB2G,YAAvB,CAAA,CAAA;AACD,WAAA;;AAEDtF,UAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,SAhBgB;;;AAmBjB,QAAA,IAAIkF,gBAAgB,IAAInK,IAAI,CAACoJ,SAAL,GAAiB,CAAzC,EAA4C;AAC1C;AACA;AACA;AACApJ,UAAAA,IAAI,CAACsJ,cAAL,CAAoBhP,KAAK,CACvBoQ,gBAAgB,GAAG,CAAC,CAAIA,GAAAA,gBAAL,IAAyB0C,GADrB,EAC0B,CAD1B,EAC6B,CAD7B,CAAzB,CAAA,CAAA;AAGD,SAAA;OAjCuB;AAmC1BlG,MAAAA,UAAU,EAAE,MAAM;AAChB;QACAa,SAAS,CAAClB,cAAV,CAAyBiG,oBAAzB,CAAA,CAAA;;AACA/E,QAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,OAAA;KAvCH,CAAA,CAAA;AAyCD,GAAA;;AA9Oe;;ACzBlB;AACA;AACA;AACA;AACA;;AAEA;;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqI,mBAAT,CAA6BC,KAA7B,EAAoC;AAClC,EAAA,OAAO,CAAC;AAAE;AAA4BA,EAAAA,KAAK,CAAC3O,MAAP,CAAe4O,OAAf,CAAuB,kBAAvB,CAArC,CAAA;AACD,CAAA;AAED;AACA;AACA;;;AACA,MAAMC,UAAN,CAAiB;AACf;AACF;AACA;EACEhP,WAAW,CAACiK,QAAD,EAAW;IACpB,IAAKA,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEgF,EAAAA,KAAK,CAACpG,KAAD,EAAQqG,aAAR,EAAuB;AAC1B,IAAA,MAAMC,eAAe;AAAG;IAA4BD,aAAa,CAAC/O,MAAf,CAAuBiP,SAA1E,CAAA;AACA,IAAA,MAAMC,YAAY,GAAGF,eAAe,CAACG,QAAhB,CAAyB,WAAzB,CAArB,CAAA;AACA,IAAA,MAAMC,iBAAiB,GAAGJ,eAAe,CAACG,QAAhB,CAAyB,YAAzB,CAAA,IACGH,eAAe,CAACG,QAAhB,CAAyB,iBAAzB,CAD7B,CAAA;;AAGA,IAAA,IAAID,YAAJ,EAAkB;AAChB,MAAA,IAAA,CAAKG,mBAAL,CAAyB,YAAzB,EAAuC3G,KAAvC,EAA8CqG,aAA9C,CAAA,CAAA;KADF,MAEO,IAAIK,iBAAJ,EAAuB;AAC5B,MAAA,IAAA,CAAKC,mBAAL,CAAyB,SAAzB,EAAoC3G,KAApC,EAA2CqG,aAA3C,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;AACEO,EAAAA,GAAG,CAAC5G,KAAD,EAAQqG,aAAR,EAAuB;AACxB,IAAA,IAAIL,mBAAmB,CAACK,aAAD,CAAvB,EAAwC;AACtC,MAAA,IAAA,CAAKM,mBAAL,CAAyB,KAAzB,EAAgC3G,KAAhC,EAAuCqG,aAAvC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;AACEQ,EAAAA,SAAS,CAAC7G,KAAD,EAAQqG,aAAR,EAAuB;AAC9B,IAAA,IAAIL,mBAAmB,CAACK,aAAD,CAAvB,EAAwC;AACtC,MAAA,IAAA,CAAKM,mBAAL,CAAyB,WAAzB,EAAsC3G,KAAtC,EAA6CqG,aAA7C,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEM,EAAAA,mBAAmB,CAACG,UAAD,EAAa9G,KAAb,EAAoBqG,aAApB,EAAmC;AAAA,IAAA,IAAA,qBAAA,CAAA;;IACpD,MAAM;AAAE3N,MAAAA,IAAAA;AAAF,KAAA,GAAW,KAAK0I,QAAtB,CAAA;IACA,MAAM;AAAEX,MAAAA,SAAAA;AAAF,KAAA,GAAgB/H,IAAtB,CAAA;AACA,IAAA,MAAMqO,cAAc;AAAG;AAA8CD,IAAAA,UAAU,GAAG,QAAlF,CAAA;AACA,IAAA,MAAM/K,WAAW,GAAGrD,IAAI,CAACD,OAAL,CAAasO,cAAb,CAApB,CAAA;;AAEA,IAAA,IAAIrO,IAAI,CAACyB,QAAL,CAAc4M,cAAd,EAA8B;MAAE/G,KAAF;AAASqG,MAAAA,aAAAA;KAAvC,CAAA,CAAwDtI,gBAA5D,EAA8E;AAC5E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOhC,WAAP,KAAuB,UAA3B,EAAuC;AACrCA,MAAAA,WAAW,CAACiL,IAAZ,CAAiBtO,IAAjB,EAAuBsH,KAAvB,EAA8BqG,aAA9B,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,QAAQtK,WAAR;AACE,MAAA,KAAK,OAAL,CAAA;AACA,MAAA,KAAK,MAAL;QACErD,IAAI,CAACqD,WAAD,CAAJ,EAAA,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAK,MAAL;QACE0E,SAAS,KAAA,IAAT,IAAAA,SAAS,KAAA,KAAA,CAAT,IAAAA,SAAS,CAAEX,UAAX,CAAsBE,KAAtB,CAAA,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAK,eAAL;AACE;AACA;QACA,IAAIS,SAAS,SAAT,IAAAA,SAAS,WAAT,IAAAA,SAAS,CAAEzB,UAAX,EAAA,IACGyB,SAAS,CAAC/E,UAAV,CAAqBR,SAArB,KAAmCuF,SAAS,CAAC/E,UAAV,CAAqBT,OAD/D,EACwE;UACtEwF,SAAS,CAACX,UAAV,CAAqBE,KAArB,CAAA,CAAA;AACD,SAHD,MAGO,IAAItH,IAAI,CAACD,OAAL,CAAawO,uBAAjB,EAA0C;AAC/CvO,UAAAA,IAAI,CAACuK,KAAL,EAAA,CAAA;AACD,SAAA;;AACD,QAAA,MAAA;;AACF,MAAA,KAAK,iBAAL;AACE,QAAA,CAAA,qBAAA,GAAA,IAAA,CAAK7B,QAAL,CAAc1I,IAAd,CAAmBgG,OAAnB,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAA4B6H,SAA5B,CAAsCW,MAAtC,CAA6C,kBAA7C,CAAA,CADF;AAGE;AACA;AACA;AACA;;AACA,QAAA,MAAA;AAzBJ,KAAA;AA2BD,GAAA;;AA7Fc;;ACjBjB;;AACA;AAEA;AACA;;AACA,MAAMC,qBAAqB,GAAG,EAA9B;;AAGA,MAAMC,gBAAgB,GAAG,GAAzB;;AACA,MAAMC,gBAAgB,GAAG,EAAzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,QAAN,CAAe;AACb;AACF;AACA;EACEnQ,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AAEA;;AACA,IAAA,IAAA,CAAKgJ,QAAL,GAAgB,IAAhB,CAJgB;AAOhB;;AACA;;AACA,IAAA,IAAA,CAAKxP,EAAL,GAAU;AAAEE,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;AAAX,KAAV,CATgB;;AAUhB;;AACA,IAAA,IAAA,CAAKF,EAAL,GAAU;AAAEC,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;AAAX,KAAV,CAXgB;;AAYhB;;AACA,IAAA,IAAA,CAAKoP,MAAL,GAAc;AAAErP,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAzB,CAAA;AACA;;AACA,IAAA,IAAA,CAAKkV,MAAL,GAAc;AAAEnV,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAzB,CAAA;AACA;;AACA,IAAA,IAAA,CAAK4S,OAAL,GAAe;AAAE7S,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA1B,CAAA;AACA;;AACA,IAAA,IAAA,CAAK6S,OAAL,GAAe;AAAE9S,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA1B,CAAA;AACA;;AACA,IAAA,IAAA,CAAK+P,QAAL,GAAgB;AAAEhQ,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA3B,CAAA;AAEA;AACJ;AACA;;AACI,IAAA,IAAA,CAAKmV,YAAL,GAAoB;AAAEpV,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA/B,CAAA;AACA;AACJ;AACA;;AACI,IAAA,IAAA,CAAKoV,WAAL,GAAmB;AAAErV,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA9B,CAAA;AACA;;IACA,IAAKqV,CAAAA,gBAAL,GAAwB,CAAxB,CAAA;AACA;AACJ;AACA;;IACI,IAAKC,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;AACA;;IACA,IAAKC,CAAAA,kBAAL,GAA0B,cAAA,IAAkB/Q,MAA5C,CAAA;AACA;;AACA,IAAA,IAAA,CAAKgR,oBAAL,GAA4B,CAAC,CAAEhR,MAAM,CAACiR,YAAtC,CAAA;AACA,IAAA,IAAA,CAAKC,aAAL,GAAqB,IAAKH,CAAAA,kBAAL,IACK,IAAA,CAAKC,oBAAL,IAA6BpR,SAAS,CAACuR,cAAV,GAA2B,CADlF,CAAA;AAEA;;IACA,IAAKN,CAAAA,gBAAL,GAAwB,CAAxB,CAAA;AACA;;IACA,IAAKO,CAAAA,aAAL,GAAqB,CAArB,CAAA;AACA;;IACA,IAAKC,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;IACA,IAAKtG,CAAAA,YAAL,GAAoB,KAApB,CAAA;IACA,IAAKuG,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,KAAjB,CAAA;AACA;;IACA,IAAKC,CAAAA,GAAL,GAAW,IAAX,CAAA;AACA;AACJ;AACA;;IACI,IAAKC,CAAAA,SAAL,GAAiB,IAAjB,CAAA;;IAEA,IAAI,CAAC,IAAKP,CAAAA,aAAV,EAAyB;AACvB;AACArP,MAAAA,IAAI,CAACD,OAAL,CAAasL,cAAb,GAA8B,KAA9B,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKwE,IAAL,GAAY,IAAIpH,WAAJ,CAAgB,IAAhB,CAAZ,CAAA;AACA,IAAA,IAAA,CAAKzF,UAAL,GAAkB,IAAIiJ,WAAJ,CAAgB,IAAhB,CAAlB,CAAA;AACA,IAAA,IAAA,CAAK6D,UAAL,GAAkB,IAAIrC,UAAJ,CAAe,IAAf,CAAlB,CAAA;AAEAzN,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,YAAR,EAAsB,MAAM;MAC1B/P,IAAI,CAACgQ,MAAL,CAAYrR,GAAZ,CACEqB,IAAI,CAACiQ,UADP,EAEE,OAFF;AAGE;AAA2B,MAAA,IAAA,CAAKC,QAAL,CAAcC,IAAd,CAAmB,IAAnB,CAH7B,CAAA,CAAA;;MAMA,IAAI,IAAA,CAAKhB,oBAAT,EAA+B;QAC7B,IAAKiB,CAAAA,WAAL,CAAiB,SAAjB,EAA4B,MAA5B,EAAoC,IAApC,EAA0C,QAA1C,CAAA,CAAA;AACD,OAFD,MAEO,IAAI,IAAKlB,CAAAA,kBAAT,EAA6B;QAClC,IAAKkB,CAAAA,WAAL,CAAiB,OAAjB,EAA0B,OAA1B,EAAmC,KAAnC,EAA0C,QAA1C,CAAA,CADkC;AAIlC;AACA;AAEA;AACA;AACA;AACA;;;QACA,IAAIpQ,IAAI,CAACiQ,UAAT,EAAqB;AACnBjQ,UAAAA,IAAI,CAACiQ,UAAL,CAAgBI,WAAhB,GAA8B,MAAM,EAApC,CAAA;;AACArQ,UAAAA,IAAI,CAACiQ,UAAL,CAAgBK,UAAhB,GAA6B,MAAM,EAAnC,CAAA;AACD,SAAA;AACF,OAfM,MAeA;AACL,QAAA,IAAA,CAAKF,WAAL,CAAiB,OAAjB,EAA0B,MAA1B,EAAkC,IAAlC,CAAA,CAAA;AACD,OAAA;KA1BH,CAAA,CAAA;AA4BD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACEA,WAAW,CAACG,IAAD,EAAOC,IAAP,EAAaC,EAAb,EAAiBC,MAAjB,EAAyB;IAClC,MAAM;AAAE1Q,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IACA,MAAM;AAAEgQ,MAAAA,MAAAA;AAAF,KAAA,GAAahQ,IAAnB,CAAA;IAEA,MAAM2Q,WAAW,GAAGD,MAAM,GAAGH,IAAI,GAAGG,MAAV,GAAmB,EAA7C,CAAA;IAEAV,MAAM,CAACrR,GAAP,CACEqB,IAAI,CAACiQ,UADP,EAEEM,IAAI,GAAGC,IAFT;AAGE;AAA2B,IAAA,IAAA,CAAKI,aAAL,CAAmBT,IAAnB,CAAwB,IAAxB,CAH7B,CAAA,CAAA;AAKAH,IAAAA,MAAM,CAACrR,GAAP,CAAWR,MAAX,EAAmBoS,IAAI,GAAG,MAA1B;AAAkC;AAA2B,IAAA,IAAA,CAAKM,aAAL,CAAmBV,IAAnB,CAAwB,IAAxB,CAA7D,CAAA,CAAA;AACAH,IAAAA,MAAM,CAACrR,GAAP,CAAWR,MAAX,EAAmBoS,IAAI,GAAGE,EAA1B;AAA8B;AAA2B,IAAA,IAAA,CAAKK,WAAL,CAAiBX,IAAjB,CAAsB,IAAtB,CAAzD,CAAA,CAAA;;AACA,IAAA,IAAIQ,WAAJ,EAAiB;AACfX,MAAAA,MAAM,CAACrR,GAAP,CACEqB,IAAI,CAACiQ,UADP,EAEEU,WAFF;AAGE;AAA2B,MAAA,IAAA,CAAKG,WAAL,CAAiBX,IAAjB,CAAsB,IAAtB,CAH7B,CAAA,CAAA;AAKD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;EACES,aAAa,CAAChU,CAAD,EAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,MAAMmU,cAAc,GAAGnU,CAAC,CAACiC,IAAF,KAAW,WAAX,IAA0BjC,CAAC,CAACoU,WAAF,KAAkB,OAAnE,CAPe;AAUf;AACA;;AACA,IAAA,IAAID,cAAc,IAAInU,CAAC,CAACC,MAAF,GAAW,CAAjC,EAAoC;AAClC,MAAA,OAAA;AACD,KAAA;;IAED,MAAM;AAAEmD,MAAAA,IAAAA;KAAS,GAAA,IAAjB,CAhBe;;AAmBf,IAAA,IAAI,CAACA,IAAI,CAAC4D,MAAL,CAAYC,MAAjB,EAAyB;AACvBjH,MAAAA,CAAC,CAACqU,cAAF,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIjR,IAAI,CAACyB,QAAL,CAAc,aAAd,EAA6B;AAAEkM,MAAAA,aAAa,EAAE/Q,CAAAA;KAA9C,CAAA,CAAmDyI,gBAAvD,EAAyE;AACvE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI0L,cAAJ,EAAoB;MAClB/Q,IAAI,CAACkR,aAAL,EAAA,CADkB;AAIlB;;AACA,MAAA,IAAA,CAAKC,6BAAL,CAAmCvU,CAAnC,EAAsC,MAAtC,CAAA,CAAA;AACD,KAAA;;IAEDoD,IAAI,CAACuG,UAAL,CAAgBsC,OAAhB,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAKuI,aAAL,CAAmBxU,CAAnB,EAAsB,MAAtB,CAAA,CAAA;;AAEA,IAAA,IAAI,IAAKoS,CAAAA,gBAAL,KAA0B,CAA9B,EAAiC;AAC/B,MAAA,IAAA,CAAKhG,QAAL,GAAgB,IAAhB,CAD+B;AAG/B;;AACAzP,MAAAA,cAAc,CAAC,IAAKgT,CAAAA,OAAN,EAAe,IAAA,CAAK/S,EAApB,CAAd,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKwV,CAAAA,gBAAL,GAAwB,CAA5B,EAA+B;AAC7B;AACA,MAAA,IAAA,CAAKqC,cAAL,EAAA,CAAA;;MACA,IAAKnI,CAAAA,YAAL,GAAoB,IAApB,CAAA;AACD,KAJD,MAIO;MACL,IAAKA,CAAAA,YAAL,GAAoB,KAApB,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;EACE2H,aAAa,CAACjU,CAAD,EAAI;AACf,IAAA,IAAA,CAAKuU,6BAAL,CAAmCvU,CAAnC,EAAsC,MAAtC,CAAA,CAAA;;IAEA,IAAI,CAAC,IAAKoS,CAAAA,gBAAV,EAA4B;AAC1B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKoC,aAAL,CAAmBxU,CAAnB,EAAsB,MAAtB,CAAA,CAAA;;AAEA,IAAA,IAAI,KAAKoD,IAAL,CAAUyB,QAAV,CAAmB,aAAnB,EAAkC;AAAEkM,MAAAA,aAAa,EAAE/Q,CAAAA;KAAnD,CAAA,CAAwDyI,gBAA5D,EAA8E;AAC5E,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK2J,gBAAL,KAA0B,CAA1B,IAA+B,CAAC,IAAA,CAAKS,UAAzC,EAAqD;MACnD,IAAI,CAAC,IAAKzG,CAAAA,QAAV,EAAoB;AAClB,QAAA,IAAA,CAAKsI,uBAAL,EAAA,CAAA;AACD,OAHkD;;;AAMnD,MAAA,IAAI,KAAKtI,QAAL,IAAiB,CAAC,IAAA,CAAKyG,UAA3B,EAAuC;QACrC,IAAI,IAAA,CAAKC,SAAT,EAAoB;UAClB,IAAKA,CAAAA,SAAL,GAAiB,KAAjB,CAAA;UACA,IAAK1M,CAAAA,UAAL,CAAgByG,GAAhB,EAAA,CAAA;AACD,SAAA;;QAED,IAAKgG,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;QACA,IAAK4B,CAAAA,cAAL,GAPqC;AASrC;;;AACA,QAAA,IAAA,CAAKE,kBAAL,EAAA,CAAA;;AACA,QAAA,IAAA,CAAKhC,aAAL,GAAqBiC,IAAI,CAACpE,GAAL,EAArB,CAXqC;;QAarC,IAAKoC,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;AACAjW,QAAAA,cAAc,CAAC,IAAKwV,CAAAA,WAAN,EAAmB,IAAA,CAAKvV,EAAxB,CAAd,CAAA;AACA,QAAA,IAAA,CAAKkQ,QAAL,CAAchQ,CAAd,GAAkB,CAAlB,CAAA;AACA,QAAA,IAAA,CAAKgQ,QAAL,CAAc/P,CAAd,GAAkB,CAAlB,CAAA;QACA,IAAKkW,CAAAA,IAAL,CAAUjH,KAAV,EAAA,CAAA;;AAEA,QAAA,IAAA,CAAK6I,YAAL,EAAA,CAAA;;AACA,QAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;AACD,OAAA;KA3BH,MA4BO,IAAI,IAAA,CAAK1C,gBAAL,GAAwB,CAAxB,IAA6B,CAAC,IAAKU,CAAAA,SAAvC,EAAkD;AACvD,MAAA,IAAA,CAAKiC,WAAL,EAAA,CAAA;;AAEA,MAAA,IAAA,CAAKjC,SAAL,GAAiB,IAAjB,CAHuD;;AAMvD,MAAA,IAAA,CAAK6B,kBAAL,EAAA,CAAA;;MAEA,IAAKvO,CAAAA,UAAL,CAAgB4F,KAAhB,EAAA,CAAA;;AAEA,MAAA,IAAA,CAAK6I,YAAL,EAAA,CAAA;;AACA,MAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEC,EAAAA,WAAW,GAAG;IACZ,IAAI,IAAA,CAAKlC,UAAT,EAAqB;AACnB,MAAA,IAAA,CAAKA,UAAL,GAAkB,KAAlB,CADmB;AAInB;;MACA,IAAI,CAAC,IAAKD,CAAAA,mBAAV,EAA+B;QAC7B,IAAKoC,CAAAA,eAAL,CAAqB,IAArB,CAAA,CAAA;AACD,OAAA;;MAED,IAAK/B,CAAAA,IAAL,CAAUpG,GAAV,EAAA,CAAA;MACA,IAAKT,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;EACE8H,WAAW,CAAClU,CAAD,EAAI;IACb,IAAI,CAAC,IAAKoS,CAAAA,gBAAV,EAA4B;AAC1B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKoC,aAAL,CAAmBxU,CAAnB,EAAsB,IAAtB,CAAA,CAAA;;AAEA,IAAA,IAAI,KAAKoD,IAAL,CAAUyB,QAAV,CAAmB,WAAnB,EAAgC;AAAEkM,MAAAA,aAAa,EAAE/Q,CAAAA;KAAjD,CAAA,CAAsDyI,gBAA1D,EAA4E;AAC1E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAK2J,CAAAA,gBAAL,KAA0B,CAA9B,EAAiC;AAC/B,MAAA,IAAA,CAAKyC,YAAL,EAAA,CAAA;;MAEA,IAAI,IAAA,CAAKhC,UAAT,EAAqB;AACnB,QAAA,IAAA,CAAKkC,WAAL,EAAA,CAAA;OADF,MAEO,IAAI,CAAC,IAAA,CAAKjC,SAAN,IAAmB,CAAC,IAAKxG,CAAAA,YAA7B,EAA2C;AAChD;QACA,IAAK2I,CAAAA,UAAL,CAAgBjV,CAAhB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAI,KAAKoS,gBAAL,GAAwB,CAAxB,IAA6B,IAAA,CAAKU,SAAtC,EAAiD;MAC/C,IAAKA,CAAAA,SAAL,GAAiB,KAAjB,CAAA;MACA,IAAK1M,CAAAA,UAAL,CAAgByG,GAAhB,EAAA,CAAA;;AAEA,MAAA,IAAI,IAAKuF,CAAAA,gBAAL,KAA0B,CAA9B,EAAiC;AAC/B;QACA,IAAKhG,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;AACA,QAAA,IAAA,CAAKuI,kBAAL,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEG,EAAAA,cAAc,GAAG;AACf,IAAA,IAAI,IAAKjC,CAAAA,UAAL,IAAmB,IAAA,CAAKC,SAA5B,EAAuC;AACrC,MAAA,IAAA,CAAKkC,eAAL,EAAA,CAAA;;MAEA,IAAI,IAAA,CAAKnC,UAAT,EAAqB;AACnB;QACA,IAAI,CAACpV,WAAW,CAAC,IAAA,CAAKb,EAAN,EAAU,IAAA,CAAKuP,MAAf,CAAhB,EAAwC;UACtC,IAAK8G,CAAAA,IAAL,CAAU/G,MAAV,EAAA,CAAA;AACD,SAAA;OAJH;AAKO;AAA0B,QAAA;AAC/B,UAAA,IAAI,CAACzO,WAAW,CAAC,KAAKb,EAAN,EAAU,KAAKuP,MAAf,CAAZ,IACG,CAAC1O,WAAW,CAAC,IAAKZ,CAAAA,EAAN,EAAU,IAAKoV,CAAAA,MAAf,CADnB,EAC2C;YACzC,IAAK7L,CAAAA,UAAL,CAAgB8F,MAAhB,EAAA,CAAA;AACD,WAAA;AACF,SAAA;;AAED,MAAA,IAAA,CAAKgJ,iBAAL,EAAA,CAAA;;MACA,IAAKnC,CAAAA,GAAL,GAAWoC,qBAAqB,CAAC,IAAA,CAAKL,cAAL,CAAoBvB,IAApB,CAAyB,IAAzB,CAAD,CAAhC,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEyB,eAAe,CAAClM,KAAD,EAAQ;AACrB,IAAA,MAAMsM,IAAI,GAAGR,IAAI,CAACpE,GAAL,EAAb,CAAA;AACA,IAAA,MAAMjS,QAAQ,GAAG6W,IAAI,GAAG,KAAKzC,aAA7B,CAAA;;AAEA,IAAA,IAAIpU,QAAQ,GAAG,EAAX,IAAiB,CAACuK,KAAtB,EAA6B;AAC3B,MAAA,OAAA;AACD,KAAA;;IAGD,IAAKgE,CAAAA,QAAL,CAAchQ,CAAd,GAAkB,IAAA,CAAKuY,YAAL,CAAkB,GAAlB,EAAuB9W,QAAvB,CAAlB,CAAA;IACA,IAAKuO,CAAAA,QAAL,CAAc/P,CAAd,GAAkB,IAAA,CAAKsY,YAAL,CAAkB,GAAlB,EAAuB9W,QAAvB,CAAlB,CAAA;IAEA,IAAKoU,CAAAA,aAAL,GAAqByC,IAArB,CAAA;AACAzY,IAAAA,cAAc,CAAC,IAAKwV,CAAAA,WAAN,EAAmB,IAAA,CAAKvV,EAAxB,CAAd,CAAA;IACA,IAAKgW,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;EACEqC,UAAU,CAACjV,CAAD,EAAI;IACZ,MAAM;AAAEuI,MAAAA,UAAAA;KAAe,GAAA,IAAA,CAAKnF,IAA5B,CADY;;AAIZ,IAAA,IAAImF,UAAU,CAACC,SAAX,EAAJ,EAA4B;AAC1B;AACA;AACAD,MAAAA,UAAU,CAAC4E,WAAX,CAAuB,CAAvB,EAA0B,IAA1B,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KATW;;;IAYZ,IAAInN,CAAC,CAACiC,IAAF,CAAOqT,OAAP,CAAe,QAAf,CAA2B,GAAA,CAA/B,EAAkC;AAChC,MAAA,OAAA;AACD,KAdW;;;IAiBZ,IAAItV,CAAC,CAACiC,IAAF,KAAW,SAAX,IAAwBjC,CAAC,CAACoU,WAAF,KAAkB,OAA9C,EAAuD;AACrD,MAAA,IAAA,CAAKlB,UAAL,CAAgBpC,KAAhB,CAAsB,IAAKnB,CAAAA,OAA3B,EAAoC3P,CAApC,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KApBW;;;AAuBZ,IAAA,MAAMuV,QAAQ,GAAG,IAAKnS,CAAAA,IAAL,CAAUD,OAAV,CAAkBqS,eAAlB,GAAoC1D,gBAApC,GAAuD,CAAxE,CAvBY;AA0BZ;AACA;;IACA,IAAI,IAAA,CAAKkB,SAAT,EAAoB;MAClB,IAAKyB,CAAAA,cAAL,GADkB;;;MAGlB,IAAInX,kBAAkB,CAAC,IAAA,CAAK4U,YAAN,EAAoB,KAAKvC,OAAzB,CAAlB,GAAsDoC,gBAA1D,EAA4E;AAC1E,QAAA,IAAA,CAAKmB,UAAL,CAAgB3B,SAAhB,CAA0B,IAAK5B,CAAAA,OAA/B,EAAwC3P,CAAxC,CAAA,CAAA;AACD,OAAA;AACF,KAND,MAMO;AACLrD,MAAAA,cAAc,CAAC,IAAKuV,CAAAA,YAAN,EAAoB,IAAA,CAAKvC,OAAzB,CAAd,CAAA;AACA,MAAA,IAAA,CAAKqD,SAAL,GAAiByC,UAAU,CAAC,MAAM;AAChC,QAAA,IAAA,CAAKvC,UAAL,CAAgB5B,GAAhB,CAAoB,IAAK3B,CAAAA,OAAzB,EAAkC3P,CAAlC,CAAA,CAAA;;AACA,QAAA,IAAA,CAAKyU,cAAL,EAAA,CAAA;OAFyB,EAGxBc,QAHwB,CAA3B,CAAA;AAID,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEd,EAAAA,cAAc,GAAG;IACf,IAAI,IAAA,CAAKzB,SAAT,EAAoB;MAClB0C,YAAY,CAAC,IAAK1C,CAAAA,SAAN,CAAZ,CAAA;MACA,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEqC,EAAAA,YAAY,CAACvQ,IAAD,EAAOvG,QAAP,EAAiB;AAC3B;IACA,MAAMoX,YAAY,GAAG,IAAA,CAAK/Y,EAAL,CAAQkI,IAAR,CAAA,GAAgB,IAAKqN,CAAAA,WAAL,CAAiBrN,IAAjB,CAArC,CAAA;;IAEA,IAAI1H,IAAI,CAACG,GAAL,CAASoY,YAAT,CAAyB,GAAA,CAAzB,IAA8BpX,QAAQ,GAAG,CAA7C,EAAgD;MAC9C,OAAOoX,YAAY,GAAGpX,QAAtB,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEsW,EAAAA,YAAY,GAAG;IACb,IAAI,IAAA,CAAK9B,GAAT,EAAc;MACZ6C,oBAAoB,CAAC,IAAK7C,CAAAA,GAAN,CAApB,CAAA;MACA,IAAKA,CAAAA,GAAL,GAAW,IAAX,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEwB,EAAAA,6BAA6B,CAACvU,CAAD,EAAIoU,WAAJ,EAAiB;AAC5C,IAAA,MAAMyB,mBAAmB,GAAG,IAAKzS,CAAAA,IAAL,CAAU0S,YAAV,CAAuB,qBAAvB,EAA8C,IAA9C,EAAoD9V,CAApD,EAAuDoU,WAAvD,CAA5B,CAAA;;AACA,IAAA,IAAIyB,mBAAJ,EAAyB;AACvB7V,MAAAA,CAAC,CAACqU,cAAF,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEG,EAAAA,aAAa,CAACxU,CAAD,EAAIoU,WAAJ,EAAiB;IAC5B,IAAI,IAAA,CAAK7B,oBAAT,EAA+B;AAC7B,MAAA,MAAMwD,YAAY;AAAG;AAA6B/V,MAAAA,CAAlD,CAD6B;;MAG7B,MAAMgW,YAAY,GAAG,IAAK3D,CAAAA,gBAAL,CAAsB4D,SAAtB,CAAiCC,cAAD,IAAoB;AACvE,QAAA,OAAOA,cAAc,CAAClZ,EAAf,KAAsB+Y,YAAY,CAACI,SAA1C,CAAA;AACD,OAFoB,CAArB,CAAA;;MAIA,IAAI/B,WAAW,KAAK,IAAhB,IAAwB4B,YAAY,GAAG,CAAC,CAA5C,EAA+C;AAC7C;AACA,QAAA,IAAA,CAAK3D,gBAAL,CAAsB+D,MAAtB,CAA6BJ,YAA7B,EAA2C,CAA3C,CAAA,CAAA;OAFF,MAGO,IAAI5B,WAAW,KAAK,MAAhB,IAA0B4B,YAAY,KAAK,CAAC,CAAhD,EAAmD;AACxD;QACA,IAAK3D,CAAAA,gBAAL,CAAsBrP,IAAtB,CAA2B,KAAKqT,uBAAL,CAA6BN,YAA7B,EAA2C;AAAEjZ,UAAAA,CAAC,EAAE,CAAL;AAAQC,UAAAA,CAAC,EAAE,CAAA;AAAX,SAA3C,CAA3B,CAAA,CAAA;AACD,OAHM,MAGA,IAAIiZ,YAAY,GAAG,CAAC,CAApB,EAAuB;AAC5B;QACA,IAAKK,CAAAA,uBAAL,CAA6BN,YAA7B,EAA2C,KAAK1D,gBAAL,CAAsB2D,YAAtB,CAA3C,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAK5D,gBAAL,GAAwB,IAAA,CAAKC,gBAAL,CAAsBiE,MAA9C,CAlB6B;AAqB7B;;AACA,MAAA,IAAI,IAAKlE,CAAAA,gBAAL,GAAwB,CAA5B,EAA+B;QAC7BzV,cAAc,CAAC,KAAKC,EAAN,EAAU,KAAKyV,gBAAL,CAAsB,CAAtB,CAAV,CAAd,CAAA;AACD,OAAA;;AAED,MAAA,IAAI,IAAKD,CAAAA,gBAAL,GAAwB,CAA5B,EAA+B;QAC7BzV,cAAc,CAAC,KAAKE,EAAN,EAAU,KAAKwV,gBAAL,CAAsB,CAAtB,CAAV,CAAd,CAAA;AACD,OAAA;AACF,KA7BD,MA6BO;AACL,MAAA,MAAMkE,UAAU;AAAG;MAA2BvW,CAA9C,CAAA;MAEA,IAAKoS,CAAAA,gBAAL,GAAwB,CAAxB,CAAA;;MACA,IAAImE,UAAU,CAACtU,IAAX,CAAgBqT,OAAhB,CAAwB,OAAxB,CAAA,GAAmC,CAAC,CAAxC,EAA2C;AACzC;AACA;QACA,IAAIiB,UAAU,CAACC,OAAX,IAAsBD,UAAU,CAACC,OAAX,CAAmBF,MAAnB,GAA4B,CAAtD,EAAyD;UACvD,IAAKD,CAAAA,uBAAL,CAA6BE,UAAU,CAACC,OAAX,CAAmB,CAAnB,CAA7B,EAAoD,IAAA,CAAK5Z,EAAzD,CAAA,CAAA;;AACA,UAAA,IAAA,CAAKwV,gBAAL,EAAA,CAAA;;AACA,UAAA,IAAImE,UAAU,CAACC,OAAX,CAAmBF,MAAnB,GAA4B,CAAhC,EAAmC;YACjC,IAAKD,CAAAA,uBAAL,CAA6BE,UAAU,CAACC,OAAX,CAAmB,CAAnB,CAA7B,EAAoD,IAAA,CAAK3Z,EAAzD,CAAA,CAAA;;AACA,YAAA,IAAA,CAAKuV,gBAAL,EAAA,CAAA;AACD,WAAA;AACF,SAAA;AACF,OAXD,MAWO;AACL;AACA,QAAA,IAAA,CAAKiE,uBAAL;AAA6B;QAA6BrW,CAA1D,EAA8D,KAAKpD,EAAnE,CAAA,CAAA;;QACA,IAAIwX,WAAW,KAAK,IAApB,EAA0B;AACxB;UACA,IAAKhC,CAAAA,gBAAL,GAAwB,CAAxB,CAAA;AACD,SAHD,MAGO;AACL,UAAA,IAAA,CAAKA,gBAAL,EAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE8C,EAAAA,iBAAiB,GAAG;AAClBvY,IAAAA,cAAc,CAAC,IAAKwP,CAAAA,MAAN,EAAc,IAAA,CAAKvP,EAAnB,CAAd,CAAA;AACAD,IAAAA,cAAc,CAAC,IAAKsV,CAAAA,MAAN,EAAc,IAAA,CAAKpV,EAAnB,CAAd,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACE8X,EAAAA,kBAAkB,GAAG;AACnBhY,IAAAA,cAAc,CAAC,IAAKgT,CAAAA,OAAN,EAAe,IAAA,CAAK/S,EAApB,CAAd,CAAA;AACAD,IAAAA,cAAc,CAAC,IAAKiT,CAAAA,OAAN,EAAe,IAAA,CAAK/S,EAApB,CAAd,CAAA;;AACA,IAAA,IAAA,CAAKqY,iBAAL,EAAA,CAAA;AACD,GAAA;AAED;;;AACAR,EAAAA,uBAAuB,GAAG;AACxB,IAAA,IAAI,KAAKtR,IAAL,CAAUmF,UAAV,CAAqBC,SAArB,EAAJ,EAAsC;AACpC;MACA,IAAK4D,CAAAA,QAAL,GAAgB,GAAhB,CAAA;AACD,KAHD,MAGO;AACL;AACA,MAAA,MAAMqK,IAAI,GAAGrZ,IAAI,CAACG,GAAL,CAAS,IAAKX,CAAAA,EAAL,CAAQE,CAAR,GAAY,IAAA,CAAK6S,OAAL,CAAa7S,CAAlC,CAAA,GAAuCM,IAAI,CAACG,GAAL,CAAS,IAAKX,CAAAA,EAAL,CAAQG,CAAR,GAAY,IAAA,CAAK4S,OAAL,CAAa5S,CAAlC,CAApD,CAAA;;MAEA,IAAI0Z,IAAI,KAAK,CAAb,EAAgB;AACd;QACA,MAAMC,WAAW,GAAGD,IAAI,GAAG,CAAP,GAAW,GAAX,GAAiB,GAArC,CAAA;;AAEA,QAAA,IAAIrZ,IAAI,CAACG,GAAL,CAAS,IAAA,CAAKX,EAAL,CAAQ8Z,WAAR,CAAuB,GAAA,IAAA,CAAK/G,OAAL,CAAa+G,WAAb,CAAhC,CAAA,IAA8D7E,qBAAlE,EAAyF;UACvF,IAAKzF,CAAAA,QAAL,GAAgBsK,WAAhB,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEL,EAAAA,uBAAuB,CAACrW,CAAD,EAAI7C,CAAJ,EAAO;AAC5BA,IAAAA,CAAC,CAACL,CAAF,GAAMkD,CAAC,CAAC2W,KAAF,GAAU,IAAA,CAAKvT,IAAL,CAAUwT,MAAV,CAAiB9Z,CAAjC,CAAA;AACAK,IAAAA,CAAC,CAACJ,CAAF,GAAMiD,CAAC,CAAC6W,KAAF,GAAU,IAAA,CAAKzT,IAAL,CAAUwT,MAAV,CAAiB7Z,CAAjC,CAAA;;IAEA,IAAI,WAAA,IAAeiD,CAAnB,EAAsB;AACpB7C,MAAAA,CAAC,CAACH,EAAF,GAAOgD,CAAC,CAACmW,SAAT,CAAA;AACD,KAFD,MAEO,IAAInW,CAAC,CAAC8W,UAAF,KAAiB7Z,SAArB,EAAgC;AACrCE,MAAAA,CAAC,CAACH,EAAF,GAAOgD,CAAC,CAAC8W,UAAT,CAAA;AACD,KAAA;;AAED,IAAA,OAAO3Z,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;EACEmW,QAAQ,CAACtT,CAAD,EAAI;AACV;AACA,IAAA,IAAI,KAAKoD,IAAL,CAAUmF,UAAV,CAAqBC,SAArB,EAAJ,EAAsC;AACpCxI,MAAAA,CAAC,CAACqU,cAAF,EAAA,CAAA;AACArU,MAAAA,CAAC,CAAC+W,eAAF,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAxkBY;;ACrBf;;AACA;;AAEA;;AAEA,MAAMC,wBAAwB,GAAG,IAAjC;AAIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,UAAN,CAAiB;AACf;AACF;AACA;EACEpV,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKtG,CAAAA,CAAL,GAAS,CAAT,CAAA;IACA,IAAKoa,CAAAA,UAAL,GAAkB,CAAlB,CAAA;AACA;;IACA,IAAKC,CAAAA,kBAAL,GAA0B,CAA1B,CAAA;AACA;;IACA,IAAKC,CAAAA,kBAAL,GAA0B,CAA1B,CAAA;AACA;;IACA,IAAKC,CAAAA,oBAAL,GAA4B,CAAC,CAA7B,CAAA;AAEA;;IACA,IAAKC,CAAAA,WAAL,GAAmB,EAAnB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACE1O,MAAM,CAAC2O,YAAD,EAAe;IACnB,MAAM;AAAEnU,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IACA,MAAMoU,aAAa,GAAGpa,IAAI,CAACC,KAAL,CACpB+F,IAAI,CAACO,YAAL,CAAkB7G,CAAlB,GAAsBsG,IAAI,CAACO,YAAL,CAAkB7G,CAAlB,GAAsBsG,IAAI,CAACD,OAAL,CAAasU,OADrC,CAAtB,CAFmB;AAMnB;AACA;;AACA,IAAA,MAAMC,iBAAiB,GAAIF,aAAa,KAAK,KAAKN,UAAlD,CAAA;;AAEA,IAAA,IAAIQ,iBAAJ,EAAuB;MACrB,IAAKR,CAAAA,UAAL,GAAkBM,aAAlB,CAAA;AACA,MAAA,IAAA,CAAKjJ,MAAL,CAAY,IAAKtB,CAAAA,aAAL,EAAZ,CAAA,CAAA;AACD,KAAA;;IAED,IAAKqK,CAAAA,WAAL,CAAiB/U,OAAjB,CAAyB,CAACoV,UAAD,EAAa9T,KAAb,KAAuB;AAC9C,MAAA,IAAI6T,iBAAJ,EAAuB;AACrBzZ,QAAAA,YAAY,CAAC0Z,UAAU,CAACnb,EAAZ,EAAgB,CAACqH,KAAK,GAAG,IAAKwT,CAAAA,oBAAd,IACE,IAAA,CAAKH,UADvB,CAAZ,CAAA;AAED,OAAA;;AAED,MAAA,IAAIK,YAAY,IAAII,UAAU,CAACpT,KAA/B,EAAsC;QACpCoT,UAAU,CAACpT,KAAX,CAAiBqE,MAAjB,EAAA,CAAA;AACD,OAAA;KARH,CAAA,CAAA;AAUD,GAAA;AAED;AACF;AACA;;;AACEgP,EAAAA,aAAa,GAAG;AACd;AACA;IACA,IAAKT,CAAAA,kBAAL,GAA0B,CAA1B,CAAA;AACA,IAAA,IAAA,CAAKC,kBAAL,GAA0B,CAA1B,CAJc;;AAOd,IAAA,IAAA,CAAKF,UAAL,GAAkB,CAAlB,CAPc;;IAUd,IAAKG,CAAAA,oBAAL,GAA4B,CAAC,CAA7B,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEQ,EAAAA,aAAa,GAAG;AACd,IAAA,IAAA,CAAKP,WAAL,GAAmB,EAAnB,CADc;AAId;;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AAC1B,MAAA,MAAMtb,EAAE,GAAGJ,aAAa,CAAC,YAAD,EAAe,KAAf,EAAsB,IAAKgH,CAAAA,IAAL,CAAUiE,SAAhC,CAAxB,CAAA;AACA7K,MAAAA,EAAE,CAACub,YAAH,CAAgB,MAAhB,EAAwB,OAAxB,CAAA,CAAA;AACAvb,MAAAA,EAAE,CAACub,YAAH,CAAgB,sBAAhB,EAAwC,OAAxC,CAAA,CAAA;AACAvb,MAAAA,EAAE,CAACub,YAAH,CAAgB,aAAhB,EAA+B,MAA/B,EAJ0B;;MAO1Bvb,EAAE,CAAC0B,KAAH,CAAS8Z,OAAT,GAAoBF,CAAC,KAAK,CAAP,GAAY,OAAZ,GAAsB,MAAzC,CAAA;MAEA,IAAKR,CAAAA,WAAL,CAAiBtU,IAAjB,CAAsB;AACpBxG,QAAAA,EADoB;;OAAtB,CAAA,CAAA;AAID,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;AACEyb,EAAAA,WAAW,GAAG;AACZ,IAAA,OAAO,IAAK7U,CAAAA,IAAL,CAAU8U,WAAV,KAA0B,CAAjC,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE/K,EAAAA,WAAW,CAACsJ,IAAD,EAAO0B,OAAP,EAAgBC,SAAhB,EAA2B;IACpC,MAAM;AAAEhV,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;AACA,IAAA,IAAIiV,QAAQ,GAAGjV,IAAI,CAACkV,cAAL,GAAsB7B,IAArC,CAAA;AACA,IAAA,MAAM8B,SAAS,GAAGnV,IAAI,CAAC8U,WAAL,EAAlB,CAAA;;AAEA,IAAA,IAAI9U,IAAI,CAACoV,OAAL,EAAJ,EAAoB;AAClBH,MAAAA,QAAQ,GAAGjV,IAAI,CAACqV,cAAL,CAAoBJ,QAApB,CAAX,CAAA;AACA,MAAA,MAAMK,QAAQ,GAAG,CAACjC,IAAI,GAAG8B,SAAR,IAAqBA,SAAtC,CAAA;;AACA,MAAA,IAAIG,QAAQ,IAAIH,SAAS,GAAG,CAA5B,EAA+B;AAC7B;AACA9B,QAAAA,IAAI,GAAGiC,QAAP,CAAA;AACD,OAHD,MAGO;AACL;QACAjC,IAAI,GAAGiC,QAAQ,GAAGH,SAAlB,CAAA;AACD,OAAA;AACF,KAVD,MAUO;MACL,IAAIF,QAAQ,GAAG,CAAf,EAAkB;AAChBA,QAAAA,QAAQ,GAAG,CAAX,CAAA;AACD,OAFD,MAEO,IAAIA,QAAQ,IAAIE,SAAhB,EAA2B;QAChCF,QAAQ,GAAGE,SAAS,GAAG,CAAvB,CAAA;AACD,OAAA;;AACD9B,MAAAA,IAAI,GAAG4B,QAAQ,GAAGjV,IAAI,CAACkV,cAAvB,CAAA;AACD,KAAA;;IAEDlV,IAAI,CAACkV,cAAL,GAAsBD,QAAtB,CAAA;IACA,IAAKlB,CAAAA,kBAAL,IAA2BV,IAA3B,CAAA;IAEArT,IAAI,CAACuG,UAAL,CAAgBgP,cAAhB,EAAA,CAAA;AAEA,IAAA,MAAMC,YAAY,GAAG,IAAK3L,CAAAA,aAAL,EAArB,CAAA;;IACA,IAAI,CAACkL,OAAL,EAAc;MACZ,IAAK5J,CAAAA,MAAL,CAAYqK,YAAZ,CAAA,CAAA;AACA,MAAA,IAAA,CAAKC,cAAL,EAAA,CAAA;AACD,KAHD,MAGO;AACLzV,MAAAA,IAAI,CAACuG,UAAL,CAAgBqE,WAAhB,CAA4B;AAC1B8K,QAAAA,YAAY,EAAE,IADY;QAE1B9M,KAAK,EAAE,KAAKlP,CAFc;AAG1B+P,QAAAA,GAAG,EAAE+L,YAHqB;QAI1B9L,QAAQ,EAAEsL,SAAS,IAAI,CAJG;AAK1B7H,QAAAA,gBAAgB,EAAE,EALQ;AAM1B1C,QAAAA,YAAY,EAAE,CANY;AAMT;QACjBI,QAAQ,EAAGnR,CAAD,IAAO;UACf,IAAKyR,CAAAA,MAAL,CAAYzR,CAAZ,CAAA,CAAA;SARwB;AAU1BwN,QAAAA,UAAU,EAAE,MAAM;AAChB,UAAA,IAAA,CAAKuO,cAAL,EAAA,CAAA;AACAzV,UAAAA,IAAI,CAAC+E,WAAL,EAAA,CAAA;AACD,SAAA;OAbH,CAAA,CAAA;MAgBA,IAAI4Q,QAAQ,GAAG3V,IAAI,CAACkV,cAAL,GAAsBlV,IAAI,CAACwD,SAA1C,CAAA;;AACA,MAAA,IAAIxD,IAAI,CAACoV,OAAL,EAAJ,EAAoB;AAClB,QAAA,MAAMQ,YAAY,GAAG,CAACD,QAAQ,GAAGR,SAAZ,IAAyBA,SAA9C,CAAA;;AACA,QAAA,IAAIS,YAAY,IAAIT,SAAS,GAAG,CAAhC,EAAmC;AACjC;AACAQ,UAAAA,QAAQ,GAAGC,YAAX,CAAA;AACD,SAHD,MAGO;AACL;UACAD,QAAQ,GAAGC,YAAY,GAAGT,SAA1B,CAAA;AACD,SAAA;AACF,OA3BI;AA8BL;;;AACA,MAAA,IAAInb,IAAI,CAACG,GAAL,CAASwb,QAAT,CAAA,GAAqB,CAAzB,EAA4B;AAC1B,QAAA,IAAA,CAAKF,cAAL,EAAA,CAAA;AACD,OAAA;AACF,KAAA;;IAED,OAAO5N,OAAO,CAACwL,IAAD,CAAd,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACExJ,EAAAA,aAAa,GAAG;AACd,IAAA,OAAO,IAAKiK,CAAAA,UAAL,GAAkB,IAAA,CAAKC,kBAA9B,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACE3O,EAAAA,SAAS,GAAG;AACV,IAAA,OAAO,IAAK1L,CAAAA,CAAL,KAAW,IAAA,CAAKmQ,aAAL,EAAlB,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACE4L,EAAAA,cAAc,GAAG;AAAA,IAAA,IAAA,kBAAA,CAAA;;IACf,MAAM;AAAEzV,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;AACA,IAAA,MAAM6V,kBAAkB,GAAG,IAAA,CAAK7B,kBAAL,GAA0B,KAAKD,kBAA1D,CAAA;;IAEA,IAAI,CAAC8B,kBAAL,EAAyB;AACvB,MAAA,OAAA;AACD,KAAA;;IAED,IAAK7B,CAAAA,kBAAL,GAA0B,IAAA,CAAKD,kBAA/B,CAAA;AAEA/T,IAAAA,IAAI,CAACwD,SAAL,GAAiBxD,IAAI,CAACkV,cAAtB,CAAA;AAEA,IAAA,IAAIY,OAAO,GAAG9b,IAAI,CAACG,GAAL,CAAS0b,kBAAT,CAAd,CAAA;AACA;;AACA,IAAA,IAAIE,UAAJ,CAAA;;IAEA,IAAID,OAAO,IAAI,CAAf,EAAkB;AAChB,MAAA,IAAA,CAAK7B,oBAAL,IAA6B4B,kBAAkB,IAAIA,kBAAkB,GAAG,CAArB,GAAyB,CAAC,CAA1B,GAA8B,CAAlC,CAA/C,CAAA;MACAC,OAAO,GAAG,CAAV,CAFgB;;AAKhB,MAAA,IAAA,CAAK5B,WAAL,CAAiB/U,OAAjB,CAA0BoV,UAAD,IAAgB;AAAA,QAAA,IAAA,iBAAA,CAAA;;AACvC,QAAA,CAAA,iBAAA,GAAAA,UAAU,CAACpT,KAAX,MAAA,IAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,IAAA,iBAAA,CAAkBmE,OAAlB,EAAA,CAAA;QACAiP,UAAU,CAACpT,KAAX,GAAmBtH,SAAnB,CAAA;OAFF,CAAA,CAAA;AAID,KAAA;;IAED,KAAK,IAAI6a,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,OAApB,EAA6BpB,CAAC,EAA9B,EAAkC;MAChC,IAAImB,kBAAkB,GAAG,CAAzB,EAA4B;AAC1BE,QAAAA,UAAU,GAAG,IAAA,CAAK7B,WAAL,CAAiB8B,KAAjB,EAAb,CAAA;;AACA,QAAA,IAAID,UAAJ,EAAgB;AACd,UAAA,IAAA,CAAK7B,WAAL,CAAiB,CAAjB,CAAsB6B,GAAAA,UAAtB,CADc;;AAGd,UAAA,IAAA,CAAK9B,oBAAL,EAAA,CAAA;AAEApZ,UAAAA,YAAY,CAACkb,UAAU,CAAC3c,EAAZ,EAAgB,CAAC,IAAK6a,CAAAA,oBAAL,GAA4B,CAA7B,IAAkC,IAAA,CAAKH,UAAvD,CAAZ,CAAA;AAEA9T,UAAAA,IAAI,CAACiW,UAAL,CAAgBF,UAAhB,EAA6B/V,IAAI,CAACwD,SAAL,GAAiBsS,OAAlB,GAA6BpB,CAA7B,GAAiC,CAA7D,CAAA,CAAA;AACD,SAAA;AACF,OAXD,MAWO;AACLqB,QAAAA,UAAU,GAAG,IAAA,CAAK7B,WAAL,CAAiBgC,GAAjB,EAAb,CAAA;;AACA,QAAA,IAAIH,UAAJ,EAAgB;AACd,UAAA,IAAA,CAAK7B,WAAL,CAAiBiC,OAAjB,CAAyBJ,UAAzB,EADc;;AAGd,UAAA,IAAA,CAAK9B,oBAAL,EAAA,CAAA;UAEApZ,YAAY,CAACkb,UAAU,CAAC3c,EAAZ,EAAgB,KAAK6a,oBAAL,GAA4B,IAAKH,CAAAA,UAAjD,CAAZ,CAAA;AAEA9T,UAAAA,IAAI,CAACiW,UAAL,CAAgBF,UAAhB,EAA6B/V,IAAI,CAACwD,SAAL,GAAiBsS,OAAlB,GAA6BpB,CAA7B,GAAiC,CAA7D,CAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAnDc;AAsDf;AACA;AACA;AACA;AACA;;;AACA,IAAA,IAAI1a,IAAI,CAACG,GAAL,CAAS,IAAK8Z,CAAAA,oBAAd,CAAsC,GAAA,EAAtC,IAA4C,CAAC,IAAK7O,CAAAA,SAAL,EAAjD,EAAmE;AACjE,MAAA,IAAA,CAAKoP,aAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAKhP,MAAL,EAAA,CAAA;AACD,KA9Dc;;;IAiEfxF,IAAI,CAACuG,UAAL,CAAgBC,UAAhB,EAAA,CAAA;IAEA,IAAK0N,CAAAA,WAAL,CAAiB/U,OAAjB,CAAyB,CAACoV,UAAD,EAAaG,CAAb,KAAmB;MAC1C,IAAIH,UAAU,CAACpT,KAAf,EAAsB;AACpB;AACAoT,QAAAA,UAAU,CAACpT,KAAX,CAAiBoD,WAAjB,CAA6BmQ,CAAC,KAAK,CAAnC,CAAA,CAAA;AACD,OAAA;KAJH,CAAA,CAAA;IAOA1U,IAAI,CAAC+H,SAAL,GAAA,CAAA,kBAAA,GAAiB,IAAKmM,CAAAA,WAAL,CAAiB,CAAjB,CAAjB,MAAiB,IAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,CAAqB/S,KAAtC,CAAA;AACAnB,IAAAA,IAAI,CAAC+D,aAAL,CAAmBqS,UAAnB,CAA8BP,kBAA9B,CAAA,CAAA;;IAEA,IAAI7V,IAAI,CAAC+H,SAAT,EAAoB;MAClB/H,IAAI,CAAC+H,SAAL,CAAe9C,mBAAf,EAAA,CAAA;AACD,KAAA;;IAEDjF,IAAI,CAACyB,QAAL,CAAc,QAAd,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE0J,EAAAA,MAAM,CAACzR,CAAD,EAAI2c,QAAJ,EAAc;IAClB,IAAI,CAAC,KAAKrW,IAAL,CAAUoV,OAAV,EAAD,IAAwBiB,QAA5B,EAAsC;AACpC;AACA,MAAA,IAAIC,mBAAmB,GAAG,CAAE,IAAA,CAAKxC,UAAL,GAAkB,IAAKC,CAAAA,kBAAxB,GAA8Cra,CAA/C,IAAoD,IAAA,CAAKoa,UAAnF,CAAA;AACAwC,MAAAA,mBAAmB,IAAI,IAAA,CAAKtW,IAAL,CAAUwD,SAAjC,CAAA;MACA,MAAMyH,KAAK,GAAGjR,IAAI,CAACC,KAAL,CAAWP,CAAC,GAAG,IAAKA,CAAAA,CAApB,CAAd,CAAA;;MAEA,IAAK4c,mBAAmB,GAAG,CAAtB,IAA2BrL,KAAK,GAAG,CAApC,IACIqL,mBAAmB,IAAI,KAAKtW,IAAL,CAAU8U,WAAV,EAA0B,GAAA,CAAjD,IAAsD7J,KAAK,GAAG,CADtE,EAC0E;AACxEvR,QAAAA,CAAC,GAAG,IAAKA,CAAAA,CAAL,GAAUuR,KAAK,GAAG2I,wBAAtB,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAKla,CAAAA,CAAL,GAASA,CAAT,CAAA;;AAEA,IAAA,IAAI,IAAKsG,CAAAA,IAAL,CAAUiE,SAAd,EAAyB;AACvBpJ,MAAAA,YAAY,CAAC,IAAKmF,CAAAA,IAAL,CAAUiE,SAAX,EAAsBvK,CAAtB,CAAZ,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKsG,IAAL,CAAUyB,QAAV,CAAmB,gBAAnB,EAAqC;MAAE/H,CAAF;AAAK2c,MAAAA,QAAQ,EAAEA,QAAF,KAAA,IAAA,IAAEA,QAAF,KAAA,KAAA,CAAA,GAAEA,QAAF,GAAc,KAAA;KAAhE,CAAA,CAAA;AACD,GAAA;;AA/Tc;;ACrBjB;;AAEA;AACA;AACA;AACA;;AAEA,MAAME,mBAAmB,GAAG;AAC1BC,EAAAA,MAAM,EAAE,EADkB;AAE1BC,EAAAA,CAAC,EAAE,EAFuB;AAG1BC,EAAAA,SAAS,EAAE,EAHe;AAI1BC,EAAAA,OAAO,EAAE,EAJiB;AAK1BC,EAAAA,UAAU,EAAE,EALc;AAM1BC,EAAAA,SAAS,EAAE,EANe;AAO1BC,EAAAA,GAAG,EAAE,CAAA;AAPqB,CAA5B,CAAA;AAUA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG,CAACC,GAAD,EAAMC,cAAN,KAAyB;AACnD,EAAA,OAAOA,cAAc,GAAGD,GAAH,GAAST,mBAAmB,CAACS,GAAD,CAAjD,CAAA;AACD,CAFD,CAAA;AAIA;AACA;AACA;AACA;;;AACA,MAAME,QAAN,CAAe;AACb;AACF;AACA;EACEzY,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;AACA;;IACA,IAAKmX,CAAAA,WAAL,GAAmB,KAAnB,CAAA;AAEAnX,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,YAAR,EAAsB,MAAM;AAC1B,MAAA,IAAI/P,IAAI,CAACD,OAAL,CAAaqX,SAAjB,EAA4B;AAC1B;AACA,QAAA,IAAI,CAACpX,IAAI,CAACD,OAAL,CAAasX,iBAAlB,EAAqC;AACnC;AACA;AACA;AACA,UAAA,IAAA,CAAKC,UAAL,EAAA,CAAA;AACD,SAAA;;AAEDtX,QAAAA,IAAI,CAACgQ,MAAL,CAAYrR,GAAZ,CACEtF,QADF,EAEE,SAFF;AAGE;AAA2B,QAAA,IAAA,CAAKke,UAAL,CAAgBpH,IAAhB,CAAqB,IAArB,CAH7B,CAAA,CAAA;AAKD,OAAA;;AAEDnQ,MAAAA,IAAI,CAACgQ,MAAL,CAAYrR,GAAZ,CAAgBtF,QAAhB,EAA0B,SAA1B;AAAqC;AAA2B,MAAA,IAAA,CAAKme,UAAL,CAAgBrH,IAAhB,CAAqB,IAArB,CAAhE,CAAA,CAAA;KAjBF,CAAA,CAAA;AAoBA,IAAA,MAAMsH,iBAAiB;AAAG;AAA4Bpe,IAAAA,QAAQ,CAACqe,aAA/D,CAAA;AACA1X,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,SAAR,EAAmB,MAAM;MACvB,IAAI/P,IAAI,CAACD,OAAL,CAAa4X,WAAb,IACGF,iBADH,IAEG,IAAKN,CAAAA,WAFZ,EAEyB;AACvBM,QAAAA,iBAAiB,CAACG,KAAlB,EAAA,CAAA;AACD,OAAA;KALH,CAAA,CAAA;AAOD,GAAA;AAED;;;AACAN,EAAAA,UAAU,GAAG;IACX,IAAI,CAAC,KAAKH,WAAN,IAAqB,KAAKnX,IAAL,CAAUgG,OAAnC,EAA4C;AAC1C,MAAA,IAAA,CAAKhG,IAAL,CAAUgG,OAAV,CAAkB4R,KAAlB,EAAA,CAAA;MACA,IAAKT,CAAAA,WAAL,GAAmB,IAAnB,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;EACEK,UAAU,CAAC5a,CAAD,EAAI;IACZ,MAAM;AAAEoD,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;;AAEA,IAAA,IAAIA,IAAI,CAACyB,QAAL,CAAc,SAAd,EAAyB;AAAEkM,MAAAA,aAAa,EAAE/Q,CAAAA;KAA1C,CAAA,CAA+CyI,gBAAnD,EAAqE;AACnE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI1I,cAAc,CAACC,CAAD,CAAlB,EAAuB;AACrB;AACA;AACA;AACA,MAAA,OAAA;AACD,KAAA;AAED;;;AACA,IAAA,IAAIib,aAAJ,CAAA;AACA;;AACA,IAAA,IAAInW,IAAJ,CAAA;IACA,IAAIoW,SAAS,GAAG,KAAhB,CAAA;AACA,IAAA,MAAMb,cAAc,IAAG,KAASra,IAAAA,CAAZ,CAApB,CAAA;;IAEA,QAAQqa,cAAc,GAAGra,CAAC,CAACoa,GAAL,GAAWpa,CAAC,CAACmb,OAAnC;AACE,MAAA,KAAKhB,mBAAmB,CAAC,QAAD,EAAWE,cAAX,CAAxB;AACE,QAAA,IAAIjX,IAAI,CAACD,OAAL,CAAaiY,MAAjB,EAAyB;AACvBH,UAAAA,aAAa,GAAG,OAAhB,CAAA;AACD,SAAA;;AACD,QAAA,MAAA;;AACF,MAAA,KAAKd,mBAAmB,CAAC,GAAD,EAAME,cAAN,CAAxB;AACEY,QAAAA,aAAa,GAAG,YAAhB,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAKd,mBAAmB,CAAC,WAAD,EAAcE,cAAd,CAAxB;AACEvV,QAAAA,IAAI,GAAG,GAAP,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAKqV,mBAAmB,CAAC,SAAD,EAAYE,cAAZ,CAAxB;AACEvV,QAAAA,IAAI,GAAG,GAAP,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAKqV,mBAAmB,CAAC,YAAD,EAAeE,cAAf,CAAxB;AACEvV,QAAAA,IAAI,GAAG,GAAP,CAAA;AACAoW,QAAAA,SAAS,GAAG,IAAZ,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAKf,mBAAmB,CAAC,WAAD,EAAcE,cAAd,CAAxB;AACEa,QAAAA,SAAS,GAAG,IAAZ,CAAA;AACApW,QAAAA,IAAI,GAAG,GAAP,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAKqV,mBAAmB,CAAC,KAAD,EAAQE,cAAR,CAAxB;AACE,QAAA,IAAA,CAAKK,UAAL,EAAA,CAAA;;AACA,QAAA,MAAA;AAzBJ,KArBY;;;AAmDZ,IAAA,IAAI5V,IAAJ,EAAU;AACR;AACA9E,MAAAA,CAAC,CAACqU,cAAF,EAAA,CAAA;MAEA,MAAM;AAAElJ,QAAAA,SAAAA;AAAF,OAAA,GAAgB/H,IAAtB,CAAA;;AAEA,MAAA,IAAIA,IAAI,CAACD,OAAL,CAAakY,SAAb,IACGvW,IAAI,KAAK,GADZ,IAEG1B,IAAI,CAAC8U,WAAL,EAAA,GAAqB,CAF5B,EAE+B;AAC7B+C,QAAAA,aAAa,GAAGC,SAAS,GAAG,MAAH,GAAY,MAArC,CAAA;AACD,OAJD,MAIO,IAAI/P,SAAS,IAAIA,SAAS,CAAC3G,aAAV,GAA0B2G,SAAS,CAAC/E,UAAV,CAAqBZ,GAAhE,EAAqE;AAC1E;AACA;AACA;AACA;QACA2F,SAAS,CAACrE,GAAV,CAAchC,IAAd,CAAA,IAAuBoW,SAAS,GAAG,CAAC,EAAJ,GAAS,EAAzC,CAAA;AACA/P,QAAAA,SAAS,CAACtC,KAAV,CAAgBsC,SAAS,CAACrE,GAAV,CAAchK,CAA9B,EAAiCqO,SAAS,CAACrE,GAAV,CAAc/J,CAA/C,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAIke,aAAJ,EAAmB;MACjBjb,CAAC,CAACqU,cAAF,EAAA,CADiB;;MAGjBjR,IAAI,CAAC6X,aAAD,CAAJ,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEN,UAAU,CAAC3a,CAAD,EAAI;IACZ,MAAM;AAAEsb,MAAAA,QAAAA;AAAF,KAAA,GAAe,KAAKlY,IAA1B,CAAA;;AACA,IAAA,IAAIkY,QAAQ,IACL7e,QAAQ,KAAKuD,CAAC,CAACgC,MADlB,IAEGsZ,QAAQ,KAAKtb,CAAC,CAACgC,MAFlB,IAGG,CAACsZ,QAAQ,CAACnK,QAAT;AAAkB;IAAqBnR,CAAC,CAACgC,MAAzC,CAHR,EAG2D;AACzD;AACAsZ,MAAAA,QAAQ,CAACN,KAAT,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAhJY;;AC/Bf,MAAMO,cAAc,GAAG,0BAAvB,CAAA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AACA,MAAMC,YAAN,CAAmB;AACjB;AACF;AACA;AACA;AACA;EACE3Z,WAAW,CAAC4Z,KAAD,EAAQ;AAAA,IAAA,IAAA,WAAA,CAAA;;IACjB,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,MAAM;MACJzZ,MADI;MAEJsI,UAFI;MAGJnM,SAHI;MAIJud,QAAQ,GAAG,MAAM,EAJb;AAKJnd,MAAAA,QAAQ,GAAG,GALP;AAMJgM,MAAAA,MAAM,GAAGgR,cAAAA;AANL,KAAA,GAOFE,KAPJ,CAAA;AASA,IAAA,IAAA,CAAKC,QAAL,GAAgBA,QAAhB,CAXiB;;AAcjB,IAAA,MAAMpd,IAAI,GAAGH,SAAS,GAAG,WAAH,GAAiB,SAAvC,CAAA;AACA,IAAA,MAAMH,SAAS,GAAGyd,CAAAA,WAAAA,GAAAA,KAAK,CAACnd,IAAD,CAAR,qDAAkB,EAAjC,CAAA;AAEA;;IACA,IAAKqd,CAAAA,OAAL,GAAe3Z,MAAf,CAAA;AACA;;IACA,IAAK4Z,CAAAA,WAAL,GAAmBtR,UAAnB,CAAA;AACA;;IACA,IAAKuR,CAAAA,SAAL,GAAiB,KAAjB,CAAA;AAEA;;IACA,IAAKC,CAAAA,gBAAL,GAAwB,IAAA,CAAKA,gBAAL,CAAsBvI,IAAtB,CAA2B,IAA3B,CAAxB,CAzBiB;AA4BjB;AACA;AACA;AACA;AACA;;AACA;;AACA,IAAA,IAAA,CAAKwI,cAAL,GAAsBtG,UAAU,CAAC,MAAM;MACrCpX,kBAAkB,CAAC2D,MAAD,EAAS1D,IAAT,EAAeC,QAAf,EAAyBgM,MAAzB,CAAlB,CAAA;AACA,MAAA,IAAA,CAAKwR,cAAL,GAAsBtG,UAAU,CAAC,MAAM;QACrCzT,MAAM,CAACR,gBAAP,CAAwB,eAAxB,EAAyC,IAAKsa,CAAAA,gBAA9C,EAAgE,KAAhE,CAAA,CAAA;QACA9Z,MAAM,CAACR,gBAAP,CAAwB,kBAAxB,EAA4C,KAAKsa,gBAAjD,EAAmE,KAAnE,CAAA,CAFqC;AAKrC;AACA;AACA;;AACA,QAAA,IAAA,CAAKC,cAAL,GAAsBtG,UAAU,CAAC,MAAM;AACrC,UAAA,IAAA,CAAKuG,kBAAL,EAAA,CAAA;AACD,SAF+B,EAE7Bzd,QAAQ,GAAG,GAFkB,CAAhC,CAAA;AAGAyD,QAAAA,MAAM,CAAC9D,KAAP,CAAaI,IAAb,IAAqBN,SAArB,CAAA;AACD,OAZ+B,EAY7B,EAZ6B,CAAhC,CAFqC;KAAP,EAe7B,CAf6B,CAAhC,CAAA;AAgBD,GAAA;AAED;AACF;AACA;AACA;;;EACE8d,gBAAgB,CAAC9b,CAAD,EAAI;AAClB,IAAA,IAAIA,CAAC,CAACgC,MAAF,KAAa,IAAA,CAAK2Z,OAAtB,EAA+B;AAC7B,MAAA,IAAA,CAAKK,kBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEA,EAAAA,kBAAkB,GAAG;IACnB,IAAI,CAAC,IAAKH,CAAAA,SAAV,EAAqB;MACnB,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACA,MAAA,IAAA,CAAKH,QAAL,EAAA,CAAA;;MACA,IAAI,IAAA,CAAKE,WAAT,EAAsB;AACpB,QAAA,IAAA,CAAKA,WAAL,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GA/EgB;;;AAkFjBlT,EAAAA,OAAO,GAAG;IACR,IAAI,IAAA,CAAKqT,cAAT,EAAyB;MACvBrG,YAAY,CAAC,IAAKqG,CAAAA,cAAN,CAAZ,CAAA;AACD,KAAA;;IACDhd,qBAAqB,CAAC,IAAK4c,CAAAA,OAAN,CAArB,CAAA;;IACA,IAAKA,CAAAA,OAAL,CAAaM,mBAAb,CAAiC,eAAjC,EAAkD,IAAA,CAAKH,gBAAvD,EAAyE,KAAzE,CAAA,CAAA;;IACA,IAAKH,CAAAA,OAAL,CAAaM,mBAAb,CAAiC,kBAAjC,EAAqD,IAAA,CAAKH,gBAA1D,EAA4E,KAA5E,CAAA,CAAA;;IACA,IAAI,CAAC,IAAKD,CAAAA,SAAV,EAAqB;AACnB,MAAA,IAAA,CAAKG,kBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AA5FgB;;ACpBnB,MAAME,yBAAyB,GAAG,EAAlC,CAAA;AACA,MAAMC,qBAAqB,GAAG,IAA9B,CAAA;AAEA;AACA;AACA;;AACA,MAAMC,WAAN,CAAkB;AAChB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEva,EAAAA,WAAW,CAAC8J,eAAD,EAAkBkC,YAAlB,EAAgC0C,gBAAhC,EAAkD;AAC3D,IAAA,IAAA,CAAKzD,QAAL,GAAgBnB,eAAe,GAAG,IAAlC,CAD2D;AAG3D;;AACA,IAAA,IAAA,CAAK0Q,aAAL,GAAqBxO,YAAY,IAAIsO,qBAArC,CAJ2D;;AAO3D,IAAA,IAAA,CAAKG,iBAAL,GAAyB/L,gBAAgB,IAAI2L,yBAA7C,CAAA;IAEA,IAAKK,CAAAA,gBAAL,GAAwB,IAAA,CAAKD,iBAA7B,CAAA;;AAEA,IAAA,IAAI,IAAKD,CAAAA,aAAL,GAAqB,CAAzB,EAA4B;AAC1B,MAAA,IAAA,CAAKE,gBAAL,IAAyBnf,IAAI,CAACI,IAAL,CAAU,CAAI,GAAA,IAAA,CAAK6e,aAAL,GAAqB,IAAKA,CAAAA,aAAxC,CAAzB,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEG,EAAAA,SAAS,CAACC,aAAD,EAAgBC,SAAhB,EAA2B;AAClC;AACA;AACA;AACA;IAEA,IAAI/G,YAAY,GAAG,CAAnB,CAAA;AACA,IAAA,IAAIgH,KAAJ,CAAA;AAEAD,IAAAA,SAAS,IAAI,IAAb,CAAA;AAEA,IAAA,MAAME,iBAAiB,GAAGxf,IAAI,CAACyf,CAAL,KAAW,CAAC,IAAKR,CAAAA,aAAN,GAAsB,IAAA,CAAKC,iBAA3B,GAA+CI,SAA1D,CAA1B,CAAA;;AAEA,IAAA,IAAI,IAAKL,CAAAA,aAAL,KAAuB,CAA3B,EAA8B;AAC5BM,MAAAA,KAAK,GAAG,IAAK7P,CAAAA,QAAL,GAAgB,IAAKwP,CAAAA,iBAAL,GAAyBG,aAAjD,CAAA;MAEA9G,YAAY,GAAG,CAAC8G,aAAa,GAAGE,KAAK,GAAGD,SAAzB,IAAsCE,iBAArD,CAAA;MAEA,IAAK9P,CAAAA,QAAL,GAAgB6I,YAAY,GACP,CAAC,KAAK2G,iBADX,GACgCK,KAAK,GACjCC,iBAFpB,CAAA;AAGD,KARD,MAQO,IAAI,IAAA,CAAKP,aAAL,GAAqB,CAAzB,EAA4B;AACjCM,MAAAA,KAAK,GAAI,CAAA,GAAI,IAAKJ,CAAAA,gBAAV,IACK,IAAKF,CAAAA,aAAL,GAAqB,IAAA,CAAKC,iBAA1B,GAA8CG,aAA9C,GAA8D,IAAA,CAAK3P,QADxE,CAAR,CAAA;MAGA,MAAMgQ,UAAU,GAAG1f,IAAI,CAAC2f,GAAL,CAAS,IAAKR,CAAAA,gBAAL,GAAwBG,SAAjC,CAAnB,CAAA;MACA,MAAMM,UAAU,GAAG5f,IAAI,CAAC6f,GAAL,CAAS,IAAKV,CAAAA,gBAAL,GAAwBG,SAAjC,CAAnB,CAAA;MAEA/G,YAAY,GAAGiH,iBAAiB,IACZH,aAAa,GAAGK,UAAhB,GAA6BH,KAAK,GAAGK,UADzB,CAAhC,CAAA;MAGA,IAAKlQ,CAAAA,QAAL,GAAgB6I,YAAY,GACP,CAAC,IAAK2G,CAAAA,iBADX,GAEI,IAAA,CAAKD,aAFT,GAGIO,iBAAiB,IAChB,CAAC,IAAA,CAAKL,gBAAN,GAAyBE,aAAzB,GAAyCO,UAAzC,GACD,IAAKT,CAAAA,gBAAL,GAAwBI,KAAxB,GAAgCG,UAFf,CAHrC,CAAA;AAMD,KArCiC;;;AAyClC,IAAA,OAAOnH,YAAP,CAAA;AACD,GAAA;;AAhFe;;ACJlB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,MAAMuH,eAAN,CAAsB;AACpB;AACF;AACA;EACErb,WAAW,CAAC4Z,KAAD,EAAQ;IACjB,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAK0B,CAAAA,IAAL,GAAY,CAAZ,CAAA;IAEA,MAAM;MACJnR,KADI;MAEJa,GAFI;MAGJC,QAHI;MAIJmB,QAJI;MAKJ3D,UALI;MAMJoR,QAAQ,GAAG,MAAM,EANb;MAOJ7N,YAPI;AAQJ0C,MAAAA,gBAAAA;AARI,KAAA,GASFkL,KATJ,CAAA;IAWA,IAAKC,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;IAEA,MAAM0B,KAAK,GAAG,IAAIhB,WAAJ,CAAgBtP,QAAhB,EAA0Be,YAA1B,EAAwC0C,gBAAxC,CAAd,CAAA;AACA,IAAA,IAAI8M,QAAQ,GAAGzI,IAAI,CAACpE,GAAL,EAAf,CAAA;AACA,IAAA,IAAIiM,aAAa,GAAGzQ,KAAK,GAAGa,GAA5B,CAAA;;IAEA,MAAMyQ,aAAa,GAAG,MAAM;MAC1B,IAAI,IAAA,CAAKH,IAAT,EAAe;AACbV,QAAAA,aAAa,GAAGW,KAAK,CAACZ,SAAN,CAAgBC,aAAhB,EAA+B7H,IAAI,CAACpE,GAAL,EAAA,GAAa6M,QAA5C,CAAhB,CADa;;AAIb,QAAA,IAAIjgB,IAAI,CAACG,GAAL,CAASkf,aAAT,IAA0B,CAA1B,IAA+Brf,IAAI,CAACG,GAAL,CAAS6f,KAAK,CAACtQ,QAAf,CAAA,GAA2B,EAA9D,EAAkE;AAChE;UACAmB,QAAQ,CAACpB,GAAD,CAAR,CAAA;;AACA,UAAA,IAAIvC,UAAJ,EAAgB;YACdA,UAAU,EAAA,CAAA;AACX,WAAA;;AACD,UAAA,IAAA,CAAKoR,QAAL,EAAA,CAAA;AACD,SAPD,MAOO;AACL2B,UAAAA,QAAQ,GAAGzI,IAAI,CAACpE,GAAL,EAAX,CAAA;AACAvC,UAAAA,QAAQ,CAACwO,aAAa,GAAG5P,GAAjB,CAAR,CAAA;AACA,UAAA,IAAA,CAAKsQ,IAAL,GAAYhI,qBAAqB,CAACmI,aAAD,CAAjC,CAAA;AACD,SAAA;AACF,OAAA;KAjBH,CAAA;;AAoBA,IAAA,IAAA,CAAKH,IAAL,GAAYhI,qBAAqB,CAACmI,aAAD,CAAjC,CAAA;AACD,GA9CmB;;;AAiDpB5U,EAAAA,OAAO,GAAG;AACR,IAAA,IAAI,IAAKyU,CAAAA,IAAL,IAAa,CAAjB,EAAoB;MAClBvH,oBAAoB,CAAC,IAAKuH,CAAAA,IAAN,CAApB,CAAA;AACD,KAAA;;IACD,IAAKA,CAAAA,IAAL,GAAY,CAAZ,CAAA;AACD,GAAA;;AAtDmB;;ACdtB;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;;AAEA;AACA;AACA;;AACA,MAAMI,UAAN,CAAiB;AACf1b,EAAAA,WAAW,GAAG;AACZ;IACA,IAAK2b,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACExP,WAAW,CAACyN,KAAD,EAAQ;AACjB,IAAA,IAAA,CAAKgC,MAAL,CAAYhC,KAAZ,EAAmB,IAAnB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACEvR,eAAe,CAACuR,KAAD,EAAQ;IACrB,IAAKgC,CAAAA,MAAL,CAAYhC,KAAZ,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEgC,EAAAA,MAAM,CAAChC,KAAD,EAAQiC,QAAR,EAAkB;AACtB,IAAA,MAAMC,SAAS,GAAGD,QAAQ,GACtB,IAAIR,eAAJ;AAAoB;IAAmCzB,KAAvD,CADsB,GAEtB,IAAID,YAAJ;AAAiB;AAAgCC,IAAAA,KAAjD,CAFJ,CAAA;AAIA,IAAA,IAAA,CAAK+B,gBAAL,CAAsBxa,IAAtB,CAA2B2a,SAA3B,CAAA,CAAA;;IACAA,SAAS,CAACjC,QAAV,GAAqB,MAAM,KAAKkC,IAAL,CAAUD,SAAV,CAA3B,CAAA;;AAEA,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACEC,IAAI,CAACD,SAAD,EAAY;AACdA,IAAAA,SAAS,CAACjV,OAAV,EAAA,CAAA;IACA,MAAM7E,KAAK,GAAG,IAAK2Z,CAAAA,gBAAL,CAAsBlI,OAAtB,CAA8BqI,SAA9B,CAAd,CAAA;;AACA,IAAA,IAAI9Z,KAAK,GAAG,CAAC,CAAb,EAAgB;AACd,MAAA,IAAA,CAAK2Z,gBAAL,CAAsBpH,MAAtB,CAA6BvS,KAA7B,EAAoC,CAApC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDoI,EAAAA,OAAO,GAAG;AAAE;AACV,IAAA,IAAA,CAAKuR,gBAAL,CAAsBjb,OAAtB,CAA+Bob,SAAD,IAAe;AAC3CA,MAAAA,SAAS,CAACjV,OAAV,EAAA,CAAA;KADF,CAAA,CAAA;IAGA,IAAK8U,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACE5T,EAAAA,UAAU,GAAG;IACX,IAAK4T,CAAAA,gBAAL,GAAwB,IAAKA,CAAAA,gBAAL,CAAsBza,MAAtB,CAA8B4a,SAAD,IAAe;AAClE,MAAA,IAAIA,SAAS,CAAClC,KAAV,CAAgBtR,KAApB,EAA2B;AACzBwT,QAAAA,SAAS,CAACjV,OAAV,EAAA,CAAA;AACA,QAAA,OAAO,KAAP,CAAA;AACD,OAAA;;AAED,MAAA,OAAO,IAAP,CAAA;AACD,KAPuB,CAAxB,CAAA;AAQD,GAAA;;AAEDiQ,EAAAA,cAAc,GAAG;IACf,IAAK6E,CAAAA,gBAAL,GAAwB,IAAKA,CAAAA,gBAAL,CAAsBza,MAAtB,CAA8B4a,SAAD,IAAe;AAClE,MAAA,IAAIA,SAAS,CAAClC,KAAV,CAAgB3C,YAApB,EAAkC;AAChC6E,QAAAA,SAAS,CAACjV,OAAV,EAAA,CAAA;AACA,QAAA,OAAO,KAAP,CAAA;AACD,OAAA;;AAED,MAAA,OAAO,IAAP,CAAA;AACD,KAPuB,CAAxB,CAAA;AAQD,GAAA;AAED;AACF;AACA;AACE;AACA;AACA;AACA;AACA;;AAEA;AACF;AACA;;;AACEmV,EAAAA,YAAY,GAAG;AACb,IAAA,OAAO,KAAKL,gBAAL,CAAsBM,IAAtB,CAA4BH,SAAD,IAAe;AAC/C,MAAA,OAAOA,SAAS,CAAClC,KAAV,CAAgBtR,KAAvB,CAAA;AACD,KAFM,CAAP,CAAA;AAGD,GAAA;;AAhGc;;ACpBjB;;AAEA;AACA;AACA;AACA;AACA,MAAM4T,WAAN,CAAkB;AAChB;AACF;AACA;EACElc,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACAA,IAAI,CAACgQ,MAAL,CAAYrR,GAAZ,CAAgBqB,IAAI,CAACgG,OAArB,EAA8B,OAA9B;AAAuC;AAA2B,IAAA,IAAA,CAAK4U,QAAL,CAAczK,IAAd,CAAmB,IAAnB,CAAlE,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;EACEyK,QAAQ,CAAChe,CAAD,EAAI;AACVA,IAAAA,CAAC,CAACqU,cAAF,EAAA,CAAA;IACA,MAAM;AAAElJ,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK/H,IAA3B,CAAA;IACA,IAAI;MAAE6a,MAAF;AAAUC,MAAAA,MAAAA;AAAV,KAAA,GAAqBle,CAAzB,CAAA;;IAEA,IAAI,CAACmL,SAAL,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAK/H,IAAL,CAAUyB,QAAV,CAAmB,OAAnB,EAA4B;AAAEkM,MAAAA,aAAa,EAAE/Q,CAAAA;KAA7C,CAAA,CAAkDyI,gBAAtD,EAAwE;AACtE,MAAA,OAAA;AACD,KAAA;;IAED,IAAIzI,CAAC,CAACE,OAAF,IAAa,IAAA,CAAKkD,IAAL,CAAUD,OAAV,CAAkBgb,WAAnC,EAAgD;AAC9C;AACA,MAAA,IAAIhT,SAAS,CAACzB,UAAV,EAAJ,EAA4B;QAC1B,IAAImB,UAAU,GAAG,CAACqT,MAAlB,CAAA;;QACA,IAAIle,CAAC,CAACoe,SAAF,KAAgB,CAAA;AAAE;UAAsB;AAC1CvT,UAAAA,UAAU,IAAI,IAAd,CAAA;AACD,SAFD,MAEO;AACLA,UAAAA,UAAU,IAAI7K,CAAC,CAACoe,SAAF,GAAc,CAAd,GAAkB,KAAhC,CAAA;AACD,SAAA;;QACDvT,UAAU,GAAG,KAAKA,UAAlB,CAAA;AAEA,QAAA,MAAMvB,aAAa,GAAG6B,SAAS,CAAC3G,aAAV,GAA0BqG,UAAhD,CAAA;AACAM,QAAAA,SAAS,CAAC9B,MAAV,CAAiBC,aAAjB,EAAgC;UAC9BxM,CAAC,EAAEkD,CAAC,CAACqe,OADyB;UAE9BthB,CAAC,EAAEiD,CAAC,CAACse,OAAAA;SAFP,CAAA,CAAA;AAID,OAAA;AACF,KAjBD,MAiBO;AACL;AACA,MAAA,IAAInT,SAAS,CAACH,UAAV,EAAJ,EAA4B;QAC1B,IAAIhL,CAAC,CAACoe,SAAF,KAAgB,CAAA;AAAE;UAAsB;AAC1C;AACAH,UAAAA,MAAM,IAAI,EAAV,CAAA;AACAC,UAAAA,MAAM,IAAI,EAAV,CAAA;AACD,SAAA;;AAED/S,QAAAA,SAAS,CAACtC,KAAV,CACEsC,SAAS,CAACrE,GAAV,CAAchK,CAAd,GAAkBmhB,MADpB,EAEE9S,SAAS,CAACrE,GAAV,CAAc/J,CAAd,GAAkBmhB,MAFpB,CAAA,CAAA;AAID,OAAA;AACF,KAAA;AACF,GAAA;;AA1De;;ACJlB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AACA,SAASK,cAAT,CAAwBC,QAAxB,EAAkC;AAChC,EAAA,IAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,OAAOA,QAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,CAACA,QAAD,IAAa,CAACA,QAAQ,CAACC,WAA3B,EAAwC;AACtC,IAAA,OAAO,EAAP,CAAA;AACD,GAAA;;EAED,MAAMC,OAAO,GAAGF,QAAhB,CAAA;AACA,EAAA,IAAIG,GAAG,GAAG,uFAAV,CAjBgC;;AAmBhCA,EAAAA,GAAG,GAAGA,GAAG,CAAC9b,KAAJ,CAAU,IAAV,EAAgB+b,IAAhB;AAAqB;AAAuBF,EAAAA,OAAO,CAACG,IAAR,IAAgB,EAA5D,CAAN,CAnBgC;AAsBhC;AACA;AACA;AACA;;EACA,IAAIH,OAAO,CAACI,SAAZ,EAAuB;AACrBH,IAAAA,GAAG,IAAI,6CAAgDD,GAAAA,OAAO,CAACI,SAAxD,GAAoE,KAA3E,CAAA;AACD,GAAA;;EAEDH,GAAG,IAAID,OAAO,CAACK,KAAf,CAAA;AAEAJ,EAAAA,GAAG,IAAI,QAAP,CAAA;AAEA,EAAA,OAAOA,GAAP,CAAA;AACD,CAAA;;AAED,MAAMK,SAAN,CAAgB;AACd;AACF;AACA;AACA;AACEnd,EAAAA,WAAW,CAACuB,IAAD,EAAO6B,IAAP,EAAa;AAAA,IAAA,IAAA,UAAA,CAAA;;IACtB,MAAMmF,IAAI,GAAGnF,IAAI,CAACmF,IAAL,IAAanF,IAAI,CAAC5I,SAA/B,CAAA;AACA,IAAA,IAAI4iB,WAAW,GAAGha,IAAI,CAACia,IAAvB,CAFsB;;AAKtB,IAAA,IAAI9b,IAAI,CAACD,OAAL,CAAaiH,IAAb,CAAA,KAAuB,KAA3B,EAAkC;AAChC;AACA,MAAA,OAAA;AACD,KARqB;AAWtB;;;IACA,IAAI,OAAOhH,IAAI,CAACD,OAAL,CAAaiH,IAAI,GAAG,KAApB,CAAP,KAAsC,QAA1C,EAAoD;AAClD;AACA;AACA;AACA;AACA;MACA6U,WAAW,GAAG7b,IAAI,CAACD,OAAL,CAAaiH,IAAI,GAAG,KAApB,CAAd,CAAA;AACD,KAAA;;AAEDhH,IAAAA,IAAI,CAACyB,QAAL,CAAc,iBAAd,EAAiC;AAAEI,MAAAA,IAAAA;KAAnC,CAAA,CAAA;IAEA,IAAI5I,SAAS,GAAG,EAAhB,CAAA;;IACA,IAAI4I,IAAI,CAACka,QAAT,EAAmB;AACjB9iB,MAAAA,SAAS,IAAI,eAAb,CAAA;MACAA,SAAS,IAAK4I,IAAI,CAAC5I,SAAL,IAAmB,CAAgB4I,cAAAA,EAAAA,IAAI,CAACmF,IAAK,CAA3D,CAAA,CAAA;AACD,KAHD,MAGO;MACL/N,SAAS,IAAK4I,IAAI,CAAC5I,SAAL,IAAmB,CAAQ4I,MAAAA,EAAAA,IAAI,CAACmF,IAAK,CAAnD,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI9N,OAAO,GAAG2I,IAAI,CAACka,QAAL,GAAiBla,IAAI,CAAC3I,OAAL,IAAgB,QAAjC,GAA8C2I,IAAI,CAAC3I,OAAL,IAAgB,KAA5E,CAAA;IACAA,OAAO;AAAG;IAA4CA,OAAO,CAAC8iB,WAAR,EAAtD,CAAA;AACA;;AACA,IAAA,MAAMhW,OAAO,GAAGhN,aAAa,CAACC,SAAD,EAAYC,OAAZ,CAA7B,CAAA;;IAEA,IAAI2I,IAAI,CAACka,QAAT,EAAmB;MACjB,IAAI7iB,OAAO,KAAK,QAAhB,EAA0B;AACxB;QAAkC8M,OAAD,CAAUnH,IAAV,GAAiB,QAAjB,CAAA;AAClC,OAAA;;MAED,IAAI;AAAEod,QAAAA,KAAAA;AAAF,OAAA,GAAYpa,IAAhB,CAAA;MACA,MAAM;AAAEqa,QAAAA,SAAAA;OAAcra,GAAAA,IAAtB,CANiB;;MASjB,IAAI,OAAO7B,IAAI,CAACD,OAAL,CAAaiH,IAAI,GAAG,OAApB,CAAP,KAAwC,QAA5C,EAAsD;AACpD;QACAiV,KAAK,GAAGjc,IAAI,CAACD,OAAL,CAAaiH,IAAI,GAAG,OAApB,CAAR,CAAA;AACD,OAAA;;AAED,MAAA,IAAIiV,KAAJ,EAAW;QACTjW,OAAO,CAACiW,KAAR,GAAgBA,KAAhB,CAAA;AACD,OAAA;;AAED,MAAA,MAAME,QAAQ,GAAGD,SAAS,IAAID,KAA9B,CAAA;;AACA,MAAA,IAAIE,QAAJ,EAAc;AACZnW,QAAAA,OAAO,CAAC2O,YAAR,CAAqB,YAArB,EAAmCwH,QAAnC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAEDnW,IAAAA,OAAO,CAACoW,SAAR,GAAoBjB,cAAc,CAACU,WAAD,CAAlC,CAAA;;IAEA,IAAIha,IAAI,CAACwa,MAAT,EAAiB;AACfxa,MAAAA,IAAI,CAACwa,MAAL,CAAYrW,OAAZ,EAAqBhG,IAArB,CAAA,CAAA;AACD,KAAA;;IAED,IAAI6B,IAAI,CAACya,OAAT,EAAkB;AAChBtW,MAAAA,OAAO,CAACuW,OAAR,GAAmB3f,CAAD,IAAO;AACvB,QAAA,IAAI,OAAOiF,IAAI,CAACya,OAAZ,KAAwB,QAA5B,EAAsC;AACpC;AACAtc,UAAAA,IAAI,CAAC6B,IAAI,CAACya,OAAN,CAAJ,EAAA,CAAA;SAFF,MAGO,IAAI,OAAOza,IAAI,CAACya,OAAZ,KAAwB,UAA5B,EAAwC;AAC7Cza,UAAAA,IAAI,CAACya,OAAL,CAAa1f,CAAb,EAAgBoJ,OAAhB,EAAyBhG,IAAzB,CAAA,CAAA;AACD,SAAA;OANH,CAAA;AAQD,KA3EqB;;;AA8EtB,IAAA,MAAMwc,QAAQ,GAAG3a,IAAI,CAAC2a,QAAL,IAAiB,KAAlC,CAAA;AACA;;AACA,IAAA,IAAIvY,SAAS,GAAGjE,IAAI,CAACgG,OAArB,CAAA;;IACA,IAAIwW,QAAQ,KAAK,KAAjB,EAAwB;AACtB,MAAA,IAAI,CAACxc,IAAI,CAACyc,MAAV,EAAkB;AAChBzc,QAAAA,IAAI,CAACyc,MAAL,GAAczjB,aAAa,CAAC,mCAAD,EAAsC,KAAtC,EAA6CgH,IAAI,CAACiQ,UAAlD,CAA3B,CAAA;AACD,OAAA;;MACDhM,SAAS,GAAGjE,IAAI,CAACyc,MAAjB,CAAA;AACD,KALD,MAKO;AACL;AACA;AACAzW,MAAAA,OAAO,CAAC6H,SAAR,CAAkBlP,GAAlB,CAAsB,qBAAtB,CAAA,CAAA;;MAEA,IAAI6d,QAAQ,KAAK,SAAjB,EAA4B;QAC1BvY,SAAS,GAAGjE,IAAI,CAACiQ,UAAjB,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,CAAA,UAAA,GAAAhM,SAAS,MAAA,IAAT,IAAW3K,UAAAA,KAAAA,KAAAA,CAAAA,IAAAA,UAAAA,CAAAA,WAAX,CAAuB0G,IAAI,CAAC0S,YAAL,CAAkB,WAAlB,EAA+B1M,OAA/B,EAAwCnE,IAAxC,CAAvB,CAAA,CAAA;AACD,GAAA;;AAtGa;;AC7EhB;AACA;AACA;;AAEA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6a,eAAT,CAAyB1W,OAAzB,EAAkChG,IAAlC,EAAwC2c,YAAxC,EAAsD;AACpD3W,EAAAA,OAAO,CAAC6H,SAAR,CAAkBlP,GAAlB,CAAsB,qBAAtB,EADoD;;AAGpDqH,EAAAA,OAAO,CAAC2O,YAAR,CAAqB,eAArB,EAAsC,aAAtC,CAAA,CAAA;AACA3U,EAAAA,IAAI,CAAC+P,EAAL,CAAQ,QAAR,EAAkB,MAAM;AACtB,IAAA,IAAI,CAAC/P,IAAI,CAACD,OAAL,CAAa6c,IAAlB,EAAwB;AACtB,MAAA,IAAID,YAAJ,EAAkB;AAChB;AACC3W,QAAAA,OAAD,CAAU6W,QAAV,GAAqB,EAAE7c,IAAI,CAACwD,SAAL,GAAiBxD,IAAI,CAAC8U,WAAL,EAAA,GAAqB,CAAxC,CAArB,CAAA;AACD,OAHD,MAGO;AACL;QACC9O,OAAD,CAAU6W,QAAV,GAAqB,EAAE7c,IAAI,CAACwD,SAAL,GAAiB,CAAnB,CAArB,CAAA;AACD,OAAA;AACF,KAAA;GATH,CAAA,CAAA;AAWD,CAAA;AAED;;;AACO,MAAMsZ,SAAS,GAAG;AACvB9V,EAAAA,IAAI,EAAE,WADiB;AAEvB/N,EAAAA,SAAS,EAAE,2BAFY;AAGvBgjB,EAAAA,KAAK,EAAE,UAHgB;AAIvBc,EAAAA,KAAK,EAAE,EAJgB;AAKvBhB,EAAAA,QAAQ,EAAE,IALa;AAMvBS,EAAAA,QAAQ,EAAE,SANa;AAOvBV,EAAAA,IAAI,EAAE;AACJT,IAAAA,WAAW,EAAE,IADT;AAEJI,IAAAA,IAAI,EAAE,EAFF;AAGJE,IAAAA,KAAK,EAAE,2EAHH;AAIJD,IAAAA,SAAS,EAAE,iBAAA;GAXU;AAavBY,EAAAA,OAAO,EAAE,MAbc;AAcvBD,EAAAA,MAAM,EAAEK,eAAAA;AAde,CAAlB,CAAA;AAiBP;;AACO,MAAMM,SAAS,GAAG;AACvBhW,EAAAA,IAAI,EAAE,WADiB;AAEvB/N,EAAAA,SAAS,EAAE,2BAFY;AAGvBgjB,EAAAA,KAAK,EAAE,MAHgB;AAIvBc,EAAAA,KAAK,EAAE,EAJgB;AAKvBhB,EAAAA,QAAQ,EAAE,IALa;AAMvBS,EAAAA,QAAQ,EAAE,SANa;AAOvBV,EAAAA,IAAI,EAAE;AACJT,IAAAA,WAAW,EAAE,IADT;AAEJI,IAAAA,IAAI,EAAE,EAFF;AAGJE,IAAAA,KAAK,EAAE,sCAHH;AAIJD,IAAAA,SAAS,EAAE,iBAAA;GAXU;AAavBY,EAAAA,OAAO,EAAE,MAbc;AAcvBD,EAAAA,MAAM,EAAE,CAACjjB,EAAD,EAAK4G,IAAL,KAAc;AACpB0c,IAAAA,eAAe,CAACtjB,EAAD,EAAK4G,IAAL,EAAW,IAAX,CAAf,CAAA;AACD,GAAA;AAhBsB,CAAlB;;ACjDP;AACA,MAAMid,WAAW,GAAG;AAClBjW,EAAAA,IAAI,EAAE,OADY;AAElBiV,EAAAA,KAAK,EAAE,OAFW;AAGlBc,EAAAA,KAAK,EAAE,EAHW;AAIlBhB,EAAAA,QAAQ,EAAE,IAJQ;AAKlBD,EAAAA,IAAI,EAAE;AACJT,IAAAA,WAAW,EAAE,IADT;AAEJM,IAAAA,KAAK,EAAE,uFAFH;AAGJD,IAAAA,SAAS,EAAE,iBAAA;GARK;AAUlBY,EAAAA,OAAO,EAAE,OAAA;AAVS,CAApB;;ACDA;AACA,MAAMY,UAAU,GAAG;AACjBlW,EAAAA,IAAI,EAAE,MADW;AAEjBiV,EAAAA,KAAK,EAAE,MAFU;AAGjBc,EAAAA,KAAK,EAAE,EAHU;AAIjBhB,EAAAA,QAAQ,EAAE,IAJO;AAKjBD,EAAAA,IAAI,EAAE;AACJT,IAAAA,WAAW,EAAE,IADT;AAEJ;AACAM,IAAAA,KAAK,EAAE,gGAAA,GACC,6EADD,GAEC,6EALJ;AAMJD,IAAAA,SAAS,EAAE,gBAAA;GAXI;AAajBY,EAAAA,OAAO,EAAE,YAAA;AAbQ,CAAnB;;ACDA;AACO,MAAMa,gBAAgB,GAAG;AAC9BnW,EAAAA,IAAI,EAAE,WADwB;AAE9BwV,EAAAA,QAAQ,EAAE,KAFoB;AAG9BO,EAAAA,KAAK,EAAE,CAHuB;AAI9BjB,EAAAA,IAAI,EAAE;AACJT,IAAAA,WAAW,EAAE,IADT;AAEJ;AACAM,IAAAA,KAAK,EAAE,iIAHH;AAIJD,IAAAA,SAAS,EAAE,mBAAA;GARiB;AAU9BW,EAAAA,MAAM,EAAE,CAACe,gBAAD,EAAmBpd,IAAnB,KAA4B;AAClC;AACA,IAAA,IAAIqd,SAAJ,CAAA;AACA;;IACA,IAAIC,YAAY,GAAG,IAAnB,CAAA;AAEA;AACJ;AACA;AACA;;AACI,IAAA,MAAMC,oBAAoB,GAAG,CAACtkB,SAAD,EAAY0F,GAAZ,KAAoB;MAC/Cye,gBAAgB,CAACvP,SAAjB,CAA2BW,MAA3B,CAAkC,mBAAsBvV,GAAAA,SAAxD,EAAmE0F,GAAnE,CAAA,CAAA;KADF,CAAA;AAIA;AACJ;AACA;;;IACI,MAAM6e,sBAAsB,GAAIC,OAAD,IAAa;MAC1C,IAAIJ,SAAS,KAAKI,OAAlB,EAA2B;AACzBJ,QAAAA,SAAS,GAAGI,OAAZ,CAAA;AACAF,QAAAA,oBAAoB,CAAC,QAAD,EAAWE,OAAX,CAApB,CAAA;AACD,OAAA;KAJH,CAAA;;IAOA,MAAMC,yBAAyB,GAAG,MAAM;AAAA,MAAA,IAAA,eAAA,CAAA;;MACtC,IAAI,EAAA,CAAA,eAAA,GAAC1d,IAAI,CAAC+H,SAAN,MAAA,IAAA,IAAA,eAAA,KAAA,KAAA,CAAA,IAAC,eAAgBjE,CAAAA,OAAhB,CAAwB6Z,SAAxB,EAAD,CAAJ,EAA0C;QACxCH,sBAAsB,CAAC,KAAD,CAAtB,CAAA;;AACA,QAAA,IAAIF,YAAJ,EAAkB;UAChBhL,YAAY,CAACgL,YAAD,CAAZ,CAAA;AACAA,UAAAA,YAAY,GAAG,IAAf,CAAA;AACD,SAAA;;AACD,QAAA,OAAA;AACD,OAAA;;MAED,IAAI,CAACA,YAAL,EAAmB;AACjB;QACAA,YAAY,GAAGjL,UAAU,CAAC,MAAM;AAAA,UAAA,IAAA,gBAAA,CAAA;;AAC9BmL,UAAAA,sBAAsB,CAAC3V,OAAO,CAAC7H,CAAAA,gBAAAA,GAAAA,IAAI,CAAC+H,SAAN,MAAC,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gBAAA,CAAgBjE,OAAhB,CAAwB6Z,SAAxB,EAAD,CAAR,CAAtB,CAAA;AACAL,UAAAA,YAAY,GAAG,IAAf,CAAA;AACD,SAHwB,EAGtBtd,IAAI,CAACD,OAAL,CAAa6d,cAHS,CAAzB,CAAA;AAID,OAAA;KAhBH,CAAA;;AAmBA5d,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,QAAR,EAAkB2N,yBAAlB,CAAA,CAAA;AAEA1d,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,cAAR,EAAyBnT,CAAD,IAAO;AAC7B,MAAA,IAAIoD,IAAI,CAAC+H,SAAL,KAAmBnL,CAAC,CAACuE,KAAzB,EAAgC;QAC9Buc,yBAAyB,EAAA,CAAA;AAC1B,OAAA;AACF,KAJD,EA7CkC;;IAoDlC,IAAI1d,IAAI,CAAC6d,EAAT,EAAa;AACX7d,MAAAA,IAAI,CAAC6d,EAAL,CAAQH,yBAAR,GAAoCA,yBAApC,CAAA;AACD,KAAA;AACF,GAAA;AAjE6B,CAAzB;;ACDP;AACO,MAAMI,gBAAgB,GAAG;AAC9B9W,EAAAA,IAAI,EAAE,SADwB;AAE9B+V,EAAAA,KAAK,EAAE,CAFuB;AAG9BV,EAAAA,MAAM,EAAE,CAAC0B,cAAD,EAAiB/d,IAAjB,KAA0B;AAChCA,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,QAAR,EAAkB,MAAM;AACtBgO,MAAAA,cAAc,CAACC,SAAf,GAA4Bhe,IAAI,CAACwD,SAAL,GAAiB,CAAlB,GACGxD,IAAI,CAACD,OAAL,CAAake,iBADhB,GAEGje,IAAI,CAAC8U,WAAL,EAF9B,CAAA;KADF,CAAA,CAAA;AAKD,GAAA;AAT6B,CAAzB;;ACMP;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASoJ,WAAT,CAAqB9kB,EAArB,EAAyB+kB,UAAzB,EAAqC;AACnC/kB,EAAAA,EAAE,CAACyU,SAAH,CAAaW,MAAb,CAAoB,iBAApB,EAAuC2P,UAAvC,CAAA,CAAA;AACD,CAAA;;AAED,MAAMC,EAAN,CAAS;AACP;AACF;AACA;EACE3f,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKqe,CAAAA,YAAL,GAAoB,KAApB,CAAA;AACA;;IACA,IAAKC,CAAAA,cAAL,GAAsB,EAAtB,CAAA;AACA;;IACA,IAAKC,CAAAA,KAAL,GAAa,EAAb,CAAA;AACA;;AACA,IAAA,IAAA,CAAKb,yBAAL,GAAiC,MAAM,EAAvC,CAAA;AAEA;AACJ;AACA;AACA;;;IACI,IAAKc,CAAAA,qBAAL,GAA6B3kB,SAA7B,CAAA;AACD,GAAA;;AAED4kB,EAAAA,IAAI,GAAG;IACL,MAAM;AAAEze,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IACA,IAAKqe,CAAAA,YAAL,GAAoB,KAApB,CAAA;AACA,IAAA,IAAA,CAAKC,cAAL,GAAsB,CACpBrB,WADoB,EAEpBH,SAFoB,EAGpBE,SAHoB,EAIpBE,UAJoB,EAKpBC,gBALoB,EAMpBW,gBANoB,CAAtB,CAAA;AASA9d,IAAAA,IAAI,CAACyB,QAAL,CAAc,YAAd,EAZK;;IAeL,IAAK6c,CAAAA,cAAL,CAAoBI,IAApB,CAAyB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACjC;AACA,MAAA,OAAO,CAACD,CAAC,CAAC5B,KAAF,IAAW,CAAZ,KAAkB6B,CAAC,CAAC7B,KAAF,IAAW,CAA7B,CAAP,CAAA;KAFF,CAAA,CAAA;IAKA,IAAKwB,CAAAA,KAAL,GAAa,EAAb,CAAA;IAEA,IAAKF,CAAAA,YAAL,GAAoB,IAApB,CAAA;AACA,IAAA,IAAA,CAAKC,cAAL,CAAoBnf,OAApB,CAA6B0f,aAAD,IAAmB;MAC7C,IAAKC,CAAAA,eAAL,CAAqBD,aAArB,CAAA,CAAA;KADF,CAAA,CAAA;AAIA7e,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,QAAR,EAAkB,MAAM;AAAA,MAAA,IAAA,aAAA,CAAA;;AACtB,MAAA,CAAA,aAAA,GAAA/P,IAAI,CAACgG,OAAL,MAAA,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,IAAA,aAAA,CAAc6H,SAAd,CAAwBW,MAAxB,CAA+B,iBAA/B,EAAkDxO,IAAI,CAAC8U,WAAL,OAAuB,CAAzE,CAAA,CAAA;KADF,CAAA,CAAA;IAIA9U,IAAI,CAAC+P,EAAL,CAAQ,eAAR,EAAyB,MAAM,IAAA,CAAKgP,gBAAL,EAA/B,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACED,eAAe,CAACE,WAAD,EAAc;IAC3B,IAAI,IAAA,CAAKX,YAAT,EAAuB;MACrB,IAAKE,CAAAA,KAAL,CAAW3e,IAAX,CACE,IAAIgc,SAAJ,CAAc,IAAK5b,CAAAA,IAAnB,EAAyBgf,WAAzB,CADF,CAAA,CAAA;AAGD,KAJD,MAIO;AACL,MAAA,IAAA,CAAKV,cAAL,CAAoB1e,IAApB,CAAyBof,WAAzB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACED,EAAAA,gBAAgB,GAAG;IACjB,MAAM;MAAE7G,QAAF;MAAYnQ,SAAZ;AAAuBhI,MAAAA,OAAAA;AAAvB,KAAA,GAAmC,KAAKC,IAA9C,CAAA;;AAEA,IAAA,IAAI,IAAKA,CAAAA,IAAL,CAAU4D,MAAV,CAAiBqb,SAAjB,IAA8B,CAAC/G,QAA/B,IAA2C,CAACnQ,SAAhD,EAA2D;AACzD,MAAA,OAAA;AACD,KAAA;;IAED,IAAI;AAAE3G,MAAAA,aAAAA;KAAkB2G,GAAAA,SAAxB,CAPiB;;AAUjB,IAAA,IAAI,CAAC,IAAK/H,CAAAA,IAAL,CAAU4D,MAAV,CAAiBC,MAAtB,EAA8B;AAC5BzC,MAAAA,aAAa,GAAG2G,SAAS,CAAC/E,UAAV,CAAqBT,OAArC,CAAA;AACD,KAAA;;AAED,IAAA,IAAInB,aAAa,KAAK,IAAKod,CAAAA,qBAA3B,EAAkD;AAChD,MAAA,OAAA;AACD,KAAA;;IACD,IAAKA,CAAAA,qBAAL,GAA6Bpd,aAA7B,CAAA;AAEA,IAAA,MAAM8d,iBAAiB,GAAGnX,SAAS,CAAC/E,UAAV,CAAqBT,OAArB,GAA+BwF,SAAS,CAAC/E,UAAV,CAAqBR,SAA9E,CAnBiB;;AAsBjB,IAAA,IAAIxI,IAAI,CAACG,GAAL,CAAS+kB,iBAAT,CAAA,GAA8B,IAA9B,IAAsC,CAACnX,SAAS,CAACzB,UAAV,EAA3C,EAAmE;AACjE;AACA4X,MAAAA,WAAW,CAAChG,QAAD,EAAW,KAAX,CAAX,CAAA;AACAA,MAAAA,QAAQ,CAACrK,SAAT,CAAmB5O,MAAnB,CAA0B,oBAA1B,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAEDiZ,IAAAA,QAAQ,CAACrK,SAAT,CAAmBlP,GAAnB,CAAuB,oBAAvB,CAAA,CAAA;IAEA,MAAMwgB,kBAAkB,GAAG/d,aAAa,KAAK2G,SAAS,CAAC/E,UAAV,CAAqBT,OAAvC,GACvBwF,SAAS,CAAC/E,UAAV,CAAqBR,SADE,GACUuF,SAAS,CAAC/E,UAAV,CAAqBT,OAD1D,CAAA;AAGA2b,IAAAA,WAAW,CAAChG,QAAD,EAAWiH,kBAAkB,IAAI/d,aAAjC,CAAX,CAAA;;IAEA,IAAIrB,OAAO,CAACqf,gBAAR,KAA6B,MAA7B,IACGrf,OAAO,CAACqf,gBAAR,KAA6B,eADpC,EACqD;AACnDlH,MAAAA,QAAQ,CAACrK,SAAT,CAAmBlP,GAAnB,CAAuB,qBAAvB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAlHM;;ACvBT;;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA,SAAS0gB,kBAAT,CAA4BjmB,EAA5B,EAAgC;AAC9B,EAAA,MAAMkmB,aAAa,GAAGlmB,EAAE,CAACmmB,qBAAH,EAAtB,CAAA;EACA,OAAO;IACL7lB,CAAC,EAAE4lB,aAAa,CAACE,IADZ;IAEL7lB,CAAC,EAAE2lB,aAAa,CAACG,GAFZ;IAGLlkB,CAAC,EAAE+jB,aAAa,CAAC7jB,KAAAA;GAHnB,CAAA;AAKD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASikB,yBAAT,CAAmCtmB,EAAnC,EAAuCumB,UAAvC,EAAmDC,WAAnD,EAAgE;AAC9D,EAAA,MAAMN,aAAa,GAAGlmB,EAAE,CAACmmB,qBAAH,EAAtB,CAD8D;AAI9D;;AACA,EAAA,MAAM5c,MAAM,GAAG2c,aAAa,CAAC7jB,KAAd,GAAsBkkB,UAArC,CAAA;AACA,EAAA,MAAM/c,MAAM,GAAG0c,aAAa,CAAC5jB,MAAd,GAAuBkkB,WAAtC,CAAA;EACA,MAAMC,aAAa,GAAGld,MAAM,GAAGC,MAAT,GAAkBD,MAAlB,GAA2BC,MAAjD,CAAA;EAEA,MAAMkd,OAAO,GAAG,CAACR,aAAa,CAAC7jB,KAAd,GAAsBkkB,UAAU,GAAGE,aAApC,IAAqD,CAArE,CAAA;EACA,MAAME,OAAO,GAAG,CAACT,aAAa,CAAC5jB,MAAd,GAAuBkkB,WAAW,GAAGC,aAAtC,IAAuD,CAAvE,CAAA;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;;AACE,EAAA,MAAMzb,MAAM,GAAG;AACb1K,IAAAA,CAAC,EAAE4lB,aAAa,CAACE,IAAd,GAAqBM,OADX;AAEbnmB,IAAAA,CAAC,EAAE2lB,aAAa,CAACG,GAAd,GAAoBM,OAFV;IAGbxkB,CAAC,EAAEokB,UAAU,GAAGE,aAAAA;AAHH,GAAf,CAnB8D;AA0B9D;;EACAzb,MAAM,CAAC4b,SAAP,GAAmB;IACjBzkB,CAAC,EAAE+jB,aAAa,CAAC7jB,KADA;IAEjBD,CAAC,EAAE8jB,aAAa,CAAC5jB,MAFA;AAGjBhC,IAAAA,CAAC,EAAEomB,OAHc;AAIjBnmB,IAAAA,CAAC,EAAEomB,OAAAA;GAJL,CAAA;AAOA,EAAA,OAAO3b,MAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,SAAS6b,cAAT,CAAwBxf,KAAxB,EAA+BD,QAA/B,EAAyC0f,QAAzC,EAAmD;AACxD;AACA,EAAA,MAAM3S,KAAK,GAAG2S,QAAQ,CAACze,QAAT,CAAkB,aAAlB,EAAiC;IAC7ChB,KAD6C;IAE7CD,QAF6C;AAG7C0f,IAAAA,QAAAA;GAHY,CAAd,CAFwD;;EAQxD,IAAI3S,KAAK,CAAC4S,WAAV,EAAuB;AACrB;IACA,OAAO5S,KAAK,CAAC4S,WAAb,CAAA;AACD,GAAA;;EAED,MAAM;AAAEna,IAAAA,OAAAA;AAAF,GAAA,GAAcxF,QAApB,CAAA;AACA;;AACA,EAAA,IAAI2f,WAAJ,CAAA;AACA;;AACA,EAAA,IAAIC,SAAJ,CAAA;;EAEA,IAAIpa,OAAO,IAAIka,QAAQ,CAACngB,OAAT,CAAiBsgB,aAAjB,KAAmC,KAAlD,EAAyD;IACvD,MAAMA,aAAa,GAAGH,QAAQ,CAACngB,OAAT,CAAiBsgB,aAAjB,IAAkC,KAAxD,CAAA;AACAD,IAAAA,SAAS,GAAGpa,OAAO,CAACsa,OAAR,CAAgBD,aAAhB,IACRra,OADQ;AACE;AAAmCA,IAAAA,OAAO,CAACua,aAAR,CAAsBF,aAAtB,CADjD,CAAA;AAED,GAAA;;AAEDD,EAAAA,SAAS,GAAGF,QAAQ,CAACxN,YAAT,CAAsB,SAAtB,EAAiC0N,SAAjC,EAA4C5f,QAA5C,EAAsDC,KAAtD,CAAZ,CAAA;;AAEA,EAAA,IAAI2f,SAAJ,EAAe;AACb,IAAA,IAAI,CAAC5f,QAAQ,CAACggB,YAAd,EAA4B;AAC1BL,MAAAA,WAAW,GAAGd,kBAAkB,CAACe,SAAD,CAAhC,CAAA;AACD,KAFD,MAEO;MACLD,WAAW,GAAGT,yBAAyB,CACrCU,SADqC,EAErC5f,QAAQ,CAAC/E,KAAT,IAAkB+E,QAAQ,CAACjF,CAA3B,IAAgC,CAFK,EAGrCiF,QAAQ,CAAC9E,MAAT,IAAmB8E,QAAQ,CAAChF,CAA5B,IAAiC,CAHI,CAAvC,CAAA;AAKD,KAAA;AACF,GAAA;;EAED,OAAO0kB,QAAQ,CAACxN,YAAT,CAAsB,aAAtB,EAAqCyN,WAArC,EAAkD3f,QAAlD,EAA4DC,KAA5D,CAAP,CAAA;AACD;;AC9GD;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMggB,eAAN,CAAsB;AACpB;AACF;AACA;AACA;AACEhiB,EAAAA,WAAW,CAACI,IAAD,EAAO6hB,OAAP,EAAgB;IACzB,IAAK7hB,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKwG,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;AACA,IAAA,IAAIqb,OAAJ,EAAa;AACXriB,MAAAA,MAAM,CAACsiB,MAAP,CAAc,IAAd,EAAoBD,OAApB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDzP,EAAAA,cAAc,GAAG;IACf,IAAK5L,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;AACD,GAAA;;AAfmB,CAAA;AAkBtB;AACA;AACA;AACA;;;AACA,MAAMub,SAAN,CAAgB;AACdniB,EAAAA,WAAW,GAAG;AACZ;AACJ;AACA;IACI,IAAKoiB,CAAAA,UAAL,GAAkB,EAAlB,CAAA;AAEA;AACJ;AACA;;IACI,IAAKC,CAAAA,QAAL,GAAgB,EAAhB,CAAA;AAEA;;IACA,IAAK9gB,CAAAA,IAAL,GAAYnG,SAAZ,CAAA;AAEA;;IACA,IAAKkG,CAAAA,OAAL,GAAelG,SAAf,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACEknB,SAAS,CAAC/Z,IAAD,EAAOga,EAAP,EAAWC,QAAQ,GAAG,GAAtB,EAA2B;AAAA,IAAA,IAAA,mBAAA,EAAA,oBAAA,EAAA,UAAA,CAAA;;AAClC,IAAA,IAAI,CAAC,IAAKH,CAAAA,QAAL,CAAc9Z,IAAd,CAAL,EAA0B;AACxB,MAAA,IAAA,CAAK8Z,QAAL,CAAc9Z,IAAd,CAAA,GAAsB,EAAtB,CAAA;AACD,KAAA;;AAED,IAAA,CAAA,mBAAA,GAAA,IAAA,CAAK8Z,QAAL,CAAc9Z,IAAd,CAAA,MAAA,IAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,IAAA,mBAAA,CAAqBpH,IAArB,CAA0B;MAAEohB,EAAF;AAAMC,MAAAA,QAAAA;KAAhC,CAAA,CAAA;AACA,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAKH,QAAL,CAAc9Z,IAAd,CAAqB0X,MAAAA,IAAAA,IAAAA,oBAAAA,KAAAA,KAAAA,CAAAA,IAAAA,oBAAAA,CAAAA,IAArB,CAA0B,CAACwC,EAAD,EAAKC,EAAL,KAAYD,EAAE,CAACD,QAAH,GAAcE,EAAE,CAACF,QAAvD,CAAA,CAAA;IAEA,CAAKjhB,UAAAA,GAAAA,IAAAA,CAAAA,IAAL,kDAAW+gB,SAAX,CAAqB/Z,IAArB,EAA2Bga,EAA3B,EAA+BC,QAA/B,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEG,EAAAA,YAAY,CAACpa,IAAD,EAAOga,EAAP,EAAW;AACrB,IAAA,IAAI,IAAKF,CAAAA,QAAL,CAAc9Z,IAAd,CAAJ,EAAyB;AACvB;AACA,MAAA,IAAA,CAAK8Z,QAAL,CAAc9Z,IAAd,IAAsB,IAAK8Z,CAAAA,QAAL,CAAc9Z,IAAd,CAAA,CAAoBrH,MAApB,CAA2BA,MAAM,IAAKA,MAAM,CAACqhB,EAAP,KAAcA,EAApD,CAAtB,CAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKhhB,IAAT,EAAe;AACb,MAAA,IAAA,CAAKA,IAAL,CAAUohB,YAAV,CAAuBpa,IAAvB,EAA6Bga,EAA7B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEtO,EAAAA,YAAY,CAAC1L,IAAD,EAAO,GAAGqa,IAAV,EAAgB;AAAA,IAAA,IAAA,oBAAA,CAAA;;AAC1B,IAAA,CAAA,oBAAA,GAAA,IAAA,CAAKP,QAAL,CAAc9Z,IAAd,uEAAqB7H,OAArB,CAA8BQ,MAAD,IAAY;AACvC;AACA0hB,MAAAA,IAAI,CAAC,CAAD,CAAJ,GAAU1hB,MAAM,CAACqhB,EAAP,CAAUM,KAAV,CAAgB,IAAhB,EAAsBD,IAAtB,CAAV,CAAA;KAFF,CAAA,CAAA;IAIA,OAAOA,IAAI,CAAC,CAAD,CAAX,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEtR,EAAAA,EAAE,CAAC/I,IAAD,EAAOga,EAAP,EAAW;AAAA,IAAA,IAAA,qBAAA,EAAA,WAAA,CAAA;;AACX,IAAA,IAAI,CAAC,IAAKH,CAAAA,UAAL,CAAgB7Z,IAAhB,CAAL,EAA4B;AAC1B,MAAA,IAAA,CAAK6Z,UAAL,CAAgB7Z,IAAhB,CAAA,GAAwB,EAAxB,CAAA;AACD,KAAA;;IACD,CAAK6Z,qBAAAA,GAAAA,IAAAA,CAAAA,UAAL,CAAgB7Z,IAAhB,CAAA,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAuBpH,IAAvB,CAA4BohB,EAA5B,EAJW;AAOX;AACA;;AACA,IAAA,CAAA,WAAA,GAAA,IAAA,CAAKhhB,IAAL,MAAW+P,IAAAA,IAAAA,WAAAA,KAAAA,KAAAA,CAAAA,IAAAA,WAAAA,CAAAA,EAAX,CAAc/I,IAAd,EAAoBga,EAApB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEO,EAAAA,GAAG,CAACva,IAAD,EAAOga,EAAP,EAAW;AAAA,IAAA,IAAA,WAAA,CAAA;;AACZ,IAAA,IAAI,IAAKH,CAAAA,UAAL,CAAgB7Z,IAAhB,CAAJ,EAA2B;AACzB;AACA,MAAA,IAAA,CAAK6Z,UAAL,CAAgB7Z,IAAhB,CAAwB,GAAA,IAAA,CAAK6Z,UAAL,CAAgB7Z,IAAhB,CAAsBrH,CAAAA,MAAtB,CAA6Bb,QAAQ,IAAKkiB,EAAE,KAAKliB,QAAjD,CAAxB,CAAA;AACD,KAAA;;AAED,IAAA,CAAA,WAAA,GAAA,IAAA,CAAKkB,IAAL,MAAWuhB,IAAAA,IAAAA,WAAAA,KAAAA,KAAAA,CAAAA,IAAAA,WAAAA,CAAAA,GAAX,CAAeva,IAAf,EAAqBga,EAArB,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEvf,EAAAA,QAAQ,CAACuF,IAAD,EAAO0Z,OAAP,EAAgB;AAAA,IAAA,IAAA,sBAAA,CAAA;;IACtB,IAAI,IAAA,CAAK1gB,IAAT,EAAe;MACb,OAAO,IAAA,CAAKA,IAAL,CAAUyB,QAAV,CAAmBuF,IAAnB,EAAyB0Z,OAAzB,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,MAAMnT,KAAK;AAAG;AAAkC,IAAA,IAAIkT,eAAJ,CAAoBzZ,IAApB,EAA0B0Z,OAA1B,CAAhD,CAAA;AAEA,IAAA,CAAA,sBAAA,GAAA,IAAA,CAAKG,UAAL,CAAgB7Z,IAAhB,2EAAuB7H,OAAvB,CAAgCL,QAAD,IAAc;AAC3CA,MAAAA,QAAQ,CAACwP,IAAT,CAAc,IAAd,EAAoBf,KAApB,CAAA,CAAA;KADF,CAAA,CAAA;AAIA,IAAA,OAAOA,KAAP,CAAA;AACD,GAAA;;AAnHa;;ACpOhB,MAAMiU,WAAN,CAAkB;AAChB;AACF;AACA;AACA;AACE/iB,EAAAA,WAAW,CAACgjB,QAAD,EAAWxd,SAAX,EAAsB;AAC/B;AACA;;AACA;AACA,IAAA,IAAA,CAAK+B,OAAL,GAAehN,aAAa,CAC1B,kCAD0B,EAE1ByoB,QAAQ,GAAG,KAAH,GAAW,KAFO,EAG1Bxd,SAH0B,CAA5B,CAAA;;AAMA,IAAA,IAAIwd,QAAJ,EAAc;AACZ,MAAA,MAAMC,KAAK;AAAG;AAAiC,MAAA,IAAA,CAAK1b,OAApD,CAAA;MACA0b,KAAK,CAACC,QAAN,GAAiB,OAAjB,CAAA;MACAD,KAAK,CAACE,GAAN,GAAY,EAAZ,CAAA;MACAF,KAAK,CAACG,GAAN,GAAYJ,QAAZ,CAAA;AACAC,MAAAA,KAAK,CAAC/M,YAAN,CAAmB,MAAnB,EAA2B,cAA3B,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK3O,OAAL,CAAa2O,YAAb,CAA0B,aAA1B,EAAyC,MAAzC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACE9O,EAAAA,gBAAgB,CAACpK,KAAD,EAAQC,MAAR,EAAgB;IAC9B,IAAI,CAAC,IAAKsK,CAAAA,OAAV,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKA,OAAL,CAAa9M,OAAb,KAAyB,KAA7B,EAAoC;AAClC;AACA;AACA;AACAoC,MAAAA,cAAc,CAAC,IAAK0K,CAAAA,OAAN,EAAe,GAAf,EAAoB,MAApB,CAAd,CAAA;AACA,MAAA,IAAA,CAAKA,OAAL,CAAalL,KAAb,CAAmB6J,eAAnB,GAAqC,KAArC,CAAA;AACA,MAAA,IAAA,CAAKqB,OAAL,CAAalL,KAAb,CAAmBC,SAAnB,GAA+BL,iBAAiB,CAAC,CAAD,EAAI,CAAJ,EAAOe,KAAK,GAAG,GAAf,CAAhD,CAAA;AACD,KAPD,MAOO;AACLH,MAAAA,cAAc,CAAC,IAAK0K,CAAAA,OAAN,EAAevK,KAAf,EAAsBC,MAAtB,CAAd,CAAA;AACD,KAAA;AACF,GAAA;;AAED4J,EAAAA,OAAO,GAAG;AAAA,IAAA,IAAA,aAAA,CAAA;;AACR,IAAA,IAAA,CAAA,aAAA,GAAI,IAAKU,CAAAA,OAAT,MAAI,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,IAAA,aAAA,CAAc8b,UAAlB,EAA8B;MAC5B,IAAK9b,CAAAA,OAAL,CAAa/G,MAAb,EAAA,CAAA;AACD,KAAA;;IACD,IAAK+G,CAAAA,OAAL,GAAe,IAAf,CAAA;AACD,GAAA;;AApDe;;ACClB;;AACA;;AACA;;AACA;;AAEA,MAAM+b,OAAN,CAAc;AACZ;AACF;AACA;AACA;AACA;AACEtjB,EAAAA,WAAW,CAAC+B,QAAD,EAAW0f,QAAX,EAAqBzf,KAArB,EAA4B;IACrC,IAAKyf,CAAAA,QAAL,GAAgBA,QAAhB,CAAA;IACA,IAAKre,CAAAA,IAAL,GAAYrB,QAAZ,CAAA;IACA,IAAKC,CAAAA,KAAL,GAAaA,KAAb,CAAA;AAEA;;IACA,IAAKuF,CAAAA,OAAL,GAAenM,SAAf,CAAA;AACA;;IACA,IAAKkM,CAAAA,WAAL,GAAmBlM,SAAnB,CAAA;AACA;;IACA,IAAKsH,CAAAA,KAAL,GAAatH,SAAb,CAAA;IAEA,IAAKmoB,CAAAA,mBAAL,GAA2B,CAA3B,CAAA;IACA,IAAKC,CAAAA,oBAAL,GAA4B,CAA5B,CAAA;AAEA,IAAA,IAAA,CAAKxmB,KAAL,GAAauF,MAAM,CAAC,IAAKa,CAAAA,IAAL,CAAUtG,CAAX,CAAN,IAAuByF,MAAM,CAAC,IAAKa,CAAAA,IAAL,CAAUpG,KAAX,CAA7B,IAAkD,CAA/D,CAAA;AACA,IAAA,IAAA,CAAKC,MAAL,GAAcsF,MAAM,CAAC,IAAKa,CAAAA,IAAL,CAAUrG,CAAX,CAAN,IAAuBwF,MAAM,CAAC,IAAKa,CAAAA,IAAL,CAAUnG,MAAX,CAA7B,IAAmD,CAAjE,CAAA;IAEA,IAAKwmB,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAK3c,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAK4c,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA;;AACA,IAAA,IAAA,CAAKC,KAAL,GAAa9lB,UAAU,CAACC,IAAxB,CAAA;;AAEA,IAAA,IAAI,IAAKsF,CAAAA,IAAL,CAAUhD,IAAd,EAAoB;AAClB,MAAA,IAAA,CAAKA,IAAL,GAAY,IAAKgD,CAAAA,IAAL,CAAUhD,IAAtB,CAAA;AACD,KAFD,MAEO,IAAI,IAAA,CAAKgD,IAAL,CAAUggB,GAAd,EAAmB;MACxB,IAAKhjB,CAAAA,IAAL,GAAY,OAAZ,CAAA;AACD,KAFM,MAEA;MACL,IAAKA,CAAAA,IAAL,GAAY,MAAZ,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKqhB,QAAL,CAAcze,QAAd,CAAuB,aAAvB,EAAsC;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAAjD,CAAA,CAAA;AACD,GAAA;;AAEDue,EAAAA,iBAAiB,GAAG;AAClB,IAAA,IAAI,KAAKtc,WAAL,IAAoB,CAAC,IAAKuc,CAAAA,eAAL,EAAzB,EAAiD;AAC/C;AACAjQ,MAAAA,UAAU,CAAC,MAAM;QACf,IAAI,IAAA,CAAKtM,WAAT,EAAsB;UACpB,IAAKA,CAAAA,WAAL,CAAiBT,OAAjB,EAAA,CAAA;UACA,IAAKS,CAAAA,WAAL,GAAmBlM,SAAnB,CAAA;AACD,SAAA;OAJO,EAKP,IALO,CAAV,CAAA;AAMD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEgL,EAAAA,IAAI,CAAC0d,MAAD,EAASC,MAAT,EAAiB;AACnB,IAAA,IAAI,KAAKrhB,KAAL,IAAc,IAAKshB,CAAAA,cAAL,EAAlB,EAAyC;MACvC,IAAI,CAAC,IAAK1c,CAAAA,WAAV,EAAuB;QACrB,MAAM2c,cAAc,GAAG,IAAKxC,CAAAA,QAAL,CAAcxN,YAAd,CACrB,gBADqB;AAGrB;AACC,QAAA,IAAA,CAAK7Q,IAAL,CAAU8gB,IAAV,IAAkB,IAAA,CAAKxhB,KAAL,CAAWwC,YAA9B,GAA8C,IAAA,CAAK9B,IAAL,CAAU8gB,IAAxD,GAA+D,KAJ1C,EAKrB,IALqB,CAAvB,CAAA;QAOA,IAAK5c,CAAAA,WAAL,GAAmB,IAAIyb,WAAJ,CACjBkB,cADiB,EAEjB,IAAKvhB,CAAAA,KAAL,CAAW8C,SAFM,CAAnB,CAAA;AAID,OAZD,MAYO;AACL,QAAA,MAAM2e,aAAa,GAAG,IAAA,CAAK7c,WAAL,CAAiBC,OAAvC,CADK;;AAGL,QAAA,IAAI4c,aAAa,IAAI,CAACA,aAAa,CAACC,aAApC,EAAmD;AACjD,UAAA,IAAA,CAAK1hB,KAAL,CAAW8C,SAAX,CAAqB6e,OAArB,CAA6BF,aAA7B,CAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;;AAED,IAAA,IAAI,IAAK5c,CAAAA,OAAL,IAAgB,CAACwc,MAArB,EAA6B;AAC3B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKtC,QAAL,CAAcze,QAAd,CAAuB,aAAvB,EAAsC;AAAEqC,MAAAA,OAAO,EAAE,IAAX;AAAiBye,MAAAA,MAAAA;KAAvD,CAAA,CAAiEld,gBAArE,EAAuF;AACrF,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK0d,cAAL,EAAJ,EAA2B;MACzB,IAAK/c,CAAAA,OAAL,GAAehN,aAAa,CAAC,WAAD,EAAc,KAAd,CAA5B,CADyB;AAGzB;;MACA,IAAI,IAAA,CAAKgpB,mBAAT,EAA8B;QAC5B,IAAKgB,CAAAA,SAAL,CAAeT,MAAf,CAAA,CAAA;AACD,OAAA;AACF,KAPD,MAOO;AACL,MAAA,IAAA,CAAKvc,OAAL,GAAehN,aAAa,CAAC,eAAD,EAAkB,KAAlB,CAA5B,CAAA;MACA,IAAKgN,CAAAA,OAAL,CAAaoW,SAAb,GAAyB,KAAKva,IAAL,CAAUia,IAAV,IAAkB,EAA3C,CAAA;AACD,KAAA;;AAED,IAAA,IAAI0G,MAAM,IAAI,IAAKrhB,CAAAA,KAAnB,EAA0B;AACxB,MAAA,IAAA,CAAKA,KAAL,CAAW2D,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEke,SAAS,CAACT,MAAD,EAAS;AAAA,IAAA,IAAA,cAAA,EAAA,cAAA,CAAA;;AAChB,IAAA,IAAI,CAAC,IAAA,CAAKQ,cAAL,EAAD,IACC,CAAC,IAAA,CAAK/c,OADP,IAEC,KAAKka,QAAL,CAAcze,QAAd,CAAuB,kBAAvB,EAA2C;AAAEqC,MAAAA,OAAO,EAAE,IAAX;AAAiBye,MAAAA,MAAAA;KAA5D,CAAA,CAAsEld,gBAF3E,EAE6F;AAC3F,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM4d,YAAY;AAAG;AAA+B,IAAA,IAAA,CAAKjd,OAAzD,CAAA;AAEA,IAAA,IAAA,CAAKkd,iBAAL,EAAA,CAAA;;AAEA,IAAA,IAAI,IAAKrhB,CAAAA,IAAL,CAAUshB,MAAd,EAAsB;AACpBF,MAAAA,YAAY,CAACE,MAAb,GAAsB,IAAKthB,CAAAA,IAAL,CAAUshB,MAAhC,CAAA;AACD,KAAA;;IAEDF,YAAY,CAACpB,GAAb,GAAmB,CAAA,cAAA,GAAA,IAAA,CAAKhgB,IAAL,CAAUggB,GAA7B,2DAAoC,EAApC,CAAA;IACAoB,YAAY,CAACrB,GAAb,GAAmB,CAAA,cAAA,GAAA,IAAA,CAAK/f,IAAL,CAAU+f,GAA7B,2DAAoC,EAApC,CAAA;AAEA,IAAA,IAAA,CAAKQ,KAAL,GAAa9lB,UAAU,CAACE,OAAxB,CAAA;;IAEA,IAAIymB,YAAY,CAACjnB,QAAjB,EAA2B;AACzB,MAAA,IAAA,CAAKonB,QAAL,EAAA,CAAA;AACD,KAFD,MAEO;MACLH,YAAY,CAAC7mB,MAAb,GAAsB,MAAM;AAC1B,QAAA,IAAA,CAAKgnB,QAAL,EAAA,CAAA;OADF,CAAA;;MAIAH,YAAY,CAAC5mB,OAAb,GAAuB,MAAM;AAC3B,QAAA,IAAA,CAAKgnB,OAAL,EAAA,CAAA;OADF,CAAA;AAGD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEC,QAAQ,CAACniB,KAAD,EAAQ;IACd,IAAKA,CAAAA,KAAL,GAAaA,KAAb,CAAA;IACA,IAAKoE,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACA,IAAA,IAAA,CAAK2a,QAAL,GAAgB/e,KAAK,CAACnB,IAAtB,CAHc;AAMf,GAAA;AAED;AACF;AACA;;;AACEojB,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAA,CAAKhB,KAAL,GAAa9lB,UAAU,CAACG,MAAxB,CAAA;;AAEA,IAAA,IAAI,IAAK0E,CAAAA,KAAL,IAAc,IAAA,CAAK6E,OAAvB,EAAgC;AAC9B,MAAA,IAAA,CAAKka,QAAL,CAAcze,QAAd,CAAuB,cAAvB,EAAuC;QAAEN,KAAK,EAAE,KAAKA,KAAd;AAAqB2C,QAAAA,OAAO,EAAE,IAAA;AAA9B,OAAvC,EAD8B;;AAI9B,MAAA,IAAI,IAAK3C,CAAAA,KAAL,CAAWoC,QAAX,IACG,IAAKpC,CAAAA,KAAL,CAAWgD,aADd,IAEG,CAAC,IAAA,CAAK6B,OAAL,CAAa8b,UAFrB,EAEiC;AAC/B,QAAA,IAAA,CAAKpd,MAAL,EAAA,CAAA;AACA,QAAA,IAAA,CAAKvD,KAAL,CAAW2D,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAI,IAAKsd,CAAAA,KAAL,KAAe9lB,UAAU,CAACG,MAA1B,IAAoC,IAAA,CAAK2lB,KAAL,KAAe9lB,UAAU,CAACI,KAAlE,EAAyE;AACvE,QAAA,IAAA,CAAK2lB,iBAAL,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEgB,EAAAA,OAAO,GAAG;AACR,IAAA,IAAA,CAAKjB,KAAL,GAAa9lB,UAAU,CAACI,KAAxB,CAAA;;IAEA,IAAI,IAAA,CAAKyE,KAAT,EAAgB;AACd,MAAA,IAAA,CAAKoiB,YAAL,EAAA,CAAA;AACA,MAAA,IAAA,CAAKrD,QAAL,CAAcze,QAAd,CAAuB,cAAvB,EAAuC;QAAEN,KAAK,EAAE,KAAKA,KAAd;AAAqBqiB,QAAAA,OAAO,EAAE,IAA9B;AAAoC1f,QAAAA,OAAO,EAAE,IAAA;OAApF,CAAA,CAAA;AACA,MAAA,IAAA,CAAKoc,QAAL,CAAcze,QAAd,CAAuB,WAAvB,EAAoC;QAAEN,KAAK,EAAE,KAAKA,KAAd;AAAqB2C,QAAAA,OAAO,EAAE,IAAA;OAAlE,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE6Z,EAAAA,SAAS,GAAG;AACV,IAAA,OAAO,IAAKuC,CAAAA,QAAL,CAAcxN,YAAd,CACL,kBADK,EAEL,IAAK0P,CAAAA,KAAL,KAAe9lB,UAAU,CAACE,OAFrB,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACEgnB,EAAAA,OAAO,GAAG;AACR,IAAA,OAAO,IAAKpB,CAAAA,KAAL,KAAe9lB,UAAU,CAACI,KAAjC,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEqmB,EAAAA,cAAc,GAAG;IACf,OAAO,IAAA,CAAKlkB,IAAL,KAAc,OAArB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEgH,EAAAA,gBAAgB,CAACpK,KAAD,EAAQC,MAAR,EAAgB;IAC9B,IAAI,CAAC,IAAKsK,CAAAA,OAAV,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKD,WAAT,EAAsB;AACpB,MAAA,IAAA,CAAKA,WAAL,CAAiBF,gBAAjB,CAAkCpK,KAAlC,EAAyCC,MAAzC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKwkB,QAAL,CAAcze,QAAd,CACF,eADE,EAEF;AAAEqC,MAAAA,OAAO,EAAE,IAAX;MAAiBrI,KAAjB;AAAwBC,MAAAA,MAAAA;KAFtB,CAAA,CAEgC2J,gBAFpC,EAGE;AACA,MAAA,OAAA;AACD,KAAA;;AAED/J,IAAAA,cAAc,CAAC,IAAK0K,CAAAA,OAAN,EAAevK,KAAf,EAAsBC,MAAtB,CAAd,CAAA;;AAEA,IAAA,IAAI,KAAKqnB,cAAL,EAAA,IAAyB,CAAC,IAAKS,CAAAA,OAAL,EAA9B,EAA8C;AAC5C,MAAA,MAAMC,mBAAmB,GAAI,CAAC,IAAKzB,CAAAA,mBAAN,IAA6BvmB,KAA1D,CAAA;MAEA,IAAKumB,CAAAA,mBAAL,GAA2BvmB,KAA3B,CAAA;MACA,IAAKwmB,CAAAA,oBAAL,GAA4BvmB,MAA5B,CAAA;;AAEA,MAAA,IAAI+nB,mBAAJ,EAAyB;QACvB,IAAKT,CAAAA,SAAL,CAAe,KAAf,CAAA,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAA,CAAKE,iBAAL,EAAA,CAAA;AACD,OAAA;;MAED,IAAI,IAAA,CAAK/hB,KAAT,EAAgB;AACd,QAAA,IAAA,CAAK+e,QAAL,CAAcze,QAAd,CACE,iBADF,EAEE;UAAEN,KAAK,EAAE,KAAKA,KAAd;UAAqB1F,KAArB;UAA4BC,MAA5B;AAAoCoI,UAAAA,OAAO,EAAE,IAAA;SAF/C,CAAA,CAAA;AAID,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEwC,EAAAA,UAAU,GAAG;AACX,IAAA,OAAO,KAAK4Z,QAAL,CAAcxN,YAAd,CACL,mBADK,EAEL,IAAKqQ,CAAAA,cAAL,EAA0B,IAAA,IAAA,CAAKX,KAAL,KAAe9lB,UAAU,CAACI,KAF/C,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACEwmB,EAAAA,iBAAiB,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA,IAAA,IAAI,CAAC,IAAA,CAAKH,cAAL,EAAD,IAA0B,CAAC,IAAA,CAAK/c,OAAhC,IAA2C,CAAC,IAAA,CAAKnE,IAAL,CAAUshB,MAA1D,EAAkE;AAChE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMO,KAAK;AAAG;AAA+B,IAAA,IAAA,CAAK1d,OAAlD,CAAA;AACA,IAAA,MAAM2d,UAAU,GAAG,IAAKzD,CAAAA,QAAL,CAAcxN,YAAd,CACjB,kBADiB,EAEjB,IAAA,CAAKsP,mBAFY,EAGjB,IAHiB,CAAnB,CAAA;;AAMA,IAAA,IACE,CAAC0B,KAAK,CAACE,OAAN,CAAcC,eAAf,IACGF,UAAU,GAAGG,QAAQ,CAACJ,KAAK,CAACE,OAAN,CAAcC,eAAf,EAAgC,EAAhC,CAF1B,EAGE;AACAH,MAAAA,KAAK,CAACK,KAAN,GAAcJ,UAAU,GAAG,IAA3B,CAAA;MACAD,KAAK,CAACE,OAAN,CAAcC,eAAd,GAAgCG,MAAM,CAACL,UAAD,CAAtC,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACElB,EAAAA,cAAc,GAAG;AACf,IAAA,OAAO,IAAKvC,CAAAA,QAAL,CAAcxN,YAAd,CACL,uBADK,EAEL,IAAA,CAAKqQ,cAAL,EAFK,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACEkB,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAI,KAAK/D,QAAL,CAAcze,QAAd,CAAuB,iBAAvB,EAA0C;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAArD,CAAA,CAA6DuB,gBAAjE,EAAmF;AACjF,MAAA,OAAA;AACD,KAAA;;IAED,IAAKR,CAAAA,IAAL,CAAU,IAAV,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEyd,EAAAA,eAAe,GAAG;AAChB,IAAA,OAAO,IAAKpC,CAAAA,QAAL,CAAcxN,YAAd,CACL,sBADK,EAEL,IAAA,CAAKiL,SAAL,EAFK,EAGL,IAHK,CAAP,CAAA;AAKD,GAAA;AAED;AACF;AACA;;;AACErY,EAAAA,OAAO,GAAG;IACR,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKpE,CAAAA,KAAL,GAAatH,SAAb,CAAA;;AAEA,IAAA,IAAI,KAAKqmB,QAAL,CAAcze,QAAd,CAAuB,gBAAvB,EAAyC;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAApD,CAAA,CAA4DuB,gBAAhE,EAAkF;AAChF,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKpG,MAAL,EAAA,CAAA;;IAEA,IAAI,IAAA,CAAK8G,WAAT,EAAsB;MACpB,IAAKA,CAAAA,WAAL,CAAiBT,OAAjB,EAAA,CAAA;MACA,IAAKS,CAAAA,WAAL,GAAmBlM,SAAnB,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKkpB,CAAAA,cAAL,EAAyB,IAAA,IAAA,CAAK/c,OAAlC,EAA2C;AACzC,MAAA,IAAA,CAAKA,OAAL,CAAa5J,MAAb,GAAsB,IAAtB,CAAA;AACA,MAAA,IAAA,CAAK4J,OAAL,CAAa3J,OAAb,GAAuB,IAAvB,CAAA;MACA,IAAK2J,CAAAA,OAAL,GAAenM,SAAf,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE0pB,EAAAA,YAAY,GAAG;IACb,IAAI,IAAA,CAAKpiB,KAAT,EAAgB;AAAA,MAAA,IAAA,qBAAA,EAAA,sBAAA,CAAA;;AACd,MAAA,IAAI+iB,UAAU,GAAGlrB,aAAa,CAAC,iBAAD,EAAoB,KAApB,CAA9B,CAAA;MACAkrB,UAAU,CAAClG,SAAX,GAAA,CAAA,qBAAA,GAAA,CAAA,sBAAA,GAAuB,IAAKkC,CAAAA,QAAL,CAAcngB,OAArC,MAAuB,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAAuBokB,QAA9C,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAA0D,EAA1D,CAAA;MACAD,UAAU;AAAG;MAA+B,IAAKhE,CAAAA,QAAL,CAAcxN,YAAd,CAC1C,qBAD0C,EAE1CwR,UAF0C,EAG1C,IAH0C,CAA5C,CAAA;AAKA,MAAA,IAAA,CAAKle,OAAL,GAAehN,aAAa,CAAC,yCAAD,EAA4C,KAA5C,CAA5B,CAAA;AACA,MAAA,IAAA,CAAKgN,OAAL,CAAa1M,WAAb,CAAyB4qB,UAAzB,CAAA,CAAA;AACA,MAAA,IAAA,CAAK/iB,KAAL,CAAW8C,SAAX,CAAqB+Z,SAArB,GAAiC,EAAjC,CAAA;AACA,MAAA,IAAA,CAAK7c,KAAL,CAAW8C,SAAX,CAAqB3K,WAArB,CAAiC,KAAK0M,OAAtC,CAAA,CAAA;AACA,MAAA,IAAA,CAAK7E,KAAL,CAAW2D,iBAAX,CAA6B,IAA7B,CAAA,CAAA;AACA,MAAA,IAAA,CAAKud,iBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE3d,EAAAA,MAAM,GAAG;AACP,IAAA,IAAI,KAAKwd,UAAL,IAAmB,CAAC,IAAA,CAAKlc,OAA7B,EAAsC;AACpC,MAAA,OAAA;AACD,KAAA;;IAED,IAAKkc,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;AAEA,IAAA,IAAI,KAAKE,KAAL,KAAe9lB,UAAU,CAACI,KAA9B,EAAqC;AACnC,MAAA,IAAA,CAAK6mB,YAAL,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKrD,QAAL,CAAcze,QAAd,CAAuB,eAAvB,EAAwC;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAAnD,CAAA,CAA2DuB,gBAA/D,EAAiF;AAC/E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM+e,cAAc,IAAI,QAAY,IAAA,IAAA,CAAKpe,OAArB,CAApB,CAAA;;IAEA,IAAI,IAAA,CAAK+c,cAAL,EAAJ,EAA2B;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,IAAIqB,cAAc,IAAI,IAAKjjB,CAAAA,KAAvB,KAAiC,CAAC,IAAA,CAAKA,KAAL,CAAWoC,QAAZ,IAAwBzF,QAAQ,EAAjE,CAAJ,EAA0E;AACxE,QAAA,IAAA,CAAKqkB,UAAL,GAAkB,IAAlB,CADwE;AAGxE;;AACA;;AACC,QAAA,IAAA,CAAKnc,OAAN,CAAelK,MAAf,EAAA,CAAwBC,KAAxB,CAA8B,MAAM,EAApC,CAAA,CAAwCsoB,OAAxC,CAAgD,MAAM;UACpD,IAAKlC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACA,UAAA,IAAA,CAAKmC,WAAL,EAAA,CAAA;SAFF,CAAA,CAAA;AAID,OATD,MASO;AACL,QAAA,IAAA,CAAKA,WAAL,EAAA,CAAA;AACD,OAAA;KAxBH,MAyBO,IAAI,IAAA,CAAKnjB,KAAL,IAAc,CAAC,IAAK6E,CAAAA,OAAL,CAAa8b,UAAhC,EAA4C;AACjD,MAAA,IAAA,CAAK3gB,KAAL,CAAW8C,SAAX,CAAqB3K,WAArB,CAAiC,KAAK0M,OAAtC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACExB,EAAAA,QAAQ,GAAG;AACT,IAAA,IAAI,KAAK0b,QAAL,CAAcze,QAAd,CAAuB,iBAAvB,EAA0C;AAAEqC,MAAAA,OAAO,EAAE,IAAA;AAAX,KAA1C,EAA6DuB,gBAA7D,IACC,CAAC,IAAA,CAAKlE,KADX,EACkB;AAChB,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK4hB,cAAL,EAAyB,IAAA,IAAA,CAAKZ,UAA9B,IAA4C,CAACrkB,QAAQ,EAAzD,EAA6D;AAC3D;AACA;AACA,MAAA,IAAA,CAAKwmB,WAAL,EAAA,CAAA;AACD,KAJD,MAIO,IAAI,IAAKd,CAAAA,OAAL,EAAJ,EAAoB;AACzB,MAAA,IAAA,CAAK3e,IAAL,CAAU,KAAV,EAAiB,IAAjB,EADyB;AAE1B,KAAA;;AAED,IAAA,IAAI,IAAK1D,CAAAA,KAAL,CAAW+C,aAAf,EAA8B;MAC5B,IAAK/C,CAAAA,KAAL,CAAW+C,aAAX,CAAyByQ,YAAzB,CAAsC,aAAtC,EAAqD,OAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACElQ,EAAAA,UAAU,GAAG;AACX,IAAA,IAAA,CAAKyb,QAAL,CAAcze,QAAd,CAAuB,mBAAvB,EAA4C;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAAvD,CAAA,CAAA;;AACA,IAAA,IAAI,KAAK3C,KAAL,IAAc,KAAKA,KAAL,CAAW+C,aAA7B,EAA4C;MAC1C,IAAK/C,CAAAA,KAAL,CAAW+C,aAAX,CAAyByQ,YAAzB,CAAsC,aAAtC,EAAqD,MAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAGD;AACF;AACA;;;AACE1V,EAAAA,MAAM,GAAG;IACP,IAAKijB,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;AAEA,IAAA,IAAI,KAAKhC,QAAL,CAAcze,QAAd,CAAuB,eAAvB,EAAwC;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAAnD,CAAA,CAA2DuB,gBAA/D,EAAiF;AAC/E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKW,OAAL,IAAgB,KAAKA,OAAL,CAAa8b,UAAjC,EAA6C;MAC3C,IAAK9b,CAAAA,OAAL,CAAa/G,MAAb,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAK8G,WAAL,IAAoB,KAAKA,WAAL,CAAiBC,OAAzC,EAAkD;AAChD,MAAA,IAAA,CAAKD,WAAL,CAAiBC,OAAjB,CAAyB/G,MAAzB,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACEqlB,EAAAA,WAAW,GAAG;IACZ,IAAI,CAAC,IAAKpC,CAAAA,UAAV,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKhC,QAAL,CAAcze,QAAd,CAAuB,oBAAvB,EAA6C;AAAEqC,MAAAA,OAAO,EAAE,IAAA;KAAxD,CAAA,CAAgEuB,gBAApE,EAAsF;AACpF,MAAA,OAAA;AACD,KAPW;;;IAUZ,IAAI,IAAA,CAAKlE,KAAL,IAAc,IAAK6E,CAAAA,OAAnB,IAA8B,CAAC,IAAKA,CAAAA,OAAL,CAAa8b,UAAhD,EAA4D;AAC1D,MAAA,IAAA,CAAK3gB,KAAL,CAAW8C,SAAX,CAAqB3K,WAArB,CAAiC,KAAK0M,OAAtC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKoc,CAAAA,KAAL,KAAe9lB,UAAU,CAACG,MAA1B,IAAoC,IAAA,CAAK2lB,KAAL,KAAe9lB,UAAU,CAACI,KAAlE,EAAyE;AACvE,MAAA,IAAA,CAAK2lB,iBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AA5fW;;ACLd;;AACA;;AACA;;AACA;;AACA;;AAEA,MAAMkC,mBAAmB,GAAG,CAA5B,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,YAAT,CAAsBhkB,QAAtB,EAAgC0f,QAAhC,EAA0Czf,KAA1C,EAAiD;EACtD,MAAMqD,OAAO,GAAGoc,QAAQ,CAACuE,qBAAT,CAA+BjkB,QAA/B,EAAyCC,KAAzC,CAAhB,CAAA;AACA;;AACA,EAAA,IAAIikB,SAAJ,CAAA;EAEA,MAAM;AAAE3kB,IAAAA,OAAAA;GAAYmgB,GAAAA,QAApB,CALsD;AAQtD;;AACA,EAAA,IAAIngB,OAAJ,EAAa;IACX2kB,SAAS,GAAG,IAAIxiB,SAAJ,CAAcnC,OAAd,EAAuBS,QAAvB,EAAiC,CAAC,CAAlC,CAAZ,CAAA;AAEA,IAAA,IAAID,YAAJ,CAAA;;IACA,IAAI2f,QAAQ,CAAClgB,IAAb,EAAmB;AACjBO,MAAAA,YAAY,GAAG2f,QAAQ,CAAClgB,IAAT,CAAcO,YAA7B,CAAA;AACD,KAFD,MAEO;AACLA,MAAAA,YAAY,GAAGT,eAAe,CAACC,OAAD,EAAUmgB,QAAV,CAA9B,CAAA;AACD,KAAA;;IAED,MAAMpe,WAAW,GAAGb,cAAc,CAAClB,OAAD,EAAUQ,YAAV,EAAwBC,QAAxB,EAAkCC,KAAlC,CAAlC,CAAA;IACAikB,SAAS,CAACpjB,MAAV,CAAiBwC,OAAO,CAACrI,KAAzB,EAAgCqI,OAAO,CAACpI,MAAxC,EAAgDoG,WAAhD,CAAA,CAAA;AACD,GAAA;;AAEDgC,EAAAA,OAAO,CAACmgB,QAAR,EAAA,CAAA;;AAEA,EAAA,IAAIS,SAAJ,EAAe;IACb5gB,OAAO,CAAC+B,gBAAR,CACE7L,IAAI,CAAC2qB,IAAL,CAAU7gB,OAAO,CAACrI,KAAR,GAAgBipB,SAAS,CAACniB,OAApC,CADF,EAEEvI,IAAI,CAAC2qB,IAAL,CAAU7gB,OAAO,CAACpI,MAAR,GAAiBgpB,SAAS,CAACniB,OAArC,CAFF,CAAA,CAAA;AAID,GAAA;;AAED,EAAA,OAAOuB,OAAP,CAAA;AACD,CAAA;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAAS8gB,aAAT,CAAuBnkB,KAAvB,EAA8Byf,QAA9B,EAAwC;AAC7C,EAAA,MAAM1f,QAAQ,GAAG0f,QAAQ,CAAC2E,WAAT,CAAqBpkB,KAArB,CAAjB,CAAA;;AAEA,EAAA,IAAIyf,QAAQ,CAACze,QAAT,CAAkB,eAAlB,EAAmC;IAAEhB,KAAF;AAASD,IAAAA,QAAAA;GAA5C,CAAA,CAAwD6E,gBAA5D,EAA8E;AAC5E,IAAA,OAAA;AACD,GAAA;;AAED,EAAA,OAAOmf,YAAY,CAAChkB,QAAD,EAAW0f,QAAX,EAAqBzf,KAArB,CAAnB,CAAA;AACD,CAAA;;AAED,MAAMqkB,aAAN,CAAoB;AAClB;AACF;AACA;EACErmB,WAAW,CAACuB,IAAD,EAAO;AAChB,IAAA,IAAA,CAAKA,IAAL,GAAYA,IAAZ,CADgB;;IAGhB,IAAK+kB,CAAAA,KAAL,GAAa/qB,IAAI,CAACS,GAAL,CACXuF,IAAI,CAACD,OAAL,CAAailB,OAAb,CAAqB,CAArB,IAA0BhlB,IAAI,CAACD,OAAL,CAAailB,OAAb,CAAqB,CAArB,CAA1B,GAAoD,CADzC,EAEXT,mBAFW,CAAb,CAAA;AAIA;;IACA,IAAKU,CAAAA,YAAL,GAAoB,EAApB,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACE7O,UAAU,CAAC/C,IAAD,EAAO;IACf,MAAM;AAAErT,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;;AAEA,IAAA,IAAIA,IAAI,CAACyB,QAAL,CAAc,UAAd,CAAA,CAA0B4D,gBAA9B,EAAgD;AAC9C,MAAA,OAAA;AACD,KAAA;;IAED,MAAM;AAAE2f,MAAAA,OAAAA;KAAYhlB,GAAAA,IAAI,CAACD,OAAzB,CAAA;IACA,MAAM+X,SAAS,GAAGzE,IAAI,KAAKxZ,SAAT,GAAqB,IAArB,GAA6BwZ,IAAI,IAAI,CAAvD,CAAA;IACA,IAAIqB,CAAJ,CATe;;AAYf,IAAA,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAIsQ,OAAO,CAAC,CAAD,CAAxB,EAA6BtQ,CAAC,EAA9B,EAAkC;AAChC,MAAA,IAAA,CAAKwQ,gBAAL,CAAsBllB,IAAI,CAACwD,SAAL,IAAkBsU,SAAS,GAAGpD,CAAH,GAAQ,CAACA,CAApC,CAAtB,CAAA,CAAA;AACD,KAdc;;;AAiBf,IAAA,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,IAAIsQ,OAAO,CAAC,CAAD,CAAxB,EAA6BtQ,CAAC,EAA9B,EAAkC;AAChC,MAAA,IAAA,CAAKwQ,gBAAL,CAAsBllB,IAAI,CAACwD,SAAL,IAAkBsU,SAAS,GAAI,CAACpD,CAAL,GAAUA,CAArC,CAAtB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;EACEwQ,gBAAgB,CAACC,YAAD,EAAe;IAC7B,MAAM1kB,KAAK,GAAG,IAAA,CAAKT,IAAL,CAAUqV,cAAV,CAAyB8P,YAAzB,CAAd,CAD6B;;AAG7B,IAAA,IAAIrhB,OAAO,GAAG,IAAA,CAAKshB,iBAAL,CAAuB3kB,KAAvB,CAAd,CAAA;;IACA,IAAI,CAACqD,OAAL,EAAc;AACZ;MACAA,OAAO,GAAG8gB,aAAa,CAACnkB,KAAD,EAAQ,IAAKT,CAAAA,IAAb,CAAvB,CAFY;;AAIZ,MAAA,IAAI8D,OAAJ,EAAa;QACX,IAAKuhB,CAAAA,UAAL,CAAgBvhB,OAAhB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;EACEE,iBAAiB,CAAC7C,KAAD,EAAQ;IACvB,IAAI2C,OAAO,GAAG,IAAKshB,CAAAA,iBAAL,CAAuBjkB,KAAK,CAACV,KAA7B,CAAd,CAAA;;IACA,IAAI,CAACqD,OAAL,EAAc;AACZ;AACAA,MAAAA,OAAO,GAAG,IAAA,CAAK9D,IAAL,CAAUykB,qBAAV,CAAgCtjB,KAAK,CAACU,IAAtC,EAA4CV,KAAK,CAACV,KAAlD,CAAV,CAAA;MACA,IAAK4kB,CAAAA,UAAL,CAAgBvhB,OAAhB,CAAA,CAAA;AACD,KANsB;;;IASvBA,OAAO,CAACwf,QAAR,CAAiBniB,KAAjB,CAAA,CAAA;AAEA,IAAA,OAAO2C,OAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACEuhB,UAAU,CAACvhB,OAAD,EAAU;AAClB;AACA,IAAA,IAAA,CAAKwhB,aAAL,CAAmBxhB,OAAO,CAACrD,KAA3B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKwkB,YAAL,CAAkBrlB,IAAlB,CAAuBkE,OAAvB,CAAA,CAAA;;AAEA,IAAA,IAAI,KAAKmhB,YAAL,CAAkB/R,MAAlB,GAA2B,IAAA,CAAK6R,KAApC,EAA2C;AACzC;MACA,MAAMQ,aAAa,GAAG,IAAKN,CAAAA,YAAL,CAAkBpS,SAAlB,CAA6B2S,IAAD,IAAU;QAC1D,OAAO,CAACA,IAAI,CAACtD,UAAN,IAAoB,CAACsD,IAAI,CAACjgB,QAAjC,CAAA;AACD,OAFqB,CAAtB,CAAA;;AAGA,MAAA,IAAIggB,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACxB,QAAA,MAAME,WAAW,GAAG,IAAKR,CAAAA,YAAL,CAAkBjS,MAAlB,CAAyBuS,aAAzB,EAAwC,CAAxC,CAA2C,CAAA,CAA3C,CAApB,CAAA;;AACAE,QAAAA,WAAW,CAACngB,OAAZ,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEggB,aAAa,CAAC7kB,KAAD,EAAQ;AACnB,IAAA,MAAM8kB,aAAa,GAAG,IAAKN,CAAAA,YAAL,CAAkBpS,SAAlB,CAA4B2S,IAAI,IAAIA,IAAI,CAAC/kB,KAAL,KAAeA,KAAnD,CAAtB,CAAA;;AACA,IAAA,IAAI8kB,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACxB,MAAA,IAAA,CAAKN,YAAL,CAAkBjS,MAAlB,CAAyBuS,aAAzB,EAAwC,CAAxC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;EACEH,iBAAiB,CAAC3kB,KAAD,EAAQ;AACvB,IAAA,OAAO,IAAKwkB,CAAAA,YAAL,CAAkBS,IAAlB,CAAuB5hB,OAAO,IAAIA,OAAO,CAACrD,KAAR,KAAkBA,KAApD,CAAP,CAAA;AACD,GAAA;;AAED6E,EAAAA,OAAO,GAAG;IACR,IAAK2f,CAAAA,YAAL,CAAkB9lB,OAAlB,CAA0B2E,OAAO,IAAIA,OAAO,CAACwB,OAAR,EAArC,CAAA,CAAA;;IACA,IAAK2f,CAAAA,YAAL,GAAoB,EAApB,CAAA;AACD,GAAA;;AAxHiB;;ACzEpB;;AACA;;AAEA;AACA;AACA;AACA;;AACA,MAAMU,cAAN,SAA6B/E,SAA7B,CAAuC;AACrC;AACF;AACA;AACA;AACA;AACE9L,EAAAA,WAAW,GAAG;AAAA,IAAA,IAAA,aAAA,CAAA;;IACZ,IAAI8Q,QAAQ,GAAG,CAAf,CAAA;AACA,IAAA,MAAMC,UAAU,GAAG,CAAA,aAAA,GAAA,IAAA,CAAK9lB,OAAR,MAAA,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,cAAc8lB,UAAjC,CAAA;;AAEA,IAAA,IAAIA,UAAU,IAAI,QAAYA,IAAAA,UAA9B,EAA0C;AACxC;MACAD,QAAQ,GAAGC,UAAU,CAAC3S,MAAtB,CAAA;AACD,KAHD,MAGO,IAAI2S,UAAU,IAAI,SAAA,IAAaA,UAA/B,EAA2C;AAChD;AACA,MAAA,IAAI,CAACA,UAAU,CAACtH,KAAhB,EAAuB;QACrBsH,UAAU,CAACtH,KAAX,GAAmB,IAAA,CAAKuH,sBAAL,CAA4BD,UAAU,CAACE,OAAvC,CAAnB,CAAA;AACD,OAAA;;MAED,IAAIF,UAAU,CAACtH,KAAf,EAAsB;AACpBqH,QAAAA,QAAQ,GAAGC,UAAU,CAACtH,KAAX,CAAiBrL,MAA5B,CAAA;AACD,OAAA;AACF,KAhBW;;;AAmBZ,IAAA,MAAM3F,KAAK,GAAG,IAAA,CAAK9L,QAAL,CAAc,UAAd,EAA0B;MACtCokB,UADsC;AAEtCD,MAAAA,QAAAA;AAFsC,KAA1B,CAAd,CAAA;IAIA,OAAO,IAAA,CAAKlT,YAAL,CAAkB,UAAlB,EAA8BnF,KAAK,CAACqY,QAApC,EAA8CC,UAA9C,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEpB,EAAAA,qBAAqB,CAACxhB,SAAD,EAAYxC,KAAZ,EAAmB;IACtC,OAAO,IAAIshB,OAAJ,CAAY9e,SAAZ,EAAuB,IAAvB,EAA6BxC,KAA7B,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACEokB,WAAW,CAACpkB,KAAD,EAAQ;AAAA,IAAA,IAAA,cAAA,CAAA;;AACjB,IAAA,MAAMolB,UAAU,GAAG,CAAA,cAAA,GAAA,IAAA,CAAK9lB,OAAR,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,eAAc8lB,UAAjC,CAAA;AACA;;IACA,IAAIG,cAAc,GAAG,EAArB,CAAA;;AACA,IAAA,IAAIvoB,KAAK,CAACC,OAAN,CAAcmoB,UAAd,CAAJ,EAA+B;AAC7B;AACAG,MAAAA,cAAc,GAAGH,UAAU,CAACplB,KAAD,CAA3B,CAAA;AACD,KAHD,MAGO,IAAIolB,UAAU,IAAI,SAAA,IAAaA,UAA/B,EAA2C;AAChD;AACA;AACA;AAEA;AACA,MAAA,IAAI,CAACA,UAAU,CAACtH,KAAhB,EAAuB;QACrBsH,UAAU,CAACtH,KAAX,GAAmB,IAAA,CAAKuH,sBAAL,CAA4BD,UAAU,CAACE,OAAvC,CAAnB,CAAA;AACD,OAAA;;AAEDC,MAAAA,cAAc,GAAGH,UAAU,CAACtH,KAAX,CAAiB9d,KAAjB,CAAjB,CAAA;AACD,KAAA;;IAED,IAAID,QAAQ,GAAGwlB,cAAf,CAAA;;IAEA,IAAIxlB,QAAQ,YAAYjD,OAAxB,EAAiC;AAC/BiD,MAAAA,QAAQ,GAAG,IAAA,CAAKylB,qBAAL,CAA2BzlB,QAA3B,CAAX,CAAA;AACD,KAxBgB;AA2BjB;;;AACA,IAAA,MAAM+M,KAAK,GAAG,IAAA,CAAK9L,QAAL,CAAc,UAAd,EAA0B;MACtCjB,QAAQ,EAAEA,QAAQ,IAAI,EADgB;AAEtCC,MAAAA,KAAAA;AAFsC,KAA1B,CAAd,CAAA;IAKA,OAAO,IAAA,CAAKiS,YAAL,CAAkB,UAAlB,EAA8BnF,KAAK,CAAC/M,QAApC,EAA8CC,KAA9C,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACEqlB,sBAAsB,CAACI,cAAD,EAAiB;AAAA,IAAA,IAAA,cAAA,EAAA,cAAA,CAAA;;IACrC,IAAI,CAAA,cAAA,GAAA,IAAA,CAAKnmB,OAAL,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,IAAA,cAAA,CAAcomB,QAAd,IAAA,CAAA,cAAA,GAA0B,KAAKpmB,OAA/B,MAAA,IAAA,IAAA,cAAA,KAAA,KAAA,CAAA,IAA0B,cAAcqmB,CAAAA,aAA5C,EAA2D;AACzD,MAAA,OAAOlpB,qBAAqB,CAC1B,IAAK6C,CAAAA,OAAL,CAAaomB,QADa,EAE1B,IAAKpmB,CAAAA,OAAL,CAAaqmB,aAFa,EAG1BF,cAH0B,CAArB,IAIF,EAJL,CAAA;AAKD,KAAA;;IAED,OAAO,CAACA,cAAD,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACED,qBAAqB,CAACjgB,OAAD,EAAU;AAC7B;AACA,IAAA,MAAMxF,QAAQ,GAAG;AACfwF,MAAAA,OAAAA;KADF,CAAA;AAIA,IAAA,MAAMqgB,MAAM;AAAG;AACbrgB,IAAAA,OAAO,CAAC9M,OAAR,KAAoB,GAApB,GACI8M,OADJ,GAEIA,OAAO,CAACua,aAAR,CAAsB,GAAtB,CAHN,CAAA;;AAMA,IAAA,IAAI8F,MAAJ,EAAY;AACV;AACA;MACA7lB,QAAQ,CAACqhB,GAAT,GAAewE,MAAM,CAACzC,OAAP,CAAe0C,OAAf,IAA0BD,MAAM,CAACE,IAAhD,CAAA;;AAEA,MAAA,IAAIF,MAAM,CAACzC,OAAP,CAAe4C,UAAnB,EAA+B;AAC7BhmB,QAAAA,QAAQ,CAAC2iB,MAAT,GAAkBkD,MAAM,CAACzC,OAAP,CAAe4C,UAAjC,CAAA;AACD,OAAA;;MAEDhmB,QAAQ,CAAC/E,KAAT,GAAiB4qB,MAAM,CAACzC,OAAP,CAAe6C,SAAf,GAA2B3C,QAAQ,CAACuC,MAAM,CAACzC,OAAP,CAAe6C,SAAhB,EAA2B,EAA3B,CAAnC,GAAoE,CAArF,CAAA;MACAjmB,QAAQ,CAAC9E,MAAT,GAAkB2qB,MAAM,CAACzC,OAAP,CAAe8C,UAAf,GAA4B5C,QAAQ,CAACuC,MAAM,CAACzC,OAAP,CAAe8C,UAAhB,EAA4B,EAA5B,CAApC,GAAsE,CAAxF,CAVU;;AAaVlmB,MAAAA,QAAQ,CAACjF,CAAT,GAAaiF,QAAQ,CAAC/E,KAAtB,CAAA;AACA+E,MAAAA,QAAQ,CAAChF,CAAT,GAAagF,QAAQ,CAAC9E,MAAtB,CAAA;;AAEA,MAAA,IAAI2qB,MAAM,CAACzC,OAAP,CAAe+C,QAAnB,EAA6B;AAC3BnmB,QAAAA,QAAQ,CAAC3B,IAAT,GAAgBwnB,MAAM,CAACzC,OAAP,CAAe+C,QAA/B,CAAA;AACD,OAAA;;AAED,MAAA,MAAMC,WAAW,GAAG5gB,OAAO,CAACua,aAAR,CAAsB,KAAtB,CAApB,CAAA;;AAEA,MAAA,IAAIqG,WAAJ,EAAiB;AAAA,QAAA,IAAA,qBAAA,CAAA;;AACf;AACA;QACApmB,QAAQ,CAACmiB,IAAT,GAAgBiE,WAAW,CAACC,UAAZ,IAA0BD,WAAW,CAAC/E,GAAtD,CAAA;QACArhB,QAAQ,CAACohB,GAAT,GAAA,CAAA,qBAAA,GAAegF,WAAW,CAACE,YAAZ,CAAyB,KAAzB,CAAf,MAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAAkD,EAAlD,CAAA;AACD,OAAA;;MAED,IAAIT,MAAM,CAACzC,OAAP,CAAemD,WAAf,IAA8BV,MAAM,CAACzC,OAAP,CAAeoD,OAAjD,EAA0D;QACxDxmB,QAAQ,CAACggB,YAAT,GAAwB,IAAxB,CAAA;AACD,OAAA;AACF,KAAA;;IAED,OAAO,IAAA,CAAK9N,YAAL,CAAkB,aAAlB,EAAiClS,QAAjC,EAA2CwF,OAA3C,EAAoDqgB,MAApD,CAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACE7B,EAAAA,YAAY,CAAChkB,QAAD,EAAWC,KAAX,EAAkB;AAC5B,IAAA,OAAO+jB,YAAY,CAAChkB,QAAD,EAAW,IAAX,EAAiBC,KAAjB,CAAnB,CAAA;AACD,GAAA;;AA1KoC;;ACLvC;;AACA;;AACA;AAEA;AACA;AACA;AACA;;AACA,MAAMwmB,WAAW,GAAG,KAApB,CAAA;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,MAAN,CAAa;AACX;AACF;AACA;EACEzoB,WAAW,CAACuB,IAAD,EAAO;IAChB,IAAKA,CAAAA,IAAL,GAAYA,IAAZ,CAAA;IACA,IAAKmnB,CAAAA,QAAL,GAAgB,IAAhB,CAAA;IACA,IAAKtjB,CAAAA,MAAL,GAAc,KAAd,CAAA;IACA,IAAKob,CAAAA,SAAL,GAAiB,KAAjB,CAAA;IACA,IAAKmI,CAAAA,SAAL,GAAiB,KAAjB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAKC,CAAAA,SAAL,GAAiBxtB,SAAjB,CAAA;AACA;;IACA,IAAKytB,CAAAA,aAAL,GAAqB,KAArB,CAAA;AACA;;IACA,IAAKC,CAAAA,YAAL,GAAoB,KAApB,CAAA;AACA;;IACA,IAAKC,CAAAA,mBAAL,GAA2B,KAA3B,CAAA;AACA;;IACA,IAAKC,CAAAA,iBAAL,GAAyB,KAAzB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAKC,CAAAA,YAAL,GAAoB7tB,SAApB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAK8tB,CAAAA,eAAL,GAAuB9tB,SAAvB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAK+tB,CAAAA,eAAL,GAAuB/tB,SAAvB,CAAA;AACA;AACJ;AACA;AACA;;IACI,IAAKguB,CAAAA,eAAL,GAAuBhuB,SAAvB,CAAA;AAEA;AACJ;AACA;AACA;;IACI,IAAKiuB,CAAAA,YAAL,GAAoBjuB,SAApB,CAAA;IAGA,IAAKkuB,CAAAA,YAAL,GAAoB,IAAA,CAAKA,YAAL,CAAkB5X,IAAlB,CAAuB,IAAvB,CAApB,CA/CgB;;AAkDhBnQ,IAAAA,IAAI,CAAC+P,EAAL,CAAQ,cAAR,EAAwB,KAAKgY,YAA7B,CAAA,CAAA;AACD,GAAA;;AAEDC,EAAAA,IAAI,GAAG;AACL,IAAA,IAAA,CAAKD,YAAL,EAAA,CAAA;;AACA,IAAA,IAAA,CAAK1N,MAAL,EAAA,CAAA;AACD,GAAA;;AAED9P,EAAAA,KAAK,GAAG;IACN,IAAI,IAAA,CAAK4c,QAAL,IAAiB,IAAA,CAAKlI,SAAtB,IAAmC,IAAA,CAAKmI,SAA5C,EAAuD;AACrD;AACA;AACA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMjmB,KAAK,GAAG,IAAKnB,CAAAA,IAAL,CAAU+H,SAAxB,CAAA;IAEA,IAAKlE,CAAAA,MAAL,GAAc,KAAd,CAAA;IACA,IAAKujB,CAAAA,SAAL,GAAiB,KAAjB,CAAA;IACA,IAAKnI,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACA,IAAA,IAAA,CAAKoI,SAAL,GAAiB,IAAA,CAAKrnB,IAAL,CAAUD,OAAV,CAAkBkoB,qBAAnC,CAAA;;AAEA,IAAA,IAAI9mB,KAAK,IAAIA,KAAK,CAACC,aAAN,GAAsBD,KAAK,CAAC1F,KAA5B,IAAqC,KAAKuE,IAAL,CAAUD,OAAV,CAAkBmoB,iBAApE,EAAuF;MACrF,IAAKb,CAAAA,SAAL,GAAiB,CAAjB,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKc,gBAAL,EAAA,CAAA;;AACA9V,IAAAA,UAAU,CAAC,MAAM;AACf,MAAA,IAAA,CAAKgI,MAAL,EAAA,CAAA;AACD,KAFS,EAEP,IAAKkN,CAAAA,YAAL,GAAoB,EAApB,GAAyB,CAFlB,CAAV,CAAA;AAGD,GAAA;AAED;;;AACAQ,EAAAA,YAAY,GAAG;AACb,IAAA,IAAA,CAAK/nB,IAAL,CAAUuhB,GAAV,CAAc,cAAd,EAA8B,KAAKwG,YAAnC,CAAA,CAAA;;IACA,IAAI,CAAC,IAAKX,CAAAA,SAAV,EAAqB;AACnB,MAAA,MAAMjmB,KAAK,GAAG,IAAKnB,CAAAA,IAAL,CAAU+H,SAAxB,CAAA;MACA,IAAKqf,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKnI,CAAAA,SAAL,GAAiB,KAAjB,CAAA;AACA,MAAA,IAAA,CAAKoI,SAAL,GAAiB,IAAA,CAAKrnB,IAAL,CAAUD,OAAV,CAAkBqoB,qBAAnC,CAAA;;AACA,MAAA,IAAIjnB,KAAK,IAAIA,KAAK,CAAC6B,UAAN,CAAiBT,OAAjB,GAA2BpB,KAAK,CAAC1F,KAAjC,IAA0C,IAAKuE,CAAAA,IAAL,CAAUD,OAAV,CAAkBmoB,iBAAzE,EAA4F;QAC1F,IAAKb,CAAAA,SAAL,GAAiB,CAAjB,CAAA;AACD,OAAA;;AACD,MAAA,IAAA,CAAKc,gBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;;;AACAA,EAAAA,gBAAgB,GAAG;IACjB,MAAM;AAAEnoB,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;AACA,IAAA,MAAMmB,KAAK,GAAG,IAAKnB,CAAAA,IAAL,CAAU+H,SAAxB,CAAA;IACA,MAAM;AAAEhI,MAAAA,OAAAA;AAAF,KAAA,GAAcC,IAApB,CAAA;;AAEA,IAAA,IAAID,OAAO,CAACsoB,qBAAR,KAAkC,MAAtC,EAA8C;MAC5CtoB,OAAO,CAACuoB,eAAR,GAA0B,IAA1B,CAAA;MACA,IAAKR,CAAAA,YAAL,GAAoBjuB,SAApB,CAAA;AACD,KAHD,MAGO,IAAIkG,OAAO,CAACsoB,qBAAR,KAAkC,MAAtC,EAA8C;MACnDtoB,OAAO,CAACuoB,eAAR,GAA0B,KAA1B,CAAA;MACA,IAAKjB,CAAAA,SAAL,GAAiB,CAAjB,CAAA;MACA,IAAKS,CAAAA,YAAL,GAAoBjuB,SAApB,CAAA;KAHK,MAIA,IAAI,IAAKutB,CAAAA,SAAL,IAAkBpnB,IAAI,CAACuoB,mBAA3B,EAAgD;AACrD;AACA,MAAA,IAAA,CAAKT,YAAL,GAAoB9nB,IAAI,CAACuoB,mBAAzB,CAAA;AACD,KAHM,MAGA;AACL,MAAA,IAAA,CAAKT,YAAL,GAAoB,IAAA,CAAK9nB,IAAL,CAAUigB,cAAV,EAApB,CAAA;AACD,KAAA;;IAED,IAAKyH,CAAAA,YAAL,GAAoBvmB,KAApB,KAAA,IAAA,IAAoBA,KAApB,KAAoBA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,KAAK,CAAE2E,qBAAP,EAApB,CAAA;AAEA9F,IAAAA,IAAI,CAACuG,UAAL,CAAgBsC,OAAhB,GArBiB;;IAwBjB,IAAKye,CAAAA,aAAL,GAAqBzf,OAAO,CAAC,IAAA,CAAKwf,SAAL,IAAkB,IAAKA,CAAAA,SAAL,GAAiB,EAApC,CAA5B,CAAA;AACA,IAAA,IAAA,CAAKmB,YAAL,GAAoB3gB,OAAO,CAAC,IAAKigB,CAAAA,YAAN,CAAP,KACG3mB,KADH,KAAA,IAAA,IACGA,KADH,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GACGA,KAAK,CAAE2C,OAAP,CAAe2e,cAAf,EADH,CAAA,KAEI,CAAC,IAAA,CAAKxD,SAAN,IAAmB,CAACjf,IAAI,CAACmF,UAAL,CAAgBC,SAAhB,EAFxB,CAApB,CAAA;;IAGA,IAAI,CAAC,IAAKojB,CAAAA,YAAV,EAAwB;MACtB,IAAKhB,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;;AAEA,MAAA,IAAI,IAAKJ,CAAAA,SAAL,IAAkBjmB,KAAtB,EAA6B;AAC3BA,QAAAA,KAAK,CAAC6D,mBAAN,EAAA,CAAA;AACA7D,QAAAA,KAAK,CAAC8D,mBAAN,EAAA,CAAA;AACD,OAAA;AACF,KAPD,MAOO;AAAA,MAAA,IAAA,qBAAA,CAAA;;AACL,MAAA,IAAA,CAAKuiB,mBAAL,GAA2BznB,CAAAA,qBAAAA,GAAAA,OAAO,CAACuoB,eAAnC,yEAAsD,KAAtD,CAAA;AACD,KAAA;;AACD,IAAA,IAAA,CAAKb,iBAAL,GAAyB,CAAC,IAAA,CAAKD,mBAAN,IAA6B,IAAKxnB,CAAAA,IAAL,CAAUD,OAAV,CAAkBqJ,SAAlB,GAA8B6d,WAApF,CAAA;IACA,IAAKU,CAAAA,eAAL,GAAuB,IAAA,CAAKH,mBAAL,GAA2BxnB,IAAI,CAACgG,OAAhC,GAA0ChG,IAAI,CAACyoB,EAAtE,CAAA;;IAEA,IAAI,CAAC,IAAKnB,CAAAA,aAAV,EAAyB;MACvB,IAAKD,CAAAA,SAAL,GAAiB,CAAjB,CAAA;MACA,IAAKmB,CAAAA,YAAL,GAAoB,KAApB,CAAA;MACA,IAAKf,CAAAA,iBAAL,GAAyB,KAAzB,CAAA;MACA,IAAKD,CAAAA,mBAAL,GAA2B,IAA3B,CAAA;;MACA,IAAI,IAAA,CAAKJ,SAAT,EAAoB;QAClB,IAAIpnB,IAAI,CAACgG,OAAT,EAAkB;UAChBhG,IAAI,CAACgG,OAAL,CAAalL,KAAb,CAAmB4tB,OAAnB,GAA6B1E,MAAM,CAACiD,WAAD,CAAnC,CAAA;AACD,SAAA;;QACDjnB,IAAI,CAACsJ,cAAL,CAAoB,CAApB,CAAA,CAAA;AACD,OAAA;;AACD,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKkf,YAAL,IAAqB,IAAKV,CAAAA,YAA1B,IAA0C,IAAKA,CAAAA,YAAL,CAAkB9H,SAAhE,EAA2E;AAAA,MAAA,IAAA,oBAAA,CAAA;;AACzE;MACA,IAAKuH,CAAAA,YAAL,GAAoB,IAApB,CAAA;AACA,MAAA,IAAA,CAAKK,eAAL,GAAuB,IAAK5nB,CAAAA,IAAL,CAAUiE,SAAjC,CAAA;MACA,IAAK4jB,CAAAA,eAAL,2BAAuB,IAAK7nB,CAAAA,IAAL,CAAU+H,SAAjC,MAAA,IAAA,IAAA,oBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAuB,qBAAqB7D,aAA5C,CAAA;;MAEA,IAAIlE,IAAI,CAACiE,SAAT,EAAoB;AAClBjE,QAAAA,IAAI,CAACiE,SAAL,CAAenJ,KAAf,CAAqB6tB,QAArB,GAAgC,QAAhC,CAAA;AACA3oB,QAAAA,IAAI,CAACiE,SAAL,CAAenJ,KAAf,CAAqBW,KAArB,GAA6BuE,IAAI,CAACO,YAAL,CAAkB7G,CAAlB,GAAsB,IAAnD,CAAA;AACD,OAAA;AACF,KAVD,MAUO;MACL,IAAK6tB,CAAAA,YAAL,GAAoB,KAApB,CAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKH,SAAT,EAAoB;AAClB;MACA,IAAI,IAAA,CAAKI,mBAAT,EAA8B;QAC5B,IAAIxnB,IAAI,CAACgG,OAAT,EAAkB;UAChBhG,IAAI,CAACgG,OAAL,CAAalL,KAAb,CAAmB4tB,OAAnB,GAA6B1E,MAAM,CAACiD,WAAD,CAAnC,CAAA;AACD,SAAA;;QACDjnB,IAAI,CAACsJ,cAAL,CAAoB,CAApB,CAAA,CAAA;AACD,OALD,MAKO;AACL,QAAA,IAAI,KAAKme,iBAAL,IAA0BznB,IAAI,CAACyoB,EAAnC,EAAuC;UACrCzoB,IAAI,CAACyoB,EAAL,CAAQ3tB,KAAR,CAAc4tB,OAAd,GAAwB1E,MAAM,CAACiD,WAAD,CAA9B,CAAA;AACD,SAAA;;QACD,IAAIjnB,IAAI,CAACgG,OAAT,EAAkB;AAChBhG,UAAAA,IAAI,CAACgG,OAAL,CAAalL,KAAb,CAAmB4tB,OAAnB,GAA6B,GAA7B,CAAA;AACD,SAAA;AACF,OAAA;;MAED,IAAI,IAAA,CAAKF,YAAT,EAAuB;AACrB,QAAA,IAAA,CAAKI,sBAAL,EAAA,CAAA;;QACA,IAAI,IAAA,CAAKlB,YAAT,EAAuB;AACrB;UACA,IAAKA,CAAAA,YAAL,CAAkB5sB,KAAlB,CAAwB+tB,UAAxB,GAAqC,WAArC,CAFqB;AAKrB;;UACA,IAAKnB,CAAAA,YAAL,CAAkB5sB,KAAlB,CAAwB4tB,OAAxB,GAAkC1E,MAAM,CAACiD,WAAD,CAAxC,CAAA;AACD,SAAA;AACF,OAAA;AACF,KA3BD,MA2BO,IAAI,IAAKhI,CAAAA,SAAT,EAAoB;AACzB;AACA;MACA,IAAIjf,IAAI,CAACmF,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAAJ,EAAoC;AAClClU,QAAAA,IAAI,CAACmF,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAA+B9a,CAAAA,EAA/B,CAAkC0B,KAAlC,CAAwC8Z,OAAxC,GAAkD,MAAlD,CAAA;AACD,OAAA;;MACD,IAAI5U,IAAI,CAACmF,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAAJ,EAAoC;AAClClU,QAAAA,IAAI,CAACmF,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAA+B9a,CAAAA,EAA/B,CAAkC0B,KAAlC,CAAwC8Z,OAAxC,GAAkD,MAAlD,CAAA;AACD,OAAA;;MAED,IAAI,IAAA,CAAK2S,YAAT,EAAuB;AACrB,QAAA,IAAIvnB,IAAI,CAACmF,UAAL,CAAgBzL,CAAhB,KAAsB,CAA1B,EAA6B;AAC3B;UACAsG,IAAI,CAACmF,UAAL,CAAgBqP,aAAhB,EAAA,CAAA;UACAxU,IAAI,CAACmF,UAAL,CAAgBK,MAAhB,EAAA,CAAA;AACD,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AAED;;;AACA6U,EAAAA,MAAM,GAAG;AACP,IAAA,IAAI,IAAK+M,CAAAA,SAAL,IACG,IAAA,CAAKE,aADR,IAEG,IAAA,CAAKI,YAFR,IAGG,KAAKA,YAAL,CAAkBxuB,OAAlB,KAA8B,KAHrC,EAG4C;AAC1C;AACA;AACA;AACA;AACA;AACA;MACA,IAAI+C,OAAJ,CAAaC,OAAD,IAAa;QACvB,IAAI4sB,OAAO,GAAG,KAAd,CAAA;QACA,IAAIC,UAAU,GAAG,IAAjB,CAAA;QACAntB,WAAW;AAAC;AAAiC,QAAA,IAAA,CAAK8rB,YAAvC,CAAX,CAAiErD,OAAjE,CAAyE,MAAM;AAC7EyE,UAAAA,OAAO,GAAG,IAAV,CAAA;;UACA,IAAI,CAACC,UAAL,EAAiB;YACf7sB,OAAO,CAAC,IAAD,CAAP,CAAA;AACD,WAAA;SAJH,CAAA,CAAA;AAMAmW,QAAAA,UAAU,CAAC,MAAM;AACf0W,UAAAA,UAAU,GAAG,KAAb,CAAA;;AACA,UAAA,IAAID,OAAJ,EAAa;YACX5sB,OAAO,CAAC,IAAD,CAAP,CAAA;AACD,WAAA;SAJO,EAKP,EALO,CAAV,CAAA;AAMAmW,QAAAA,UAAU,CAACnW,OAAD,EAAU,GAAV,CAAV,CAAA;AACD,OAhBD,EAgBGmoB,OAhBH,CAgBW,MAAM,IAAA,CAAK2E,SAAL,EAhBjB,CAAA,CAAA;AAiBD,KA3BD,MA2BO;AACL,MAAA,IAAA,CAAKA,SAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;;;AACAA,EAAAA,SAAS,GAAG;AAAA,IAAA,IAAA,kBAAA,EAAA,mBAAA,CAAA;;AACV,IAAA,CAAA,kBAAA,GAAA,IAAA,CAAKhpB,IAAL,CAAUgG,OAAV,MAAA,IAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,IAAA,kBAAA,CAAmBlL,KAAnB,CAAyBmuB,WAAzB,CAAqC,4BAArC,EAAmE,IAAK5B,CAAAA,SAAL,GAAiB,IAApF,CAAA,CAAA;IAEA,IAAKrnB,CAAAA,IAAL,CAAUyB,QAAV,CACE,IAAA,CAAK2lB,SAAL,GAAiB,uBAAjB,GAA2C,uBAD7C,CAAA,CAHU;;IAQV,IAAKpnB,CAAAA,IAAL,CAAUyB,QAAV;AACE;AACC,IAAA,aAAA,IAAiB,KAAK2lB,SAAL,GAAiB,IAAjB,GAAwB,KAAzC,CAFH,CAAA,CAAA;IAKA,CAAKpnB,mBAAAA,GAAAA,IAAAA,CAAAA,IAAL,CAAUgG,OAAV,MAAmB6H,IAAAA,IAAAA,mBAAAA,KAAAA,KAAAA,CAAAA,IAAAA,mBAAAA,CAAAA,SAAnB,CAA6BW,MAA7B,CAAoC,kBAApC,EAAwD,IAAA,CAAK4Y,SAA7D,CAAA,CAAA;;IAEA,IAAI,IAAA,CAAKA,SAAT,EAAoB;MAClB,IAAI,IAAA,CAAKM,YAAT,EAAuB;AACrB;AACA,QAAA,IAAA,CAAKA,YAAL,CAAkB5sB,KAAlB,CAAwB4tB,OAAxB,GAAkC,GAAlC,CAAA;AACD,OAAA;;AACD,MAAA,IAAA,CAAKQ,mBAAL,EAAA,CAAA;AACD,KAND,MAMO,IAAI,IAAKjK,CAAAA,SAAT,EAAoB;AACzB,MAAA,IAAA,CAAKkK,qBAAL,EAAA,CAAA;AACD,KAAA;;IAED,IAAI,CAAC,IAAK7B,CAAAA,aAAV,EAAyB;AACvB,MAAA,IAAA,CAAK8B,oBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;;;AACAA,EAAAA,oBAAoB,GAAG;IACrB,MAAM;AAAEppB,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IACA,IAAK6D,CAAAA,MAAL,GAAc,IAAA,CAAKujB,SAAnB,CAAA;IACA,IAAKD,CAAAA,QAAL,GAAgB,IAAA,CAAKlI,SAArB,CAAA;IACA,IAAKmI,CAAAA,SAAL,GAAiB,KAAjB,CAAA;IACA,IAAKnI,CAAAA,SAAL,GAAiB,KAAjB,CAAA;IAEAjf,IAAI,CAACyB,QAAL,CACE,IAAKoC,CAAAA,MAAL,GAAc,qBAAd,GAAsC,qBADxC,CAAA,CAPqB;;AAYrB7D,IAAAA,IAAI,CAACyB,QAAL;AACE;AACC,IAAA,aAAA,IAAiB,KAAKoC,MAAL,GAAc,OAAd,GAAwB,QAAzC,CAFH,CAAA,CAAA;;IAKA,IAAI,IAAA,CAAKsjB,QAAT,EAAmB;AACjBnnB,MAAAA,IAAI,CAACsF,OAAL,EAAA,CAAA;AACD,KAFD,MAEO,IAAI,IAAKzB,CAAAA,MAAT,EAAiB;AAAA,MAAA,IAAA,eAAA,CAAA;;AACtB,MAAA,IAAI,KAAK2kB,YAAL,IAAqBxoB,IAAI,CAACiE,SAA9B,EAAyC;AACvCjE,QAAAA,IAAI,CAACiE,SAAL,CAAenJ,KAAf,CAAqB6tB,QAArB,GAAgC,SAAhC,CAAA;AACA3oB,QAAAA,IAAI,CAACiE,SAAL,CAAenJ,KAAf,CAAqBW,KAArB,GAA6B,MAA7B,CAAA;AACD,OAAA;;AACD,MAAA,CAAA,eAAA,GAAAuE,IAAI,CAAC+H,SAAL,MAAA,IAAA,IAAA,eAAA,KAAA,KAAA,CAAA,IAAA,eAAA,CAAgB9C,mBAAhB,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;;;AACAikB,EAAAA,mBAAmB,GAAG;IACpB,MAAM;AAAElpB,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;;IACA,IAAI,IAAA,CAAKwoB,YAAT,EAAuB;MACrB,IAAI,IAAA,CAAKjB,YAAL,IAAqB,IAAA,CAAKK,eAA1B,IAA6C,IAAA,CAAKC,eAAtD,EAAuE;AACrE,QAAA,IAAA,CAAKwB,UAAL,CAAgB,IAAA,CAAKzB,eAArB,EAAsC,WAAtC,EAAmD,oBAAnD,CAAA,CAAA;;AACA,QAAA,IAAA,CAAKyB,UAAL,CAAgB,IAAA,CAAKxB,eAArB,EAAsC,WAAtC,EAAmD,MAAnD,CAAA,CAAA;AACD,OAAA;;MAED,IAAI7nB,IAAI,CAAC+H,SAAT,EAAoB;QAClB/H,IAAI,CAAC+H,SAAL,CAAe/C,mBAAf,EAAA,CAAA;;AACA,QAAA,IAAA,CAAKqkB,UAAL,CACErpB,IAAI,CAAC+H,SAAL,CAAe9D,SADjB,EAEE,WAFF,EAGEjE,IAAI,CAAC+H,SAAL,CAAed,mBAAf,EAHF,CAAA,CAAA;AAKD,OAAA;AACF,KAAA;;AAED,IAAA,IAAI,KAAKwgB,iBAAL,IAA0BznB,IAAI,CAACyoB,EAAnC,EAAuC;AACrC,MAAA,IAAA,CAAKY,UAAL,CAAgBrpB,IAAI,CAACyoB,EAArB,EAAyB,SAAzB,EAAoCzE,MAAM,CAAChkB,IAAI,CAACD,OAAL,CAAaqJ,SAAd,CAA1C,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKoe,mBAAL,IAA4BxnB,IAAI,CAACgG,OAArC,EAA8C;MAC5C,IAAKqjB,CAAAA,UAAL,CAAgBrpB,IAAI,CAACgG,OAArB,EAA8B,SAA9B,EAAyC,GAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;;;AACAmjB,EAAAA,qBAAqB,GAAG;IACtB,MAAM;AAAEnpB,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;;IAEA,IAAI,IAAA,CAAKwoB,YAAT,EAAuB;MACrB,IAAKI,CAAAA,sBAAL,CAA4B,IAA5B,CAAA,CAAA;AACD,KALqB;;;AAQtB,IAAA,IAAI,IAAKnB,CAAAA,iBAAL,IAA0BznB,IAAI,CAACoJ,SAAL,GAAiB,IAA3C,IAAmDpJ,IAAI,CAACyoB,EAA5D,EAAgE;MAC9D,IAAKY,CAAAA,UAAL,CAAgBrpB,IAAI,CAACyoB,EAArB,EAAyB,SAAzB,EAAoC,GAApC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKjB,mBAAL,IAA4BxnB,IAAI,CAACgG,OAArC,EAA8C;MAC5C,IAAKqjB,CAAAA,UAAL,CAAgBrpB,IAAI,CAACgG,OAArB,EAA8B,SAA9B,EAAyC,GAAzC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;;;EACE4iB,sBAAsB,CAAC7T,OAAD,EAAU;IAC9B,IAAI,CAAC,IAAK+S,CAAAA,YAAV,EAAwB,OAAA;IAExB,MAAM;AAAE9nB,MAAAA,IAAAA;AAAF,KAAA,GAAW,IAAjB,CAAA;IACA,MAAM;AAAEggB,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK8H,YAA3B,CAAA;IACA,MAAM;MAAE/f,SAAF;AAAaxH,MAAAA,YAAAA;AAAb,KAAA,GAA8BP,IAApC,CAAA;;IAEA,IAAI,IAAA,CAAKunB,YAAL,IAAqBvH,SAArB,IAAkC,KAAK4H,eAAvC,IAA0D,IAAKC,CAAAA,eAAnE,EAAoF;AAClF,MAAA,MAAMyB,gBAAgB,GAAG,CAAC/oB,YAAY,CAAC7G,CAAd,IAAmB,IAAKouB,CAAAA,YAAL,CAAkBpuB,CAAlB,GAAsBsmB,SAAS,CAACtmB,CAAnD,CAAwDsmB,GAAAA,SAAS,CAACzkB,CAA3F,CAAA;AACA,MAAA,MAAMguB,gBAAgB,GAAG,CAAChpB,YAAY,CAAC5G,CAAd,IAAmB,IAAKmuB,CAAAA,YAAL,CAAkBnuB,CAAlB,GAAsBqmB,SAAS,CAACrmB,CAAnD,CAAwDqmB,GAAAA,SAAS,CAACxkB,CAA3F,CAAA;MACA,MAAMguB,gBAAgB,GAAGjpB,YAAY,CAAC7G,CAAb,GAAiBsmB,SAAS,CAACzkB,CAApD,CAAA;MACA,MAAMkuB,gBAAgB,GAAGlpB,YAAY,CAAC5G,CAAb,GAAiBqmB,SAAS,CAACxkB,CAApD,CAAA;;AAGA,MAAA,IAAIuZ,OAAJ,EAAa;AACX,QAAA,IAAA,CAAKsU,UAAL,CACE,IAAKzB,CAAAA,eADP,EAEE,WAFF,EAGEltB,iBAAiB,CAAC4uB,gBAAD,EAAmBC,gBAAnB,CAHnB,CAAA,CAAA;;AAMA,QAAA,IAAA,CAAKF,UAAL,CACE,IAAKxB,CAAAA,eADP,EAEE,WAFF,EAGEntB,iBAAiB,CAAC8uB,gBAAD,EAAmBC,gBAAnB,CAHnB,CAAA,CAAA;AAKD,OAZD,MAYO;AACL5uB,QAAAA,YAAY,CAAC,IAAK+sB,CAAAA,eAAN,EAAuB0B,gBAAvB,EAAyCC,gBAAzC,CAAZ,CAAA;AACA1uB,QAAAA,YAAY,CAAC,IAAKgtB,CAAAA,eAAN,EAAuB2B,gBAAvB,EAAyCC,gBAAzC,CAAZ,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAI1hB,SAAJ,EAAe;MACbxO,cAAc,CAACwO,SAAS,CAACrE,GAAX,EAAgBsc,SAAS,IAAI,IAAK8H,CAAAA,YAAlC,CAAd,CAAA;MACA/f,SAAS,CAAC3G,aAAV,GAA0B,IAAK0mB,CAAAA,YAAL,CAAkBvsB,CAAlB,GAAsBwM,SAAS,CAACtM,KAA1D,CAAA;;AACA,MAAA,IAAIsZ,OAAJ,EAAa;QACX,IAAKsU,CAAAA,UAAL,CAAgBthB,SAAS,CAAC9D,SAA1B,EAAqC,WAArC,EAAkD8D,SAAS,CAACd,mBAAV,EAAlD,CAAA,CAAA;AACD,OAFD,MAEO;AACLc,QAAAA,SAAS,CAAC9C,mBAAV,EAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACEokB,EAAAA,UAAU,CAACzqB,MAAD,EAAS1D,IAAT,EAAeN,SAAf,EAA0B;IAClC,IAAI,CAAC,IAAKysB,CAAAA,SAAV,EAAqB;AACnBzoB,MAAAA,MAAM,CAAC9D,KAAP,CAAaI,IAAb,IAAqBN,SAArB,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED,MAAM;AAAE2L,MAAAA,UAAAA;AAAF,KAAA,GAAiB,KAAKvG,IAA5B,CAAA;AACA;;AACA,IAAA,MAAM0pB,SAAS,GAAG;MAChBvuB,QAAQ,EAAE,KAAKksB,SADC;AAEhBlgB,MAAAA,MAAM,EAAE,IAAKnH,CAAAA,IAAL,CAAUD,OAAV,CAAkBoH,MAFV;AAGhBD,MAAAA,UAAU,EAAE,MAAM;AAChB,QAAA,IAAI,CAACX,UAAU,CAAC6T,gBAAX,CAA4BlH,MAAjC,EAAyC;AACvC,UAAA,IAAA,CAAKkW,oBAAL,EAAA,CAAA;AACD,SAAA;OANa;AAQhBxqB,MAAAA,MAAAA;KARF,CAAA;AAUA8qB,IAAAA,SAAS,CAACxuB,IAAD,CAAT,GAAkBN,SAAlB,CAAA;IACA2L,UAAU,CAACO,eAAX,CAA2B4iB,SAA3B,CAAA,CAAA;AACD,GAAA;;AAhbU;;ACAb;AACA;AACA;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;AACA;AACA;;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA,MAAMC,cAAc,GAAG;AACrBte,EAAAA,cAAc,EAAE,IADK;AAErBgJ,EAAAA,OAAO,EAAE,GAFY;AAGrBuI,EAAAA,IAAI,EAAE,IAHe;AAIrBjQ,EAAAA,YAAY,EAAE,IAJO;AAKrB1D,EAAAA,mBAAmB,EAAE,IALA;AAMrBgf,EAAAA,qBAAqB,EAAE,GANF;AAOrBG,EAAAA,qBAAqB,EAAE,GAPF;AAQrB/gB,EAAAA,qBAAqB,EAAE,GARF;AASrB2Q,EAAAA,MAAM,EAAE,IATa;AAUrBC,EAAAA,SAAS,EAAE,IAVU;AAWrBb,EAAAA,SAAS,EAAE,IAXU;AAYrBO,EAAAA,WAAW,EAAE,IAZQ;AAarBuQ,EAAAA,iBAAiB,EAAE,IAbE;AAcrB3Z,EAAAA,uBAAuB,EAAE,IAdJ;AAerB6Q,EAAAA,gBAAgB,EAAE,eAfG;AAgBrBwK,EAAAA,aAAa,EAAE,OAhBM;AAiBrBC,EAAAA,SAAS,EAAE,iBAjBU;AAkBrBzX,EAAAA,eAAe,EAAE,MAlBI;AAmBrB6L,EAAAA,iBAAiB,EAAE,KAnBE;AAoBrBL,EAAAA,cAAc,EAAE,IApBK;AAqBrBxU,EAAAA,SAAS,EAAE,GArBU;AAuBrB3I,EAAAA,KAAK,EAAE,CAvBc;AAwBrB0jB,EAAAA,QAAQ,EAAE,4BAxBW;AAyBrBa,EAAAA,OAAO,EAAE,CAAC,CAAD,EAAI,CAAJ,CAzBY;AA0BrB7d,EAAAA,MAAM,EAAE,0BAAA;AA1Ba,CAAvB,CAAA;AA6BA;AACA;AACA;;AACA,MAAM2iB,UAAN,SAAyBnE,cAAzB,CAAwC;AACtC;AACF;AACA;EACElnB,WAAW,CAACsB,OAAD,EAAU;AACnB,IAAA,KAAA,EAAA,CAAA;IAEA,IAAKA,CAAAA,OAAL,GAAe,IAAKgqB,CAAAA,eAAL,CAAqBhqB,OAAO,IAAI,EAAhC,CAAf,CAAA;AAEA;AACJ;AACA;AACA;AACA;;AACI,IAAA,IAAA,CAAKyT,MAAL,GAAc;AAAE9Z,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAAzB,CAAA;AAEA;AACJ;AACA;AACA;;AACI,IAAA,IAAA,CAAKqwB,iBAAL,GAAyB;AAAEtwB,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAApC,CAAA;AAEA;AACJ;AACA;AACA;AACA;;AACI,IAAA,IAAA,CAAK4G,YAAL,GAAoB;AAAE7G,MAAAA,CAAC,EAAE,CAAL;AAAQC,MAAAA,CAAC,EAAE,CAAA;KAA/B,CAAA;AAEA;AACJ;AACA;;IACI,IAAKyP,CAAAA,SAAL,GAAiB,CAAjB,CAAA;IACA,IAAK5F,CAAAA,SAAL,GAAiB,CAAjB,CAAA;IACA,IAAK0R,CAAAA,cAAL,GAAsB,CAAtB,CAAA;IACA,IAAKrR,CAAAA,MAAL,GAAc,KAAd,CAAA;IACA,IAAKomB,CAAAA,YAAL,GAAoB,KAApB,CAAA;IACA,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;AAEA;AACJ;AACA;AACA;;IACI,IAAKC,CAAAA,gBAAL,GAAwB,EAAxB,CAAA;AACA;;IACA,IAAK5B,CAAAA,mBAAL,GAA2B1uB,SAA3B,CAAA;AAEA;;IACA,IAAK4iB,CAAAA,MAAL,GAAc5iB,SAAd,CAAA;AACA;;IACA,IAAKmM,CAAAA,OAAL,GAAenM,SAAf,CAAA;AACA;;IACA,IAAKqe,CAAAA,QAAL,GAAgBre,SAAhB,CAAA;AACA;;IACA,IAAKoK,CAAAA,SAAL,GAAiBpK,SAAjB,CAAA;AACA;;IACA,IAAKoW,CAAAA,UAAL,GAAkBpW,SAAlB,CAAA;AACA;;IACA,IAAKkO,CAAAA,SAAL,GAAiBlO,SAAjB,CAAA;AAEA,IAAA,IAAA,CAAKmW,MAAL,GAAc,IAAIxR,SAAJ,EAAd,CAAA;AACA,IAAA,IAAA,CAAK+H,UAAL,GAAkB,IAAI4T,UAAJ,EAAlB,CAAA;AACA,IAAA,IAAA,CAAKhV,UAAL,GAAkB,IAAI0O,UAAJ,CAAe,IAAf,CAAlB,CAAA;AACA,IAAA,IAAA,CAAKnL,QAAL,GAAgB,IAAIkG,QAAJ,CAAa,IAAb,CAAhB,CAAA;AACA,IAAA,IAAA,CAAKhL,MAAL,GAAc,IAAIsjB,MAAJ,CAAW,IAAX,CAAd,CAAA;AACA,IAAA,IAAA,CAAKkD,QAAL,GAAgB,IAAIlT,QAAJ,CAAa,IAAb,CAAhB,CAAA;AACA,IAAA,IAAA,CAAKnT,aAAL,GAAqB,IAAI+gB,aAAJ,CAAkB,IAAlB,CAArB,CAAA;AACD,GAAA;AAED;;;AACArG,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,IAAK5a,CAAAA,MAAL,IAAe,IAAA,CAAKomB,YAAxB,EAAsC;AACpC,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;IAED,IAAKpmB,CAAAA,MAAL,GAAc,IAAd,CAAA;AACA,IAAA,IAAA,CAAKpC,QAAL,CAAc,MAAd,CAAA,CANK;;IAOL,IAAKA,CAAAA,QAAL,CAAc,YAAd,CAAA,CAAA;;IAEA,IAAK4oB,CAAAA,oBAAL,GATK;;;IAYL,IAAIC,WAAW,GAAG,YAAlB,CAAA;;AACA,IAAA,IAAI,IAAK5hB,CAAAA,QAAL,CAAc2G,aAAlB,EAAiC;AAC/Bib,MAAAA,WAAW,IAAI,cAAf,CAAA;AACD,KAAA;;AACD,IAAA,IAAI,IAAKvqB,CAAAA,OAAL,CAAawqB,SAAjB,EAA4B;AAC1BD,MAAAA,WAAW,IAAI,GAAA,GAAM,IAAKvqB,CAAAA,OAAL,CAAawqB,SAAlC,CAAA;AACD,KAAA;;IACD,IAAI,IAAA,CAAKvkB,OAAT,EAAkB;AAChB,MAAA,IAAA,CAAKA,OAAL,CAAa/M,SAAb,IAA0B,MAAMqxB,WAAhC,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK9mB,SAAL,GAAiB,IAAA,CAAKzD,OAAL,CAAaU,KAAb,IAAsB,CAAvC,CAAA;IACA,IAAKyU,CAAAA,cAAL,GAAsB,IAAA,CAAK1R,SAA3B,CAAA;AACA,IAAA,IAAA,CAAK/B,QAAL,CAAc,aAAd,CAAA,CAzBK;AA2BL;;IACA,IAAK+oB,CAAAA,WAAL,GAAmB,IAAI7P,WAAJ,CAAgB,IAAhB,CAAnB,CA5BK;;AA+BL,IAAA,IAAI3Z,MAAM,CAACypB,KAAP,CAAa,IAAKjnB,CAAAA,SAAlB,KACG,IAAKA,CAAAA,SAAL,GAAiB,CADpB,IAEG,IAAKA,CAAAA,SAAL,IAAkB,IAAKsR,CAAAA,WAAL,EAFzB,EAE6C;MAC3C,IAAKtR,CAAAA,SAAL,GAAiB,CAAjB,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAAC,IAAA,CAAKkF,QAAL,CAAc2G,aAAnB,EAAkC;AAChC;AACA,MAAA,IAAA,CAAK6B,aAAL,EAAA,CAAA;AACD,KAxCI;;;AA2CL,IAAA,IAAA,CAAKwZ,UAAL,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKlX,MAAL,CAAY7Z,CAAZ,GAAgBwE,MAAM,CAACwsB,WAAvB,CAAA;AAEA,IAAA,IAAA,CAAKR,gBAAL,GAAwB,IAAA,CAAKtF,WAAL,CAAiB,IAAA,CAAKrhB,SAAtB,CAAxB,CAAA;IACA,IAAK/B,CAAAA,QAAL,CAAc,aAAd,EAA6B;MAC3BhB,KAAK,EAAE,KAAK+C,SADe;MAE3B3B,IAAI,EAAE,KAAKsoB,gBAFgB;AAG3BhpB,MAAAA,KAAK,EAAEtH,SAAAA;AAHoB,KAA7B,EAhDK;;AAuDL,IAAA,IAAA,CAAK0uB,mBAAL,GAA2B,IAAKtI,CAAAA,cAAL,EAA3B,CAAA;IACA,IAAKxe,CAAAA,QAAL,CAAc,eAAd,CAAA,CAAA;AAEA,IAAA,IAAA,CAAKsO,EAAL,CAAQ,qBAAR,EAA+B,MAAM;MACnC,MAAM;AAAEmE,QAAAA,WAAAA;OAAgB,GAAA,IAAA,CAAK/O,UAA7B,CADmC;;AAInC,MAAA,IAAI+O,WAAW,CAAC,CAAD,CAAf,EAAoB;QAClBA,WAAW,CAAC,CAAD,CAAX,CAAe9a,EAAf,CAAkB0B,KAAlB,CAAwB8Z,OAAxB,GAAkC,OAAlC,CAAA;QACA,IAAKqB,CAAAA,UAAL,CAAgB/B,WAAW,CAAC,CAAD,CAA3B,EAAgC,IAAA,CAAK1Q,SAAL,GAAiB,CAAjD,CAAA,CAAA;AACD,OAAA;;AACD,MAAA,IAAI0Q,WAAW,CAAC,CAAD,CAAf,EAAoB;QAClBA,WAAW,CAAC,CAAD,CAAX,CAAe9a,EAAf,CAAkB0B,KAAlB,CAAwB8Z,OAAxB,GAAkC,OAAlC,CAAA;QACA,IAAKqB,CAAAA,UAAL,CAAgB/B,WAAW,CAAC,CAAD,CAA3B,EAAgC,IAAA,CAAK1Q,SAAL,GAAiB,CAAjD,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKuB,WAAL,EAAA,CAAA;MAEA,IAAKhB,CAAAA,aAAL,CAAmBqS,UAAnB,EAAA,CAAA;AAEA,MAAA,IAAA,CAAKpG,MAAL,CAAYrR,GAAZ,CAAgBR,MAAhB,EAAwB,QAAxB,EAAkC,IAAA,CAAKysB,iBAAL,CAAuBza,IAAvB,CAA4B,IAA5B,CAAlC,CAAA,CAAA;AACA,MAAA,IAAA,CAAKH,MAAL,CAAYrR,GAAZ,CAAgBR,MAAhB,EAAwB,QAAxB,EAAkC,IAAA,CAAK0sB,uBAAL,CAA6B1a,IAA7B,CAAkC,IAAlC,CAAlC,CAAA,CAAA;MACA,IAAK1O,CAAAA,QAAL,CAAc,YAAd,CAAA,CAAA;AACD,KApBD,EA1DK;;AAiFL,IAAA,IAAI,KAAK0D,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAAJ,EAAoC;MAClC,IAAK+B,CAAAA,UAAL,CAAgB,IAAA,CAAK9Q,UAAL,CAAgB+O,WAAhB,CAA4B,CAA5B,CAAhB,EAAgD,IAAA,CAAK1Q,SAArD,CAAA,CAAA;AACD,KAAA;;IACD,IAAK/B,CAAAA,QAAL,CAAc,QAAd,CAAA,CAAA;IAEA,IAAKmC,CAAAA,MAAL,CAAYokB,IAAZ,EAAA,CAAA;IAEA,IAAKvmB,CAAAA,QAAL,CAAc,WAAd,CAAA,CAAA;AAEA,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;EACE4T,cAAc,CAAC5U,KAAD,EAAQ;AACpB,IAAA,MAAM0U,SAAS,GAAG,IAAKL,CAAAA,WAAL,EAAlB,CAAA;;AAEA,IAAA,IAAI,IAAK/U,CAAAA,OAAL,CAAa6c,IAAjB,EAAuB;AACrB,MAAA,IAAInc,KAAK,GAAG0U,SAAS,GAAG,CAAxB,EAA2B;AACzB1U,QAAAA,KAAK,IAAI0U,SAAT,CAAA;AACD,OAAA;;MAED,IAAI1U,KAAK,GAAG,CAAZ,EAAe;AACbA,QAAAA,KAAK,IAAI0U,SAAT,CAAA;AACD,OAAA;AACF,KAAA;;IAED,OAAO7a,KAAK,CAACmG,KAAD,EAAQ,CAAR,EAAW0U,SAAS,GAAG,CAAvB,CAAZ,CAAA;AACD,GAAA;;AAEDpQ,EAAAA,WAAW,GAAG;AACZ,IAAA,IAAA,CAAKI,UAAL,CAAgB+O,WAAhB,CAA4B/U,OAA5B,CAAqCoV,UAAD,IAAgB;AAAA,MAAA,IAAA,iBAAA,CAAA;;AAClD,MAAA,CAAA,iBAAA,GAAAA,UAAU,CAACpT,KAAX,MAAA,IAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,IAAA,iBAAA,CAAkB4D,WAAlB,EAAA,CAAA;KADF,CAAA,CAAA;AAGD,GAAA;AAED;AACF;AACA;AACA;;;EACE+lB,IAAI,CAACrqB,KAAD,EAAQ;IACV,IAAK0E,CAAAA,UAAL,CAAgB4E,WAAhB,CACE,IAAA,CAAKsL,cAAL,CAAoB5U,KAApB,CAA6B,GAAA,IAAA,CAAKyU,cADpC,CAAA,CAAA;AAGD,GAAA;AAED;AACF;AACA;;;AACE6V,EAAAA,IAAI,GAAG;AACL,IAAA,IAAA,CAAKD,IAAL,CAAU,IAAK5V,CAAAA,cAAL,GAAsB,CAAhC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACE8V,EAAAA,IAAI,GAAG;AACL,IAAA,IAAA,CAAKF,IAAL,CAAU,IAAK5V,CAAAA,cAAL,GAAsB,CAAhC,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEjP,MAAM,CAAC,GAAGob,IAAJ,EAAU;AAAA,IAAA,IAAA,eAAA,CAAA;;AACd,IAAA,CAAA,eAAA,GAAA,IAAA,CAAKtZ,SAAL,MAAA,IAAA,IAAA,eAAA,KAAA,KAAA,CAAA,IAAA,eAAA,CAAgB9B,MAAhB,CAAuB,GAAGob,IAA1B,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AACEja,EAAAA,UAAU,GAAG;AAAA,IAAA,IAAA,gBAAA,CAAA;;IACX,CAAKW,gBAAAA,GAAAA,IAAAA,CAAAA,SAAL,8DAAgBX,UAAhB,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEmD,EAAAA,KAAK,GAAG;IACN,IAAI,CAAC,KAAK3G,MAAL,CAAYC,MAAb,IAAuB,IAAA,CAAKomB,YAAhC,EAA8C;AAC5C,MAAA,OAAA;AACD,KAAA;;IAED,IAAKA,CAAAA,YAAL,GAAoB,IAApB,CAAA;IAEA,IAAKxoB,CAAAA,QAAL,CAAc,OAAd,CAAA,CAAA;IAEA,IAAKuO,CAAAA,MAAL,CAAY9Q,SAAZ,EAAA,CAAA;IACA,IAAK0E,CAAAA,MAAL,CAAY2G,KAAZ,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEjF,EAAAA,OAAO,GAAG;AAAA,IAAA,IAAA,aAAA,CAAA;;IACR,IAAI,CAAC,IAAK2kB,CAAAA,YAAV,EAAwB;AACtB,MAAA,IAAA,CAAKlqB,OAAL,CAAasoB,qBAAb,GAAqC,MAArC,CAAA;AACA,MAAA,IAAA,CAAK9d,KAAL,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED,IAAK9I,CAAAA,QAAL,CAAc,SAAd,CAAA,CAAA;IAEA,IAAKof,CAAAA,UAAL,GAAkB,EAAlB,CAAA;;IAEA,IAAI,IAAA,CAAK5Q,UAAT,EAAqB;AACnB,MAAA,IAAA,CAAKA,UAAL,CAAgBI,WAAhB,GAA8B,IAA9B,CAAA;AACA,MAAA,IAAA,CAAKJ,UAAL,CAAgBK,UAAhB,GAA6B,IAA7B,CAAA;AACD,KAAA;;IAED,CAAKtK,aAAAA,GAAAA,IAAAA,CAAAA,OAAL,wDAAc/G,MAAd,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKkG,UAAL,CAAgB+O,WAAhB,CAA4B/U,OAA5B,CAAqCoV,UAAD,IAAgB;AAAA,MAAA,IAAA,kBAAA,CAAA;;AAClD,MAAA,CAAA,kBAAA,GAAAA,UAAU,CAACpT,KAAX,MAAA,IAAA,IAAA,kBAAA,KAAA,KAAA,CAAA,IAAA,kBAAA,CAAkBmE,OAAlB,EAAA,CAAA;KADF,CAAA,CAAA;IAIA,IAAKvB,CAAAA,aAAL,CAAmBuB,OAAnB,EAAA,CAAA;IACA,IAAK0K,CAAAA,MAAL,CAAY9Q,SAAZ,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACE+rB,mBAAmB,CAACC,UAAD,EAAa;AAC9B,IAAA,IAAA,CAAKnnB,aAAL,CAAmBuhB,aAAnB,CAAiC4F,UAAjC,CAAA,CAAA;IACA,IAAK/lB,CAAAA,UAAL,CAAgB+O,WAAhB,CAA4B/U,OAA5B,CAAoC,CAACoV,UAAD,EAAaG,CAAb,KAAmB;AAAA,MAAA,IAAA,qBAAA,EAAA,gBAAA,CAAA;;AACrD,MAAA,IAAIyW,oBAAoB,GAAG,CAAC,CAAA,qBAAA,GAAA,CAAA,gBAAA,GAAA,IAAA,CAAKpjB,SAAN,MAAA,IAAA,IAAA,gBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAC,gBAAgBtH,CAAAA,KAAjB,MAA0B,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,GAAA,qBAAA,GAAA,CAA1B,IAA+B,CAA/B,GAAmCiU,CAA9D,CAAA;;MACA,IAAI,IAAA,CAAKU,OAAL,EAAJ,EAAoB;AAClB+V,QAAAA,oBAAoB,GAAG,IAAA,CAAK9V,cAAL,CAAoB8V,oBAApB,CAAvB,CAAA;AACD,OAAA;;MACD,IAAIA,oBAAoB,KAAKD,UAA7B,EAAyC;AACvC;QACA,IAAKjV,CAAAA,UAAL,CAAgB1B,UAAhB,EAA4B2W,UAA5B,EAAwC,IAAxC,EAFuC;;QAKvC,IAAIxW,CAAC,KAAK,CAAV,EAAa;AAAA,UAAA,IAAA,kBAAA,CAAA;;AACX,UAAA,IAAA,CAAK3M,SAAL,GAAiBwM,UAAU,CAACpT,KAA5B,CAAA;AACA,UAAA,CAAA,kBAAA,GAAAoT,UAAU,CAACpT,KAAX,MAAkBoD,IAAAA,IAAAA,kBAAAA,KAAAA,KAAAA,CAAAA,IAAAA,kBAAAA,CAAAA,WAAlB,CAA8B,IAA9B,CAAA,CAAA;AACD,SAAA;AACF,OAAA;KAdH,CAAA,CAAA;IAiBA,IAAK9C,CAAAA,QAAL,CAAc,QAAd,CAAA,CAAA;AACD,GAAA;AAGD;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEwU,EAAAA,UAAU,CAACmV,MAAD,EAAS3qB,KAAT,EAAgBiF,KAAhB,EAAuB;IAC/B,IAAI,IAAA,CAAK0P,OAAL,EAAJ,EAAoB;AAClB3U,MAAAA,KAAK,GAAG,IAAA,CAAK4U,cAAL,CAAoB5U,KAApB,CAAR,CAAA;AACD,KAAA;;IAED,IAAI2qB,MAAM,CAACjqB,KAAX,EAAkB;MAChB,IAAIiqB,MAAM,CAACjqB,KAAP,CAAaV,KAAb,KAAuBA,KAAvB,IAAgC,CAACiF,KAArC,EAA4C;AAC1C;AACA;AACA,QAAA,OAAA;AACD,OALe;;;MAQhB0lB,MAAM,CAACjqB,KAAP,CAAamE,OAAb,EAAA,CAAA;MACA8lB,MAAM,CAACjqB,KAAP,GAAetH,SAAf,CAAA;AACD,KAf8B;;;AAkB/B,IAAA,IAAI,CAAC,IAAA,CAAKub,OAAL,EAAD,KAAoB3U,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,IAAA,CAAKqU,WAAL,EAA1C,CAAJ,EAAmE;AACjE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMtU,QAAQ,GAAG,IAAA,CAAKqkB,WAAL,CAAiBpkB,KAAjB,CAAjB,CAAA;AACA2qB,IAAAA,MAAM,CAACjqB,KAAP,GAAe,IAAImC,KAAJ,CAAU9C,QAAV,EAAoBC,KAApB,EAA2B,IAA3B,CAAf,CAvB+B;;AA0B/B,IAAA,IAAIA,KAAK,KAAK,IAAK+C,CAAAA,SAAnB,EAA8B;AAC5B,MAAA,IAAA,CAAKuE,SAAL,GAAiBqjB,MAAM,CAACjqB,KAAxB,CAAA;AACD,KAAA;;AAEDiqB,IAAAA,MAAM,CAACjqB,KAAP,CAAauD,MAAb,CAAoB0mB,MAAM,CAAChyB,EAA3B,CAAA,CAAA;AACD,GAAA;AAED;;;AACAoO,EAAAA,sBAAsB,GAAG;IACvB,OAAO;AACL9N,MAAAA,CAAC,EAAE,IAAK6G,CAAAA,YAAL,CAAkB7G,CAAlB,GAAsB,CADpB;AAELC,MAAAA,CAAC,EAAE,IAAA,CAAK4G,YAAL,CAAkB5G,CAAlB,GAAsB,CAAA;KAF3B,CAAA;AAID,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;EACE+wB,UAAU,CAAChlB,KAAD,EAAQ;AAChB;AACA;IAEA,IAAI,IAAA,CAAKukB,YAAT,EAAuB;AACrB;AACA;AACA,MAAA,OAAA;AACD,KARe;AAWhB;;;IAEA,MAAM/pB,eAAe,GAAGJ,eAAe,CAAC,KAAKC,OAAN,EAAe,IAAf,CAAvC,CAAA;;IAEA,IAAI,CAAC2F,KAAD,IAAUrL,WAAW,CAAC6F,eAAD,EAAkB,IAAA,CAAK8pB,iBAAvB,CAAzB,EAAoE;AAClE;AACA,MAAA,OAAA;AACD,KAlBe;AAqBhB;;;AACAzwB,IAAAA,cAAc,CAAC,IAAA,CAAKywB,iBAAN,EAAyB9pB,eAAzB,CAAd,CAAA;IAEA,IAAKuB,CAAAA,QAAL,CAAc,cAAd,CAAA,CAAA;AAEAlI,IAAAA,cAAc,CAAC,IAAKgH,CAAAA,YAAN,EAAoB,IAAA,CAAKypB,iBAAzB,CAAd,CAAA;;AAEA,IAAA,IAAA,CAAKa,uBAAL,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAKppB,QAAL,CAAc,cAAd,CAAA,CA9BgB;AAiChB;;AACA,IAAA,IAAA,CAAK0D,UAAL,CAAgBK,MAAhB,CAAuB,IAAK5B,CAAAA,MAAL,CAAYC,MAAnC,CAAA,CAAA;;IAEA,IAAI,CAAC,IAAKqmB,CAAAA,QAAN,IAAkB/rB,MAAM,CAACktB,UAAP,CAAkB,oBAAlB,CAAwC/K,CAAAA,OAA9D,EAAuE;AACrE,MAAA,IAAA,CAAKpP,aAAL,EAAA,CAAA;AACD,KAAA;;IAED,IAAKzP,CAAAA,QAAL,CAAc,QAAd,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;EACE6H,cAAc,CAACof,OAAD,EAAU;IACtB,IAAKtf,CAAAA,SAAL,GAAiBpP,IAAI,CAACS,GAAL,CAASiuB,OAAT,EAAkB,CAAlB,CAAjB,CAAA;;IACA,IAAI,IAAA,CAAKD,EAAT,EAAa;AACX,MAAA,IAAA,CAAKA,EAAL,CAAQ3tB,KAAR,CAAc4tB,OAAd,GAAwB1E,MAAM,CAAC,IAAA,CAAK5a,SAAL,GAAiB,IAAA,CAAKrJ,OAAL,CAAaqJ,SAA/B,CAA9B,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;;;AACE8H,EAAAA,aAAa,GAAG;IACd,IAAI,CAAC,IAAKgZ,CAAAA,QAAV,EAAoB;AAAA,MAAA,IAAA,cAAA,CAAA;;MAClB,IAAKA,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACA,MAAA,CAAA,cAAA,GAAA,IAAA,CAAKlkB,OAAL,MAAc6H,IAAAA,IAAAA,cAAAA,KAAAA,KAAAA,CAAAA,IAAAA,cAAAA,CAAAA,SAAd,CAAwBlP,GAAxB,CAA4B,iBAA5B,CAAA,CAAA;AACD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;;;AACEisB,EAAAA,iBAAiB,GAAG;IAClB,IAAKF,CAAAA,UAAL,GADkB;AAIlB;AACA;AACA;AACA;;IACA,IAAI,mBAAA,CAAoBY,IAApB,CAAyBntB,MAAM,CAACJ,SAAP,CAAiBwtB,SAA1C,CAAJ,EAA0D;AACxDlZ,MAAAA,UAAU,CAAC,MAAM;AACf,QAAA,IAAA,CAAKqY,UAAL,EAAA,CAAA;OADQ,EAEP,GAFO,CAAV,CAAA;AAGD,KAAA;AACF,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;AACEG,EAAAA,uBAAuB,GAAG;AACxB,IAAA,IAAA,CAAKW,eAAL,CAAqB,CAArB,EAAwBrtB,MAAM,CAACwsB,WAA/B,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;;;AACEa,EAAAA,eAAe,CAAC9xB,CAAD,EAAIC,CAAJ,EAAO;AACpB,IAAA,IAAA,CAAK6Z,MAAL,CAAY9Z,CAAZ,GAAgBA,CAAhB,CAAA;AACA,IAAA,IAAA,CAAK8Z,MAAL,CAAY7Z,CAAZ,GAAgBA,CAAhB,CAAA;IACA,IAAK8H,CAAAA,QAAL,CAAc,oBAAd,CAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;AACA;;;AACE4oB,EAAAA,oBAAoB,GAAG;AACrB;AACA,IAAA,IAAA,CAAKrkB,OAAL,GAAehN,aAAa,CAAC,MAAD,EAAS,KAAT,CAA5B,CAAA;AACA,IAAA,IAAA,CAAKgN,OAAL,CAAa2O,YAAb,CAA0B,UAA1B,EAAsC,IAAtC,CAAA,CAAA;IACA,IAAK3O,CAAAA,OAAL,CAAa2O,YAAb,CAA0B,MAA1B,EAAkC,QAAlC,EAJqB;;AAOrB,IAAA,IAAA,CAAKuD,QAAL,GAAgB,IAAKlS,CAAAA,OAArB,CAPqB;AAUrB;;IACA,IAAKyiB,CAAAA,EAAL,GAAUzvB,aAAa,CAAC,UAAD,EAAa,KAAb,EAAoB,IAAKgN,CAAAA,OAAzB,CAAvB,CAAA;IACA,IAAKiK,CAAAA,UAAL,GAAkBjX,aAAa,CAAC,mBAAD,EAAsB,SAAtB,EAAiC,IAAKgN,CAAAA,OAAtC,CAA/B,CAAA;AACA,IAAA,IAAA,CAAK/B,SAAL,GAAiBjL,aAAa,CAAC,iBAAD,EAAoB,KAApB,EAA2B,IAAKiX,CAAAA,UAAhC,CAA9B,CAbqB;;AAgBrB,IAAA,IAAA,CAAKA,UAAL,CAAgB0E,YAAhB,CAA6B,sBAA7B,EAAqD,UAArD,CAAA,CAAA;AACA,IAAA,IAAA,CAAK1Q,SAAL,CAAe0Q,YAAf,CAA4B,WAA5B,EAAyC,KAAzC,CAAA,CAAA;AACA,IAAA,IAAA,CAAK1Q,SAAL,CAAe0Q,YAAf,CAA4B,IAA5B,EAAkC,aAAlC,CAAA,CAAA;IAEA,IAAKxP,CAAAA,UAAL,CAAgBsP,aAAhB,EAAA,CAAA;AAEA,IAAA,IAAA,CAAKoJ,EAAL,GAAU,IAAIO,EAAJ,CAAO,IAAP,CAAV,CAAA;AACA,IAAA,IAAA,CAAKP,EAAL,CAAQY,IAAR,EAAA,CAvBqB;;AA0BrB,IAAA,CAAC,IAAK1e,CAAAA,OAAL,CAAa5G,UAAb,IAA2BE,QAAQ,CAACoyB,IAArC,EAA2CnyB,WAA3C,CAAuD,IAAA,CAAK0M,OAA5D,CAAA,CAAA;AACD,GAAA;AAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;;AACEia,EAAAA,cAAc,GAAG;AACf,IAAA,OAAOA,cAAc,CACnB,IAAA,CAAKzc,SADc,EAEnB,KAAKuE,SAAL,GAAiB,IAAKA,CAAAA,SAAL,CAAelG,IAAhC,GAAuC,KAAKsoB,gBAFzB,EAGnB,IAHmB,CAArB,CAAA;AAKD,GAAA;AAED;AACF;AACA;AACA;;;AACE/U,EAAAA,OAAO,GAAG;IACR,OAAQ,IAAA,CAAKrV,OAAL,CAAa6c,IAAb,IAAqB,IAAK9H,CAAAA,WAAL,KAAqB,CAAlD,CAAA;AACD,GAAA;AAED;AACF;AACA;AACA;AACA;;;EACEiV,eAAe,CAAChqB,OAAD,EAAU;AACvB,IAAA,IAAI5B,MAAM,CAACktB,UAAP,CAAkB,0CAAlB,CAAA,CAA8D/K,OAAlE,EAA2E;MACzEvgB,OAAO,CAACsoB,qBAAR,GAAgC,MAAhC,CAAA;MACAtoB,OAAO,CAACsH,qBAAR,GAAgC,CAAhC,CAAA;AACD,KAAA;AAED;;;IACA,OAAO,EACL,GAAGsiB,cADE;MAEL,GAAG5pB,OAAAA;KAFL,CAAA;AAID,GAAA;;AAhiBqC;;;;"}