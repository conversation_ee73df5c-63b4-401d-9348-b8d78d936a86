{"version": 3, "file": "perfect-scrollbar.js", "sources": ["../src/lib/css.js", "../src/lib/dom.js", "../src/lib/class-names.js", "../src/lib/event-manager.js", "../src/process-scroll-diff.js", "../src/lib/util.js", "../src/update-geometry.js", "../src/handlers/click-rail.js", "../src/handlers/drag-thumb.js", "../src/handlers/keyboard.js", "../src/handlers/mouse-wheel.js", "../src/handlers/touch.js", "../src/index.js"], "sourcesContent": ["export function get(element) {\n  return getComputedStyle(element);\n}\n\nexport function set(element, obj) {\n  for (const key in obj) {\n    let val = obj[key];\n    if (typeof val === 'number') {\n      val = `${val}px`;\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n", "export function div(className) {\n  const div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nconst elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nexport function matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nexport function remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nexport function queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, child =>\n    matches(child, selector)\n  );\n}\n", "const cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: x => `ps__thumb-${x}`,\n    rail: x => `ps__rail-${x}`,\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: x => `ps--active-${x}`,\n    scrolling: x => `ps--scrolling-${x}`,\n  },\n};\n\nexport default cls;\n\n/*\n * Helper methods\n */\nconst scrollingClassTimeout = { x: null, y: null };\n\nexport function addScrollingClass(i, x) {\n  const classList = i.element.classList;\n  const className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nexport function removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    () => i.isAlive && i.element.classList.remove(cls.state.scrolling(x)),\n    i.settings.scrollingThreshold\n  );\n}\n\nexport function setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n", "class EventElement {\n  constructor(element) {\n    this.element = element;\n    this.handlers = {};\n  }\n\n  bind(eventName, handler) {\n    if (typeof this.handlers[eventName] === 'undefined') {\n      this.handlers[eventName] = [];\n    }\n    this.handlers[eventName].push(handler);\n    this.element.addEventListener(eventName, handler, false);\n  }\n\n  unbind(eventName, target) {\n    this.handlers[eventName] = this.handlers[eventName].filter(handler => {\n      if (target && handler !== target) {\n        return true;\n      }\n      this.element.removeEventListener(eventName, handler, false);\n      return false;\n    });\n  }\n\n  unbindAll() {\n    for (const name in this.handlers) {\n      this.unbind(name);\n    }\n  }\n\n  get isEmpty() {\n    return Object.keys(this.handlers).every(\n      key => this.handlers[key].length === 0\n    );\n  }\n}\n\nexport default class EventManager {\n  constructor() {\n    this.eventElements = [];\n  }\n\n  eventElement(element) {\n    let ee = this.eventElements.filter(ee => ee.element === element)[0];\n    if (!ee) {\n      ee = new EventElement(element);\n      this.eventElements.push(ee);\n    }\n    return ee;\n  }\n\n  bind(element, eventName, handler) {\n    this.eventElement(element).bind(eventName, handler);\n  }\n\n  unbind(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    ee.unbind(eventName, handler);\n\n    if (ee.isEmpty) {\n      // remove\n      this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n    }\n  }\n\n  unbindAll() {\n    this.eventElements.forEach(e => e.unbindAll());\n    this.eventElements = [];\n  }\n\n  once(element, eventName, handler) {\n    const ee = this.eventElement(element);\n    const onceHandler = evt => {\n      ee.unbind(eventName, onceHandler);\n      handler(evt);\n    };\n    ee.bind(eventName, onceHandler);\n  }\n}\n", "import { setScrollingClassInstantly } from './lib/class-names';\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  }\n\n  const evt = document.createEvent('CustomEvent');\n  evt.initCustomEvent(name, false, false, undefined);\n  return evt;\n}\n\nexport default function (i, axis, diff, useScrollingClass = true, forceFireReachEvent = false) {\n  let fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff(\n  i,\n  diff,\n  [contentHeight, containerHeight, scrollTop, y, up, down],\n  useScrollingClass = true,\n  forceFireReachEvent = false\n) {\n  const element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent(`ps-scroll-${y}`));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${up}`));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(`ps-scroll-${down}`));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(`ps-${y}-reach-${i.reach[y]}`));\n  }\n}\n", "import * as CSS from './css';\nimport * as DOM from './dom';\n\nexport function toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nexport function isEditable(el) {\n  return (\n    DOM.matches(el, 'input,[contenteditable]') ||\n    DOM.matches(el, 'select,[contenteditable]') ||\n    DOM.matches(el, 'textarea,[contenteditable]') ||\n    DOM.matches(el, 'button,[contenteditable]')\n  );\n}\n\nexport function outerWidth(element) {\n  const styles = CSS.get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nexport const env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n", "import * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport { toInt } from './lib/util';\n\n/* eslint-disable no-lonely-if */\n\nexport default function (i) {\n  const element = i.element;\n  const roundedScrollTop = Math.floor(element.scrollTop);\n  const rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.floor(rect.width);\n  i.containerHeight = Math.floor(rect.height);\n\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('x')).forEach((el) => DOM.remove(el));\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    DOM.queryChildren(element, cls.element.rail('y')).forEach((el) => DOM.remove(el));\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt((i.railXWidth * i.containerWidth) / i.contentWidth));\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  const xRailOffset = { width: i.railXWidth };\n  const roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  CSS.set(i.scrollbarXRail, xRailOffset);\n\n  const yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  CSS.set(i.scrollbarYRail, yRailOffset);\n\n  CSS.set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  CSS.set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n", "/* eslint-disable */\n\nimport updateGeometry from '../update-geometry';\n\nexport default function (i) {\n  // const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', (e) => e.stopPropagation());\n  i.event.bind(i.scrollbarYRail, 'mousedown', (e) => {\n    const positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    const direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', (e) => e.stopPropagation());\n  i.event.bind(i.scrollbarXRail, 'mousedown', (e) => {\n    const positionLeft =\n      e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    const direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n", "import cls, { addScrollingClass, removeScrollingClass } from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\n\nlet activeSlider = null; // Variable to track the currently active slider\n\nexport default function setupScrollHandlers(i) {\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail',\n  ]);\n\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail',\n  ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  [\n    containerDimension,\n    contentDimension,\n    pageAxis,\n    railDimension,\n    scrollbarAxis,\n    scrollbarDimension,\n    scrollAxis,\n    axis,\n    scrollbarRail,\n  ]\n) {\n  const element = i.element;\n  let startingScrollPosition = null;\n  let startingMousePagePosition = null;\n  let scrollBy = null;\n\n  function moveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageAxis] = e.touches[0][`page${axis.toUpperCase()}`];\n    }\n\n    // Only move if the active slider is the one we started with\n    if (activeSlider === scrollbarAxis) {\n      element[scrollAxis] =\n        startingScrollPosition + scrollBy * (e[pageAxis] - startingMousePagePosition);\n      addScrollingClass(i, axis);\n      updateGeometry(i);\n\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  function endHandler() {\n    removeScrollingClass(i, axis);\n    i[scrollbarRail].classList.remove(cls.state.clicking);\n    document.removeEventListener('mousemove', moveHandler);\n    document.removeEventListener('mouseup', endHandler);\n    document.removeEventListener('touchmove', moveHandler);\n    document.removeEventListener('touchend', endHandler);\n    activeSlider = null; // Reset active slider when interaction ends\n  }\n\n  function bindMoves(e) {\n    if (activeSlider === null) {\n      // Only bind if no slider is currently active\n      activeSlider = scrollbarAxis; // Set current slider as active\n\n      startingScrollPosition = element[scrollAxis];\n      if (e.touches) {\n        e[pageAxis] = e.touches[0][`page${axis.toUpperCase()}`];\n      }\n      startingMousePagePosition = e[pageAxis];\n      scrollBy =\n        (i[contentDimension] - i[containerDimension]) / (i[railDimension] - i[scrollbarDimension]);\n\n      if (!e.touches) {\n        document.addEventListener('mousemove', moveHandler);\n        document.addEventListener('mouseup', endHandler);\n      } else {\n        document.addEventListener('touchmove', moveHandler, { passive: false });\n        document.addEventListener('touchend', endHandler);\n      }\n\n      i[scrollbarRail].classList.add(cls.state.clicking);\n    }\n\n    e.stopPropagation();\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  i[scrollbarAxis].addEventListener('mousedown', bindMoves);\n  i[scrollbarAxis].addEventListener('touchstart', bindMoves);\n}\n", "/* eslint-disable */\n\nimport * as DOM from '../lib/dom';\nimport updateGeometry from '../update-geometry';\nimport { isEditable } from '../lib/util';\n\nexport default function (i) {\n  const element = i.element;\n\n  const elementHovered = () => DOM.matches(element, ':hover');\n  const scrollbarFocused = () =>\n    DOM.matches(i.scrollbarX, ':focus') || DOM.matches(i.scrollbarY, ':focus');\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    const scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', (e) => {\n    if ((e.isDefaultPrevented && e.isDefaultPrevented()) || e.defaultPrevented) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    let activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    let deltaX = 0;\n    let deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n", "/* eslint-disable */\n\nimport * as CSS from '../lib/css';\nimport cls from '../lib/class-names';\nimport updateGeometry from '../update-geometry';\nimport { env } from '../lib/util';\n\nexport default function (i) {\n  const element = i.element;\n\n  let shouldPrevent = false;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    const roundedScrollTop = Math.floor(element.scrollTop);\n    const isTop = element.scrollTop === 0;\n    const isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    const isLeft = element.scrollLeft === 0;\n    const isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    let hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    let deltaX = e.deltaX;\n    let deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    const [deltaX, deltaY] = getDeltaFromEvent(e);\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    let shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n", "import updateGeometry from '../update-geometry';\nimport cls from '../lib/class-names';\nimport * as CSS from '../lib/css';\nimport { env } from '../lib/util';\n\nexport default function (i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  const element = i.element;\n\n  const state = {\n    startOffset: {},\n    startTime: 0,\n    speed: {},\n    easingLoop: null,\n  };\n\n  function shouldPrevent(deltaX, deltaY) {\n    const scrollTop = Math.floor(element.scrollTop);\n    const scrollLeft = element.scrollLeft;\n    const magnitudeX = Math.abs(deltaX);\n    const magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    }\n    // Maybe IE pointer\n    return e;\n  }\n\n  function shouldHandle(e) {\n    if (e.target === i.scrollbarX || e.target === i.scrollbarY) {\n      return false;\n    }\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    const touch = getTouch(e);\n\n    state.startOffset.pageX = touch.pageX;\n    state.startOffset.pageY = touch.pageY;\n\n    state.startTime = new Date().getTime();\n\n    if (state.easingLoop !== null) {\n      clearInterval(state.easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    let cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      const style = CSS.get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        const maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        const maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      const touch = getTouch(e);\n\n      const currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      const differenceX = currentOffset.pageX - state.startOffset.pageX;\n      const differenceY = currentOffset.pageY - state.startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      state.startOffset = currentOffset;\n\n      const currentTime = new Date().getTime();\n\n      const timeGap = currentTime - state.startTime;\n      if (timeGap > 0) {\n        state.speed.x = differenceX / timeGap;\n        state.speed.y = differenceY / timeGap;\n        state.startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        // Prevent the default behavior if the event is cancelable\n        if (e.cancelable) {\n          e.preventDefault();\n        }\n      }\n    }\n  }\n\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(state.easingLoop);\n      state.easingLoop = setInterval(() => {\n        if (i.isInitialized) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (!state.speed.x && !state.speed.y) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (Math.abs(state.speed.x) < 0.01 && Math.abs(state.speed.y) < 0.01) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        applyTouchMove(state.speed.x * 30, state.speed.y * 30);\n\n        state.speed.x *= 0.8;\n        state.speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n", "/* eslint-disable */\n\nimport * as CSS from './lib/css';\nimport * as DOM from './lib/dom';\nimport cls from './lib/class-names';\nimport EventManager from './lib/event-manager';\nimport processScrollDiff from './process-scroll-diff';\nimport updateGeometry from './update-geometry';\nimport { toInt, outerWidth } from './lib/util';\n\nimport clickRail from './handlers/click-rail';\nimport dragThumb from './handlers/drag-thumb';\nimport keyboard from './handlers/keyboard';\nimport wheel from './handlers/mouse-wheel';\nimport touch from './handlers/touch';\n\nconst defaultSettings = () => ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n});\n\nconst handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': dragThumb,\n  keyboard,\n  wheel,\n  touch,\n};\n\nexport default class PerfectScrollbar {\n  constructor(element, userSettings = {}) {\n    if (typeof element === 'string') {\n      element = document.querySelector(element);\n    }\n\n    if (!element || !element.nodeName) {\n      throw new Error('no element is specified to initialize PerfectScrollbar');\n    }\n\n    this.element = element;\n\n    element.classList.add(cls.main);\n\n    this.settings = defaultSettings();\n    for (const key in userSettings) {\n      this.settings[key] = userSettings[key];\n    }\n\n    this.containerWidth = null;\n    this.containerHeight = null;\n    this.contentWidth = null;\n    this.contentHeight = null;\n\n    const focus = () => element.classList.add(cls.state.focus);\n    const blur = () => element.classList.remove(cls.state.focus);\n\n    this.isRtl = CSS.get(element).direction === 'rtl';\n    if (this.isRtl === true) {\n      element.classList.add(cls.rtl);\n    }\n    this.isNegativeScroll = (() => {\n      const originalScrollLeft = element.scrollLeft;\n      let result = null;\n      element.scrollLeft = -1;\n      result = element.scrollLeft < 0;\n      element.scrollLeft = originalScrollLeft;\n      return result;\n    })();\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? element.scrollWidth - element.clientWidth\n      : 0;\n    this.event = new EventManager();\n    this.ownerDocument = element.ownerDocument || document;\n\n    this.scrollbarXRail = DOM.div(cls.element.rail('x'));\n    element.appendChild(this.scrollbarXRail);\n    this.scrollbarX = DOM.div(cls.element.thumb('x'));\n    this.scrollbarXRail.appendChild(this.scrollbarX);\n    this.scrollbarX.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarX, 'focus', focus);\n    this.event.bind(this.scrollbarX, 'blur', blur);\n    this.scrollbarXActive = null;\n    this.scrollbarXWidth = null;\n    this.scrollbarXLeft = null;\n    const railXStyle = CSS.get(this.scrollbarXRail);\n    this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n    if (isNaN(this.scrollbarXBottom)) {\n      this.isScrollbarXUsingBottom = false;\n      this.scrollbarXTop = toInt(railXStyle.top);\n    } else {\n      this.isScrollbarXUsingBottom = true;\n    }\n    this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n    // Set rail to display:block to calculate margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n    CSS.set(this.scrollbarXRail, { display: '' });\n    this.railXWidth = null;\n    this.railXRatio = null;\n\n    this.scrollbarYRail = DOM.div(cls.element.rail('y'));\n    element.appendChild(this.scrollbarYRail);\n    this.scrollbarY = DOM.div(cls.element.thumb('y'));\n    this.scrollbarYRail.appendChild(this.scrollbarY);\n    this.scrollbarY.setAttribute('tabindex', 0);\n    this.event.bind(this.scrollbarY, 'focus', focus);\n    this.event.bind(this.scrollbarY, 'blur', blur);\n    this.scrollbarYActive = null;\n    this.scrollbarYHeight = null;\n    this.scrollbarYTop = null;\n    const railYStyle = CSS.get(this.scrollbarYRail);\n    this.scrollbarYRight = parseInt(railYStyle.right, 10);\n    if (isNaN(this.scrollbarYRight)) {\n      this.isScrollbarYUsingRight = false;\n      this.scrollbarYLeft = toInt(railYStyle.left);\n    } else {\n      this.isScrollbarYUsingRight = true;\n    }\n    this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n    this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n    CSS.set(this.scrollbarYRail, { display: '' });\n    this.railYHeight = null;\n    this.railYRatio = null;\n\n    this.reach = {\n      x:\n        element.scrollLeft <= 0\n          ? 'start'\n          : element.scrollLeft >= this.contentWidth - this.containerWidth\n          ? 'end'\n          : null,\n      y:\n        element.scrollTop <= 0\n          ? 'start'\n          : element.scrollTop >= this.contentHeight - this.containerHeight\n          ? 'end'\n          : null,\n    };\n\n    this.isAlive = true;\n\n    this.settings.handlers.forEach((handlerName) => handlers[handlerName](this));\n\n    this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n    this.lastScrollLeft = element.scrollLeft; // for onScroll only\n    this.event.bind(this.element, 'scroll', (e) => this.onScroll(e));\n    updateGeometry(this);\n  }\n\n  update() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    // Recalcuate negative scrollLeft adjustment\n    this.negativeScrollAdjustment = this.isNegativeScroll\n      ? this.element.scrollWidth - this.element.clientWidth\n      : 0;\n\n    // Recalculate rail margins\n    CSS.set(this.scrollbarXRail, { display: 'block' });\n    CSS.set(this.scrollbarYRail, { display: 'block' });\n    this.railXMarginWidth =\n      toInt(CSS.get(this.scrollbarXRail).marginLeft) +\n      toInt(CSS.get(this.scrollbarXRail).marginRight);\n    this.railYMarginHeight =\n      toInt(CSS.get(this.scrollbarYRail).marginTop) +\n      toInt(CSS.get(this.scrollbarYRail).marginBottom);\n\n    // Hide scrollbars not to affect scrollWidth and scrollHeight\n    CSS.set(this.scrollbarXRail, { display: 'none' });\n    CSS.set(this.scrollbarYRail, { display: 'none' });\n\n    updateGeometry(this);\n\n    processScrollDiff(this, 'top', 0, false, true);\n    processScrollDiff(this, 'left', 0, false, true);\n\n    CSS.set(this.scrollbarXRail, { display: '' });\n    CSS.set(this.scrollbarYRail, { display: '' });\n  }\n\n  onScroll(e) {\n    if (!this.isAlive) {\n      return;\n    }\n\n    updateGeometry(this);\n    processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n    processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n\n    this.lastScrollTop = Math.floor(this.element.scrollTop);\n    this.lastScrollLeft = this.element.scrollLeft;\n  }\n\n  destroy() {\n    if (!this.isAlive) {\n      return;\n    }\n\n    this.event.unbindAll();\n    DOM.remove(this.scrollbarX);\n    DOM.remove(this.scrollbarY);\n    DOM.remove(this.scrollbarXRail);\n    DOM.remove(this.scrollbarYRail);\n    this.removePsClasses();\n\n    // unset elements\n    this.element = null;\n    this.scrollbarX = null;\n    this.scrollbarY = null;\n    this.scrollbarXRail = null;\n    this.scrollbarYRail = null;\n\n    this.isAlive = false;\n  }\n\n  removePsClasses() {\n    this.element.className = this.element.className\n      .split(' ')\n      .filter((name) => !name.match(/^ps([-_].+|)$/))\n      .join(' ');\n  }\n}\n"], "names": ["const", "let", "this", "processScrollDiff", "DOM.matches", "CSS.get", "DOM.query<PERSON><PERSON><PERSON>n", "DOM.remove", "CSS.set", "dragThumb", "DOM.div"], "mappings": ";;;;;;;;;;;;EAAO,SAAS,GAAG,CAAC,OAAO,EAAE;IAC3B,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC;GAClC;;EAEM,SAAS,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE;IAChC,KAAKA,IAAM,GAAG,IAAI,GAAG,EAAE;MACrBC,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;MACnB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAC3B,GAAG,GAAM,GAAG,OAAI,CAAC;OAClB;MACD,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KAC1B;IACD,OAAO,OAAO,CAAC;;;ECZV,SAAS,GAAG,CAAC,SAAS,EAAE;IAC7BD,IAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC1C,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC;IAC1B,OAAO,GAAG,CAAC;GACZ;;EAEDA,IAAM,SAAS;IACb,OAAO,OAAO,KAAK,WAAW;KAC7B,OAAO,CAAC,SAAS,CAAC,OAAO;MACxB,OAAO,CAAC,SAAS,CAAC,qBAAqB;MACvC,OAAO,CAAC,SAAS,CAAC,kBAAkB;MACpC,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;;AAEzC,EAAO,SAAS,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE;IACtC,IAAI,CAAC,SAAS,EAAE;MACd,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;;IAED,OAAO,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;GACvC;;AAED,EAAO,SAAS,MAAM,CAAC,OAAO,EAAE;IAC9B,IAAI,OAAO,CAAC,MAAM,EAAE;MAClB,OAAO,CAAC,MAAM,EAAE,CAAC;KAClB,MAAM;MACL,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;OACzC;KACF;GACF;;AAED,EAAO,SAAS,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE;IAC/C,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,YAAE,OAAM,SACzD,OAAO,CAAC,KAAK,EAAE,QAAQ,IAAC;KACzB,CAAC;GACH;;ECnCDA,IAAM,GAAG,GAAG;IACV,IAAI,EAAE,IAAI;IACV,GAAG,EAAE,SAAS;IACd,OAAO,EAAE;MACP,KAAK,YAAE,GAAE,yBAAgB,CAAC,IAAE;MAC5B,IAAI,YAAE,GAAE,wBAAe,CAAC,IAAE;MAC1B,SAAS,EAAE,oBAAoB;KAChC;IACD,KAAK,EAAE;MACL,KAAK,EAAE,WAAW;MAClB,QAAQ,EAAE,cAAc;MACxB,MAAM,YAAE,GAAE,0BAAiB,CAAC,IAAE;MAC9B,SAAS,YAAE,GAAE,6BAAoB,CAAC,IAAE;KACrC;GACF,CAAC;;;;;EAOFA,IAAM,qBAAqB,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC;;AAEnD,EAAO,SAAS,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE;IACtCA,IAAM,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;IACtCA,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;IAEzC,IAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;MACjC,YAAY,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;KACxC,MAAM;MACL,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;KAC1B;GACF;;AAED,EAAO,SAAS,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE;IACzC,qBAAqB,CAAC,CAAC,CAAC,GAAG,UAAU;kBAChC,SAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,IAAC;MACrE,CAAC,CAAC,QAAQ,CAAC,kBAAkB;KAC9B,CAAC;GACH;;AAED,EAAO,SAAS,0BAA0B,CAAC,CAAC,EAAE,CAAC,EAAE;IAC/C,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACxB,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;GAC5B;;EC5CD,IAAM,YAAY,GAChB,qBAAW,CAAC,OAAO,EAAE;IACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;EACvB;;+DAAG;;EAEH,uBAAE,sBAAK,SAAS,EAAE,OAAO,EAAE;IACzB,IAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;MACrD,IAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KAC/B;IACH,IAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;EAC3D,EAAC;;EAEH,uBAAE,0BAAO,SAAS,EAAE,MAAM,EAAE;;AAAC;IACzB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,WAAC,SAAQ;MACjE,IAAI,MAAM,IAAI,OAAO,KAAK,MAAM,EAAE;QAClC,OAAS,IAAI,CAAC;OACb;MACDE,MAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;MAC9D,OAAS,KAAK,CAAC;KACd,CAAC,CAAC;EACL,EAAC;;EAEH,uBAAE,kCAAY;IACZ,KAAOF,IAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;MAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACnB;EACH,EAAC;;EAEH,mBAAM,0BAAU;;AAAC;IACf,OAAS,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK;MACvC,UAAE,KAAI,SAAGE,MAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,IAAC;KACvC,CAAC;EACJ,CAAC;;wEACF;;EAEc,IAAM,YAAY,GAC/B,qBAAW,GAAG;IACZ,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;EAC1B,EAAC;;EAEH,uBAAE,sCAAa,OAAO,EAAE;IACtB,IAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,WAAC,IAAG,SAAG,EAAE,CAAC,OAAO,KAAK,UAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,IAAM,CAAC,EAAE,EAAE;MACP,EAAE,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC;MACjC,IAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7B;IACH,OAAS,EAAE,CAAC;EACZ,EAAC;;EAEH,uBAAE,sBAAK,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;IAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;EACtD,EAAC;;EAEH,uBAAE,0BAAO,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;IACpC,IAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACxC,EAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;;IAE9B,IAAI,EAAE,CAAC,OAAO,EAAE;;MAEd,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAC9D;EACH,EAAC;;EAEH,uBAAE,kCAAY;IACV,IAAI,CAAC,aAAa,CAAC,OAAO,WAAC,GAAE,SAAG,CAAC,CAAC,SAAS,KAAE,CAAC,CAAC;IAC/C,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;EAC1B,EAAC;;EAEH,uBAAE,sBAAK,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;IAClC,IAAQ,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACtCF,IAAM,WAAW,aAAG,KAAI;MACxB,EAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;MAClC,OAAO,CAAC,GAAG,CAAC,CAAC;KACd,CAAC;IACJ,EAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;EAClC,CAAC;;EC3EH,SAAS,WAAW,CAAC,IAAI,EAAE;IACzB,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,UAAU,EAAE;MAC5C,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;KAC9B;;IAEDA,IAAM,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAChD,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IACnD,OAAO,GAAG,CAAC;GACZ;;AAED,EAAe,4BAAU,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAwB,EAAE,mBAA2B,EAAE;yDAAtC,GAAG;6DAAyB,GAAG;AAAQ;IAC9FC,IAAI,MAAM,CAAC;IACX,IAAI,IAAI,KAAK,KAAK,EAAE;MAClB,MAAM,GAAG,CAAC,eAAe,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;KAC/E,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE;MAC1B,MAAM,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;KACjF,MAAM;MACL,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACrD;;IAEDE,mBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;GAC5E;;EAED,SAASA,mBAAiB;IACxB,CAAC;IACD,IAAI;IACJ,GAAwD;IACxD,iBAAwB;IACxB,mBAA2B;IAC3B;+BAHgB;iCAAiB;2BAAW;mBAAG;oBAAI;;yDAClC,GAAG;6DACD,GAAG;AACrB;IACDH,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;;IAG1B,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;;IAGlB,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;MAC1B,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;KACtB;;;IAGD,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE;MAClE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;KACpB;;IAED,IAAI,IAAI,EAAE;MACR,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,CAAC,EAAG,CAAC,CAAC;;MAErD,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,EAAE,EAAG,CAAC,CAAC;OACvD,MAAM,IAAI,IAAI,GAAG,CAAC,EAAE;QACnB,OAAO,CAAC,aAAa,CAAC,WAAW,iBAAc,IAAI,EAAG,CAAC,CAAC;OACzD;;MAED,IAAI,iBAAiB,EAAE;QACrB,0BAA0B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;OAClC;KACF;;IAED,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,mBAAmB,CAAC,EAAE;MAC/C,OAAO,CAAC,aAAa,CAAC,WAAW,UAAO,CAAC,gBAAU,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACnE;GACF;;EC7DM,SAAS,KAAK,CAAC,CAAC,EAAE;IACvB,OAAO,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;GAC7B;;AAED,EAAO,SAAS,UAAU,CAAC,EAAE,EAAE;IAC7B;MACEI,OAAW,CAAC,EAAE,EAAE,yBAAyB,CAAC;MAC1CA,OAAW,CAAC,EAAE,EAAE,0BAA0B,CAAC;MAC3CA,OAAW,CAAC,EAAE,EAAE,4BAA4B,CAAC;MAC7CA,OAAW,CAAC,EAAE,EAAE,0BAA0B,CAAC;MAC3C;GACH;;AAED,EAAO,SAAS,UAAU,CAAC,OAAO,EAAE;IAClCJ,IAAM,MAAM,GAAGK,GAAO,CAAC,OAAO,CAAC,CAAC;IAChC;MACE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;MACnB,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;MACzB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC;MAC1B,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC;MAC7B,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC;MAC9B;GACH;;AAED,EAAOL,IAAM,GAAG,GAAG;IACjB,QAAQ;MACN,OAAO,QAAQ,KAAK,WAAW;MAC/B,kBAAkB,IAAI,QAAQ,CAAC,eAAe,CAAC,KAAK;IACtD,aAAa;MACX,OAAO,MAAM,KAAK,WAAW;OAC5B,cAAc,IAAI,MAAM;SACtB,gBAAgB,IAAI,MAAM,CAAC,SAAS;UACnC,MAAM,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;SACrC,MAAM,CAAC,aAAa,IAAI,QAAQ,YAAY,MAAM,CAAC,aAAa,CAAC,CAAC;IACvE,iBAAiB;MACf,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,gBAAgB;IAChE,QAAQ;MACN,OAAO,SAAS,KAAK,WAAW;MAChC,SAAS,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC;GACnD,CAAC;;;;ACnCF,EAAe,yBAAU,CAAC,EAAE;IAC1BA,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;IAC1BA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACvDA,IAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;;IAE7C,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;IAE5C,CAAC,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;IACrC,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;;IAEvC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE;;MAEvCM,aAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,WAAE,EAAE,EAAE,SAAGC,MAAU,CAAC,EAAE,IAAC,CAAC,CAAC;MAClF,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;KACvC;IACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE;;MAEvCD,aAAiB,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,WAAE,EAAE,EAAE,SAAGC,MAAU,CAAC,EAAE,IAAC,CAAC,CAAC;MAClF,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;KACvC;;IAED;MACE,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe;MAC3B,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,YAAY;MAClE;MACA,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;MAC1B,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,gBAAgB,CAAC;MACrD,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,UAAU,CAAC;MAC/C,CAAC,CAAC,eAAe,GAAG,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;MAC/F,CAAC,CAAC,cAAc,GAAG,KAAK;QACtB,CAAC,CAAC,CAAC,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC;WACpF,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc,CAAC;OACtC,CAAC;KACH,MAAM;MACL,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC5B;;IAED;MACE,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe;MAC3B,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,QAAQ,CAAC,mBAAmB,GAAG,CAAC,CAAC,aAAa;MACpE;MACA,CAAC,CAAC,gBAAgB,GAAG,IAAI,CAAC;MAC1B,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,iBAAiB,CAAC;MACxD,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,WAAW,CAAC;MACjD,CAAC,CAAC,gBAAgB,GAAG,YAAY;QAC/B,CAAC;QACD,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,aAAa,CAAC;OAC7D,CAAC;MACF,CAAC,CAAC,aAAa,GAAG,KAAK;QACrB,CAAC,gBAAgB,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,CAAC;WACrD,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe,CAAC;OACxC,CAAC;KACH,MAAM;MACL,CAAC,CAAC,gBAAgB,GAAG,KAAK,CAAC;KAC5B;;IAED,IAAI,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,EAAE;MACxD,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,eAAe,CAAC;KACrD;IACD,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,EAAE;MACzD,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,gBAAgB,CAAC;KACtD;;IAED,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;;IAEtB,IAAI,CAAC,CAAC,gBAAgB,EAAE;MACtB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9C,MAAM;MACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC;MACtB,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC;MACrB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC;KAC5D;IACD,IAAI,CAAC,CAAC,gBAAgB,EAAE;MACtB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9C,MAAM;MACL,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC;MACvB,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC;MACpB,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;KACvB;GACF;;EAED,SAAS,YAAY,CAAC,CAAC,EAAE,SAAS,EAAE;IAClC,IAAI,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE;MACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;KAChE;IACD,IAAI,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE;MACjC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;KAChE;IACD,OAAO,SAAS,CAAC;GAClB;;EAED,SAAS,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE;IAC7BP,IAAM,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC;IAC5CA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;IAEvD,IAAI,CAAC,CAAC,KAAK,EAAE;MACX,WAAW,CAAC,IAAI;QACd,CAAC,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,YAAY,CAAC;KACvF,MAAM;MACL,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;KACvC;IACD,IAAI,CAAC,CAAC,uBAAuB,EAAE;MAC7B,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;KAC5D,MAAM;MACL,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,aAAa,GAAG,gBAAgB,CAAC;KACtD;IACDQ,GAAO,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;;IAEvCR,IAAM,WAAW,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;IACrE,IAAI,CAAC,CAAC,sBAAsB,EAAE;MAC5B,IAAI,CAAC,CAAC,KAAK,EAAE;QACX,WAAW,CAAC,KAAK;UACf,CAAC,CAAC,YAAY;WACb,CAAC,CAAC,wBAAwB,GAAG,OAAO,CAAC,UAAU,CAAC;UACjD,CAAC,CAAC,eAAe;UACjB,CAAC,CAAC,oBAAoB;UACtB,CAAC,CAAC;OACL,MAAM;QACL,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,eAAe,GAAG,OAAO,CAAC,UAAU,CAAC;OAC5D;KACF,MAAM;MACL,IAAI,CAAC,CAAC,KAAK,EAAE;QACX,WAAW,CAAC,IAAI;UACd,CAAC,CAAC,wBAAwB;UAC1B,OAAO,CAAC,UAAU;UAClB,CAAC,CAAC,cAAc,GAAG,CAAC;UACpB,CAAC,CAAC,YAAY;UACd,CAAC,CAAC,cAAc;UAChB,CAAC,CAAC,oBAAoB,CAAC;OAC1B,MAAM;QACL,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC;OAC1D;KACF;IACDQ,GAAO,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;;IAEvCA,GAAO,CAAC,CAAC,CAAC,UAAU,EAAE;MACpB,IAAI,EAAE,CAAC,CAAC,cAAc;MACtB,KAAK,EAAE,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,gBAAgB;KAC9C,CAAC,CAAC;IACHA,GAAO,CAAC,CAAC,CAAC,UAAU,EAAE;MACpB,GAAG,EAAE,CAAC,CAAC,aAAa;MACpB,MAAM,EAAE,CAAC,CAAC,gBAAgB,GAAG,CAAC,CAAC,gBAAgB;KAChD,CAAC,CAAC;GACJ;;ECzJD;;AAIA,EAAe,oBAAU,CAAC,EAAE;;;IAG1B,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,YAAG,CAAC,EAAE,SAAG,CAAC,CAAC,eAAe,KAAE,CAAC,CAAC;IACpE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,YAAG,CAAC,EAAE;MAC9CR,IAAM,WAAW,GAAG,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,GAAG,CAAC;MAChGA,IAAM,SAAS,GAAG,WAAW,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;MAEzD,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,SAAS,GAAG,CAAC,CAAC,eAAe,CAAC;MACrD,cAAc,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,eAAe,EAAE,CAAC;KACrB,CAAC,CAAC;;IAEH,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,WAAW,YAAG,CAAC,EAAE,SAAG,CAAC,CAAC,eAAe,KAAE,CAAC,CAAC;IACpE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,YAAG,CAAC,EAAE;MAC9CA,IAAM,YAAY;QAChB,CAAC,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,cAAc,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;MAC/EA,IAAM,SAAS,GAAG,YAAY,GAAG,CAAC,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;MAE3D,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC;MACrD,cAAc,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,eAAe,EAAE,CAAC;KACrB,CAAC,CAAC;GACJ;;EC1BDC,IAAI,YAAY,GAAG,IAAI,CAAC;;AAExB,EAAe,SAAS,mBAAmB,CAAC,CAAC,EAAE;IAC7C,sBAAsB,CAAC,CAAC,EAAE;MACxB,iBAAiB;MACjB,eAAe;MACf,OAAO;MACP,aAAa;MACb,YAAY;MACZ,kBAAkB;MAClB,WAAW;MACX,GAAG;MACH,gBAAgB,EACjB,CAAC,CAAC;;IAEH,sBAAsB,CAAC,CAAC,EAAE;MACxB,gBAAgB;MAChB,cAAc;MACd,OAAO;MACP,YAAY;MACZ,YAAY;MACZ,iBAAiB;MACjB,YAAY;MACZ,GAAG;MACH,gBAAgB,EACjB,CAAC,CAAC;GACJ;;EAED,SAAS,sBAAsB;IAC7B,CAAC;IACD,GAUC;IACD;oCATE;kCACA;0BACA;+BACA;+BACA;oCACA;4BACA;sBACA;;AAED;IACDD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;IAC1BC,IAAI,sBAAsB,GAAG,IAAI,CAAC;IAClCA,IAAI,yBAAyB,GAAG,IAAI,CAAC;IACrCA,IAAI,QAAQ,GAAG,IAAI,CAAC;;IAEpB,SAAS,WAAW,CAAC,CAAC,EAAE;MACtB,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;QAC7B,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAQ,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;OACzD;;;MAGD,IAAI,YAAY,KAAK,aAAa,EAAE;QAClC,OAAO,CAAC,UAAU,CAAC;UACjB,sBAAsB,GAAG,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,yBAAyB,CAAC,CAAC;QAChF,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3B,cAAc,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC,cAAc,EAAE,CAAC;OACpB;KACF;;IAED,SAAS,UAAU,GAAG;MACpB,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;MACtD,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;MACvD,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;MACpD,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;MACvD,QAAQ,CAAC,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;MACrD,YAAY,GAAG,IAAI,CAAC;KACrB;;IAED,SAAS,SAAS,CAAC,CAAC,EAAE;MACpB,IAAI,YAAY,KAAK,IAAI,EAAE;;QAEzB,YAAY,GAAG,aAAa,CAAC;;QAE7B,sBAAsB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,CAAC,OAAO,EAAE;UACb,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAQ,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;SACzD;QACD,yBAAyB,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;QACxC,QAAQ;UACN,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;;QAE7F,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;UACd,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;UACpD,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SAClD,MAAM;UACL,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;UACxE,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;SACnD;;QAED,CAAC,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;OACpD;;MAED,CAAC,CAAC,eAAe,EAAE,CAAC;MACpB,IAAI,CAAC,CAAC,UAAU,EAAE;QAChB,CAAC,CAAC,cAAc,EAAE,CAAC;OACpB;KACF;;IAED,CAAC,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IAC1D,CAAC,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;GAC5D;;EC7GD;;AAMA,EAAe,mBAAU,CAAC,EAAE;IAC1BD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;IAE1BA,IAAM,cAAc,eAAM,SAAGI,OAAW,CAAC,OAAO,EAAE,QAAQ,IAAC,CAAC;IAC5DJ,IAAM,gBAAgB,eAAM,SAC1BI,OAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAIA,OAAW,CAAC,CAAC,CAAC,UAAU,EAAE,QAAQ,IAAC,CAAC;;IAE7E,SAAS,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE;MAC5CJ,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;MAChD,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;UACvB,OAAO,KAAK,CAAC;SACd;QACD;UACE,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC;WAC7B,SAAS,IAAI,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe,IAAI,MAAM,GAAG,CAAC,CAAC;UAChE;UACA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SACrC;OACF;;MAEDA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;MACtC,IAAI,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;UACvB,OAAO,KAAK,CAAC;SACd;QACD;UACE,CAAC,UAAU,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC;WAC9B,UAAU,IAAI,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc,IAAI,MAAM,GAAG,CAAC,CAAC;UAC/D;UACA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;SACrC;OACF;MACD,OAAO,IAAI,CAAC;KACb;;IAED,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,SAAS,YAAG,CAAC,EAAE;MAC3C,IAAI,CAAC,CAAC,CAAC,kBAAkB,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,gBAAgB,EAAE;QAC1E,OAAO;OACR;;MAED,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE;QAC5C,OAAO;OACR;;MAEDC,IAAI,aAAa,GAAG,QAAQ,CAAC,aAAa;UACtC,QAAQ,CAAC,aAAa;UACtB,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC;MAClC,IAAI,aAAa,EAAE;QACjB,IAAI,aAAa,CAAC,OAAO,KAAK,QAAQ,EAAE;UACtC,aAAa,GAAG,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC;SAC7D,MAAM;;UAEL,OAAO,aAAa,CAAC,UAAU,EAAE;YAC/B,aAAa,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa,CAAC;WACxD;SACF;QACD,IAAI,UAAU,CAAC,aAAa,CAAC,EAAE;UAC7B,OAAO;SACR;OACF;;MAEDA,IAAI,MAAM,GAAG,CAAC,CAAC;MACfA,IAAI,MAAM,GAAG,CAAC,CAAC;;MAEf,QAAQ,CAAC,CAAC,KAAK;QACb,KAAK,EAAE;UACL,IAAI,CAAC,CAAC,OAAO,EAAE;YACb,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC;WAC1B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;YACnB,MAAM,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC;WAC5B,MAAM;YACL,MAAM,GAAG,CAAC,EAAE,CAAC;WACd;UACD,MAAM;QACR,KAAK,EAAE;UACL,IAAI,CAAC,CAAC,OAAO,EAAE;YACb,MAAM,GAAG,CAAC,CAAC,aAAa,CAAC;WAC1B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;YACnB,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;WAC5B,MAAM;YACL,MAAM,GAAG,EAAE,CAAC;WACb;UACD,MAAM;QACR,KAAK,EAAE;UACL,IAAI,CAAC,CAAC,OAAO,EAAE;YACb,MAAM,GAAG,CAAC,CAAC,YAAY,CAAC;WACzB,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;YACnB,MAAM,GAAG,CAAC,CAAC,cAAc,CAAC;WAC3B,MAAM;YACL,MAAM,GAAG,EAAE,CAAC;WACb;UACD,MAAM;QACR,KAAK,EAAE;UACL,IAAI,CAAC,CAAC,OAAO,EAAE;YACb,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;WAC3B,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;YACnB,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;WAC7B,MAAM;YACL,MAAM,GAAG,CAAC,EAAE,CAAC;WACd;UACD,MAAM;QACR,KAAK,EAAE;UACL,IAAI,CAAC,CAAC,QAAQ,EAAE;YACd,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;WAC5B,MAAM;YACL,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;WAC7B;UACD,MAAM;QACR,KAAK,EAAE;UACL,MAAM,GAAG,CAAC,CAAC,eAAe,CAAC;UAC3B,MAAM;QACR,KAAK,EAAE;UACL,MAAM,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC;UAC5B,MAAM;QACR,KAAK,EAAE;UACL,MAAM,GAAG,CAAC,CAAC,aAAa,CAAC;UACzB,MAAM;QACR,KAAK,EAAE;UACL,MAAM,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;UAC1B,MAAM;QACR;UACE,OAAO;OACV;;MAED,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,MAAM,KAAK,CAAC,EAAE;QAC9C,OAAO;OACR;MACD,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,IAAI,MAAM,KAAK,CAAC,EAAE;QAC9C,OAAO;OACR;;MAED,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC;MAC5B,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC;MAC7B,cAAc,CAAC,CAAC,CAAC,CAAC;;MAElB,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QACxC,CAAC,CAAC,cAAc,EAAE,CAAC;OACpB;KACF,CAAC,CAAC;GACJ;;EClJD;;AAOA,EAAe,gBAAU,CAAC,EAAE;IAC1BD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;IAI1B,SAAS,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE;MAC5CA,IAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;MACvDA,IAAM,KAAK,GAAG,OAAO,CAAC,SAAS,KAAK,CAAC,CAAC;MACtCA,IAAM,QAAQ,GAAG,gBAAgB,GAAG,OAAO,CAAC,YAAY,KAAK,OAAO,CAAC,YAAY,CAAC;MAClFA,IAAM,MAAM,GAAG,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC;MACxCA,IAAM,OAAO,GAAG,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,CAAC;;MAEjFC,IAAI,SAAS,CAAC;;;MAGd,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACvC,SAAS,GAAG,KAAK,IAAI,QAAQ,CAAC;OAC/B,MAAM;QACL,SAAS,GAAG,MAAM,IAAI,OAAO,CAAC;OAC/B;;MAED,OAAO,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC;KACxD;;IAED,SAAS,iBAAiB,CAAC,CAAC,EAAE;MAC5BA,IAAI,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;MACtBA,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;;MAE3B,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;;QAElE,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC;QAClC,MAAM,GAAG,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;OAC5B;;MAED,IAAI,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,EAAE;;QAEpC,MAAM,IAAI,EAAE,CAAC;QACb,MAAM,IAAI,EAAE,CAAC;OACd;;MAED,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,mBAAmB;;QAE3D,MAAM,GAAG,CAAC,CAAC;QACX,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC;OACvB;;MAED,IAAI,CAAC,CAAC,QAAQ,EAAE;;QAEd,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;OAC3B;MACD,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACzB;;IAED,SAAS,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;;MAEvD,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,EAAE;QAC1D,OAAO,IAAI,CAAC;OACb;;MAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7B,OAAO,KAAK,CAAC;OACd;;MAEDA,IAAI,MAAM,GAAG,MAAM,CAAC;;MAEpB,OAAO,MAAM,IAAI,MAAM,KAAK,OAAO,EAAE;QACnC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;UACpD,OAAO,IAAI,CAAC;SACb;;QAEDD,IAAM,KAAK,GAAGK,GAAO,CAAC,MAAM,CAAC,CAAC;;;QAG9B,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;UACpDL,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;UAC/D,IAAI,YAAY,GAAG,CAAC,EAAE;YACpB;cACE,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;eAClC,MAAM,CAAC,SAAS,GAAG,YAAY,IAAI,MAAM,GAAG,CAAC,CAAC;cAC/C;cACA,OAAO,IAAI,CAAC;aACb;WACF;SACF;;QAED,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;UACpDA,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;UAC9D,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB;cACE,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;eACnC,MAAM,CAAC,UAAU,GAAG,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;cACjD;cACA,OAAO,IAAI,CAAC;aACb;WACF;SACF;;QAED,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;OAC5B;;MAED,OAAO,KAAK,CAAC;KACd;;IAED,SAAS,iBAAiB,CAAC,CAAC,EAAE;MAC5B,OAAsB,GAAG,iBAAiB,CAAC,CAAC;MAArC;MAAQ,oBAA+B;;MAE9C,IAAI,uBAAuB,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE;QACrD,OAAO;OACR;;MAEDC,IAAI,aAAa,GAAG,KAAK,CAAC;MAC1B,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,EAAE;;;QAGhC,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;QACpD,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;OACtD,MAAM,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;;;QAGpD,IAAI,MAAM,EAAE;UACV,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;SACrD,MAAM;UACL,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;SACrD;QACD,aAAa,GAAG,IAAI,CAAC;OACtB,MAAM,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE;;;QAGpD,IAAI,MAAM,EAAE;UACV,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;SACtD,MAAM;UACL,OAAO,CAAC,UAAU,IAAI,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;SACtD;QACD,aAAa,GAAG,IAAI,CAAC;OACtB;;MAED,cAAc,CAAC,CAAC,CAAC,CAAC;;MAElB,aAAa,GAAG,aAAa,IAAI,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;MACtE,IAAI,aAAa,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;QAC/B,CAAC,CAAC,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC,cAAc,EAAE,CAAC;OACpB;KACF;;IAED,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,WAAW,EAAE;MACzC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;KACnD,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,WAAW,EAAE;MACrD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;KACxD;GACF;;ECxJc,gBAAU,CAAC,EAAE;IAC1B,IAAI,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE;MAChD,OAAO;KACR;;IAEDD,IAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;;IAE1BA,IAAM,KAAK,GAAG;MACZ,WAAW,EAAE,EAAE;MACf,SAAS,EAAE,CAAC;MACZ,KAAK,EAAE,EAAE;MACT,UAAU,EAAE,IAAI;KACjB,CAAC;;IAEF,SAAS,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE;MACrCA,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;MAChDA,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;MACtCA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;MACpCA,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;MAEpC,IAAI,UAAU,GAAG,UAAU,EAAE;;;QAG3B;UACE,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,eAAe;WAC/D,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC;UAC/B;;UAEA,OAAO,MAAM,CAAC,OAAO,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC;SAC3D;OACF,MAAM,IAAI,UAAU,GAAG,UAAU,EAAE;;;QAGlC;UACE,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,cAAc;WAC9D,MAAM,GAAG,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC;UAChC;UACA,OAAO,IAAI,CAAC;SACb;OACF;;MAED,OAAO,IAAI,CAAC;KACb;;IAED,SAAS,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE;MAChD,OAAO,CAAC,SAAS,IAAI,WAAW,CAAC;MACjC,OAAO,CAAC,UAAU,IAAI,WAAW,CAAC;;MAElC,cAAc,CAAC,CAAC,CAAC,CAAC;KACnB;;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE;MACnB,IAAI,CAAC,CAAC,aAAa,EAAE;QACnB,OAAO,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;OAC3B;;MAED,OAAO,CAAC,CAAC;KACV;;IAED,SAAS,YAAY,CAAC,CAAC,EAAE;MACvB,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,UAAU,EAAE;QAC1D,OAAO,KAAK,CAAC;OACd;MACD,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,EAAE;QAC/D,OAAO,KAAK,CAAC;OACd;MACD,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;QACnD,OAAO,IAAI,CAAC;OACb;MACD,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,oBAAoB,EAAE;QAC1F,OAAO,IAAI,CAAC;OACb;MACD,OAAO,KAAK,CAAC;KACd;;IAED,SAAS,UAAU,CAAC,CAAC,EAAE;MACrB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QACpB,OAAO;OACR;;MAEDA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAE1B,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;MACtC,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;;MAEtC,KAAK,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;;MAEvC,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE;QAC7B,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;OACjC;KACF;;IAED,SAAS,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;MACvD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC7B,OAAO,KAAK,CAAC;OACd;;MAEDC,IAAI,MAAM,GAAG,MAAM,CAAC;;MAEpB,OAAO,MAAM,IAAI,MAAM,KAAK,OAAO,EAAE;QACnC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;UACpD,OAAO,IAAI,CAAC;SACb;;QAEDD,IAAM,KAAK,GAAGK,GAAO,CAAC,MAAM,CAAC,CAAC;;;QAG9B,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;UACpDL,IAAM,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;UAC/D,IAAI,YAAY,GAAG,CAAC,EAAE;YACpB;cACE,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;eAClC,MAAM,CAAC,SAAS,GAAG,YAAY,IAAI,MAAM,GAAG,CAAC,CAAC;cAC/C;cACA,OAAO,IAAI,CAAC;aACb;WACF;SACF;;QAED,IAAI,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;UACpDA,IAAM,aAAa,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;UAC9D,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB;cACE,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC;eACnC,MAAM,CAAC,UAAU,GAAG,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;cACjD;cACA,OAAO,IAAI,CAAC;aACb;WACF;SACF;;QAED,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;OAC5B;;MAED,OAAO,KAAK,CAAC;KACd;;IAED,SAAS,SAAS,CAAC,CAAC,EAAE;MACpB,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;QACnBA,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAE1BA,IAAM,aAAa,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC;;QAEjEA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;QAClEA,IAAM,WAAW,GAAG,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;;QAElE,IAAI,uBAAuB,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE;UAC/D,OAAO;SACR;;QAED,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QACzC,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC;;QAElCA,IAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;;QAEzCA,IAAM,OAAO,GAAG,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;QAC9C,IAAI,OAAO,GAAG,CAAC,EAAE;UACf,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,OAAO,CAAC;UACtC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,WAAW,GAAG,OAAO,CAAC;UACtC,KAAK,CAAC,SAAS,GAAG,WAAW,CAAC;SAC/B;;QAED,IAAI,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;;UAE3C,IAAI,CAAC,CAAC,UAAU,EAAE;YAChB,CAAC,CAAC,cAAc,EAAE,CAAC;WACpB;SACF;OACF;KACF;;IAED,SAAS,QAAQ,GAAG;MAClB,IAAI,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE;QAC1B,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAChC,KAAK,CAAC,UAAU,GAAG,WAAW,aAAI;UAChC,IAAI,CAAC,CAAC,aAAa,EAAE;YACnB,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAChC,OAAO;WACR;;UAED,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YACpC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAChC,OAAO;WACR;;UAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;YACpE,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAChC,OAAO;WACR;;UAED,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;;UAEvD,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC;UACrB,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC;SACtB,EAAE,EAAE,CAAC,CAAC;OACR;KACF;;IAED,IAAI,GAAG,CAAC,aAAa,EAAE;MACrB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;MAChD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;MAC9C,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;KAC7C,MAAM,IAAI,GAAG,CAAC,iBAAiB,EAAE;MAChC,IAAI,MAAM,CAAC,YAAY,EAAE;QACvB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;OAC9C,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE;QAChC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QACnD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;OAChD;KACF;GACF;;EC1ND;;EAgBAA,IAAM,eAAe,eAAM,UAAI;IAC7B,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;IACpE,kBAAkB,EAAE,IAAI;IACxB,kBAAkB,EAAE,IAAI;IACxB,kBAAkB,EAAE,IAAI;IACxB,mBAAmB,EAAE,CAAC;IACtB,mBAAmB,EAAE,CAAC;IACtB,eAAe,EAAE,KAAK;IACtB,eAAe,EAAE,KAAK;IACtB,WAAW,EAAE,IAAI;IACjB,gBAAgB,EAAE,KAAK;IACvB,gBAAgB,EAAE,IAAI;IACtB,UAAU,EAAE,CAAC;GACd,IAAC,CAAC;;EAEHA,IAAM,QAAQ,GAAG;IACf,YAAY,EAAE,SAAS;IACvB,YAAY,EAAES,mBAAS;cACvB,QAAQ;WACR,KAAK;WACL,KAAK;GACN,CAAC;;EAEF,IAAqB,gBAAgB,GACnC,yBAAW,CAAC,OAAO,EAAE,YAAiB,EAAE;;+CAAP,GAAG;AAAK;IACvC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;MACjC,OAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;KAC3C;;IAEH,IAAM,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;MACjC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;;IAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;;IAEzB,OAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;IAEhC,IAAI,CAAC,QAAQ,GAAG,eAAe,EAAE,CAAC;IAClC,KAAKT,IAAM,GAAG,IAAI,YAAY,EAAE;MAChC,IAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;KACxC;;IAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;;IAE1BA,IAAM,KAAK,eAAM,SAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAC,CAAC;IAC3DA,IAAM,IAAI,eAAM,SAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,IAAC,CAAC;;IAE7D,IAAI,CAAC,KAAK,GAAGK,GAAO,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC;IAClD,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;MACzB,OAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KAChC;IACD,IAAI,CAAC,gBAAgB,GAAG,aAAI;MAC1BL,IAAM,kBAAkB,GAAG,OAAO,CAAC,UAAU,CAAC;MAC9CC,IAAI,MAAM,GAAG,IAAI,CAAC;MAClB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;MACxB,MAAM,GAAG,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;MAChC,OAAO,CAAC,UAAU,GAAG,kBAAkB,CAAC;MAC1C,OAAS,MAAM,CAAC;KACf,GAAG,CAAC;IACL,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gBAAgB;QACjD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW;QACzC,CAAC,CAAC;IACN,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;IAClC,IAAM,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;;IAEvD,IAAI,CAAC,cAAc,GAAGS,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,OAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,IAAI,CAAC,UAAU,GAAGA,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,IAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,IAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3BV,IAAM,UAAU,GAAGK,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAChD,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACxD,IAAI,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;MAChC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;MACvC,IAAM,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KAC5C,MAAM;MACL,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;KACrC;IACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;;IAE/FG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACnD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACrFA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;IAEvB,IAAI,CAAC,cAAc,GAAGE,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,OAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,IAAI,CAAC,UAAU,GAAGA,GAAO,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,IAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,IAAM,CAAC,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACjD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC7B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC1BV,IAAM,UAAU,GAAGK,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAChD,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACtD,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;MAC/B,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;MACtC,IAAM,CAAC,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KAC9C,MAAM;MACL,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;KACpC;IACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;IAC5E,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC/FG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACnD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;IACtFA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACxB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;;IAEzB,IAAM,CAAC,KAAK,GAAG;MACX,CAAC;QACC,OAAO,CAAC,UAAU,IAAI,CAAC;YACnB,OAAO;YACP,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc;YAC7D,KAAK;YACL,IAAI;MACV,CAAC;QACC,OAAO,CAAC,SAAS,IAAI,CAAC;YAClB,OAAO;YACP,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe;YAC9D,KAAK;YACL,IAAI;KACX,CAAC;;IAEF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;IAEtB,IAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,WAAE,WAAW,EAAE,SAAG,QAAQ,CAAC,WAAW,CAAC,CAACN,MAAI,IAAC,CAAC,CAAC;;IAE7E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACnD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,UAAU,CAAC;IAC3C,IAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,YAAG,CAAC,EAAE,SAAGA,MAAI,CAAC,QAAQ,CAAC,CAAC,IAAC,CAAC,CAAC;IACjE,cAAc,CAAC,IAAI,CAAC,CAAC;EACvB,EAAC;;EAEH,2BAAE,4BAAS;IACP,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,OAAO;KACR;;;IAGD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gBAAgB;QACjD,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QACnD,CAAC,CAAC;;;IAGNM,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACnDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IACrD,IAAM,CAAC,gBAAgB;MACnB,KAAK,CAACH,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;MAC9C,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;IACpD,IAAM,CAAC,iBAAiB;MACpB,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC;MAC7C,KAAK,CAACA,GAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC,CAAC;;;IAGnDG,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;IAClDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;;IAElD,cAAc,CAAC,IAAI,CAAC,CAAC;;IAErB,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/C,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;;IAEhDA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9CA,GAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;EAChD,EAAC;;EAEH,2BAAE,8BAAS,CAAC,EAAE;IACV,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,OAAO;KACR;;IAED,cAAc,CAAC,IAAI,CAAC,CAAC;IACrB,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;IAC5E,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;;IAE/E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC1D,IAAM,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;EAChD,EAAC;;EAEH,2BAAE,8BAAU;IACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;MACjB,OAAO;KACR;;IAED,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IACzBD,MAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9BA,MAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9BA,MAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClCA,MAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAChC,IAAI,CAAC,eAAe,EAAE,CAAC;;;IAGvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;;IAE3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;EACvB,EAAC;;EAEH,2BAAE,8CAAkB;IAClB,IAAM,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;OAC5C,KAAK,CAAC,GAAG,CAAC;OACV,MAAM,WAAE,IAAI,EAAE,SAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAC,CAAC;OAC9C,IAAI,CAAC,GAAG,CAAC,CAAC;EACf,CAAC;;;;;;;;"}