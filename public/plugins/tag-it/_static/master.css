@charset "UTF-8";

html, body {
    color:#333;
    background:#002F2F/*#232f2e*/;
    line-height:1.4;
    margin:0;
    padding:0;
    font-family: 'Lucida Grande', arial, sans-serif;
}
a {
    color:#636363;
    /*color:#1155bd;*/
}
a:hover {
    text-decoration:none;
}
hr {
    border: none;
    background-color: #ccc;
    height: 6px;
    margin: 1em 0;
}
em {
    font-style:italic;
}
h1,h2,h3 {
    /*color:#4f6f6c;*/
    color:#046380;
    font-family: 'Brawler', arial, sans-serif;
    font-weight: normal;
}
h1,h2,h3,h4 {
    margin:1.8em 0 .5em 0;
}
h1 {
    font-size:2.6em;
}
h2 {
    font-size:1.8em;
}
h3 {
    font-size:1.5em;
}
h4 {
    font-size:129%;
}
p {
    margin: 1.0em 0 1em 0;
}
pre {
    background:#eee;
    border:1px solid #ccc;
    font-size:100%;
    overflow:auto;
    margin:0 0 20px;
    padding:20px;
}
code {
    font-size:100%;
    margin:0;
    padding:0;
}
#wrapper ul, #wrapper li {
    list-style:disc;
}
div#wrapper {
    background:#fff;
    width:560px;
    border:10px solid #0f1616;
    border-width:0 10px 10px;
    margin:0 auto;
    padding:15px 20px;
}
div#header {
    position:relative;
    border-bottom:1px dotted;
    margin:0 0 10px;
    padding:0 0 4px;
}
ul#nav {
    position:absolute;
    top:.7em;
    right:-7px;
    list-style:none;
    margin:0;
    padding:0;
}
ul#nav li {
    display:inline;
    padding:0 0 0 2px;
    font-size: 1.2em;
    font-weight: bold;
}
ul#nav a, .highlighted {
    background-color:#FFF8AC;
    padding: .3em .4em;
}
ul#nav a {
    padding: .4em .5em;
}
#twitter {
    position:absolute;
    top:3.1em;
    right:0;
    width:106px;
    padding: .6em .6em .4em .65em;
}
#footer {
    border-top:1px dotted;
    margin:40px 0 0;
    padding:10px 0 0;
    font-size: .8em;
}
.left {
    float:left;
}
.right {
    float:right;
}
.clear {
    clear:both;
}
.multiselect {
    width:460px;
    height:200px;
}
#switcher {
    margin-top:20px;
}
strong,h1,h4,h5,h6 {
    font-weight:bold;
}
#header p,#header h1,form {
    margin:0;
    padding:0;
}
#header h1 {
    margin-bottom: .2em;
}
.weak, .weak a, .weak a:visited {
    color: gray;
}

hr + h2, hr + h3 {
    margin-top: .5em;
}
form + hr, p + hr {
    margin-top: 2em;
}

label {
    float: left;
    width: 38px;
    margin-right: 1em;
    margin-bottom:.5em;
    font-size: 1.2em;
}
ul.tagit {
    width: 495px;
}

#tag-icon {
    float: left;
    margin-right: 1.4em;
    position: relative;
    top: .1em;
}
#title-jquery, #title-tag-it {
    display: block;
}
#title-tag-it {
    font-size: 1.2em;
}
#title-jquery {
    font-size: .75em;
    font-weight: normal;
    position: relative;
    left: .2em;
    top: .1em;
}
#feature-list {
    margin-top:2.5em;
}

#disqus-container {
    margin-top: 2em;
}
#disqus-container h3 {
    font-size: 1em;
    font-family: inherit;
}

